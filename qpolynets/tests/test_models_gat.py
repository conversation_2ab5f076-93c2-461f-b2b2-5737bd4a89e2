import jax
import jax.numpy as jnp
from flax import nnx

from qpolynets.models import SpinGAT, MolecularGAT


def test_spingat_forward_scalar():
    m = SpinGAT(n_sites=6, hidden_features=(8, 4), n_heads=(2, 1), rngs=nnx.Rngs(0))
    x = jnp.ones(6)
    y = m(x)
    assert y.shape == ()  # scalar


def test_moleculargat_forward_scalar():
    m = MolecularGAT(n_atoms=3, n_orbitals=6, hidden_features=(8, 4), n_heads=(2, 1), rngs=nnx.Rngs(0))
    x = jnp.ones(6)
    y = m(x)
    assert y.shape == ()  # scalar

