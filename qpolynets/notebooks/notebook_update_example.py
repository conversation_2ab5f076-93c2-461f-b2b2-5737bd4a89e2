"""
Example of how to update your existing notebook to use enhanced configurations

This shows how to modify your training configurations to take advantage of:
1. Per-component configurations (EN, EE, EEN)
2. Configurable activation functions
3. Physics-informed presets
4. Rank analysis capabilities

Copy these configurations into your notebook to replace the existing training_configs.
"""

# =============================================================================
# UPDATED TRAINING CONFIGURATIONS FOR YOUR NOTEBOOK
# =============================================================================

# Replace your existing training_configs with this enhanced version:

training_configs = [
    # Keep your existing neural network configuration
    ("Neural Network", "neural", {
        "layer_widths": NEURAL_LAYERS
    }),
    
    # FIXED: Your original polynomial configuration with rank=1 issue resolved
    ("Polynomial CP Multi-layer Fixed", "polynomial", {
        "degree": [2, 2],
        "rank": [2, 2],  # Changed from [1, 1] to fix the error
        "cp_variant": ['CP', 'CP'],
        "activation_scale": 0.1,
        "use_final_activation": True,
        "hidden_dims": [2],  # Changed from [1] to avoid bottleneck
        "use_activation_between_layers": False
    }),
    
    # Keep your sparse configuration
    ("Polynomial CP sparse", "polynomial", {
        "degree": 4,
        "rank": 8,
        "cp_variant": 'CP_sparse_degree_LU',
        "activation_scale": 0.1,
        "use_final_activation": True,
        "use_activation_between_layers": False
    }),
    
    # NEW: Per-component optimized configuration
    ("Polynomial Optimized Components", "polynomial", {
        "en_config": {
            "degree": 2, "rank": 3, "cp_variant": "CP",
            "activation_between_layers": "tanh", "final_activation": "tanh"
        },
        "ee_config": {
            "degree": 3, "rank": 4, "cp_variant": "CP",
            "activation_between_layers": "relu", "final_activation": "tanh"
        },
        "een_config": {
            "degree": 4, "rank": 6, "cp_variant": "CP_sparse_LU",
            "activation_between_layers": "gelu", "final_activation": "tanh"
        },
        "activation_scale": 0.1,
        "use_final_activation": True
    }),
    
    # NEW: Individual config preset (automatically optimized for simple molecules)
    ("Polynomial Individual Preset", "polynomial", {
        "individual_preset": "simple_molecule",
        "activation_scale": 0.05,  # Slightly more conservative
        "use_final_activation": True
    }),
]

# =============================================================================
# RANK ANALYSIS CONFIGURATIONS (for your convergence study)
# =============================================================================

# Use these configurations to study rank convergence:
rank_analysis_configs = [
    ("Rank 1 Analysis", "polynomial", {
        "individual_preset": "rank_analysis",
        "en_config": {"rank": 1, "cp_variant": "CP"},  # Use dense CP for rank=1
        "ee_config": {"rank": 1, "cp_variant": "CP"},
        "een_config": {"rank": 1, "cp_variant": "CP"},
        "activation_scale": 0.1
    }),
    ("Rank 2 Analysis", "polynomial", {
        "individual_preset": "rank_analysis",
        "en_config": {"rank": 2},
        "ee_config": {"rank": 2},
        "een_config": {"rank": 2},
        "activation_scale": 0.1
    }),
    ("Rank 3 Analysis", "polynomial", {
        "individual_preset": "rank_analysis",
        "en_config": {"rank": 3},
        "ee_config": {"rank": 3},
        "een_config": {"rank": 3},
        "activation_scale": 0.1
    }),
    ("Rank 4 Analysis", "polynomial", {
        "individual_preset": "rank_analysis",
        "en_config": {"rank": 4},
        "ee_config": {"rank": 4},
        "een_config": {"rank": 4},
        "activation_scale": 0.1
    }),
    ("Rank 6 Analysis", "polynomial", {
        "individual_preset": "rank_analysis",
        "en_config": {"rank": 6},
        "ee_config": {"rank": 6},
        "een_config": {"rank": 6},
        "activation_scale": 0.1
    }),
    ("Rank 8 Analysis", "polynomial", {
        "individual_preset": "rank_analysis",
        "en_config": {"rank": 8},
        "ee_config": {"rank": 8},
        "een_config": {"rank": 8},
        "activation_scale": 0.1
    })
]

# =============================================================================
# ACTIVATION FUNCTION COMPARISON CONFIGURATIONS
# =============================================================================

# Use these to test different activation functions:
activation_comparison_configs = [
    ("Tanh Activations", "polynomial", {
        "degree": 3, "rank": 6, "cp_variant": "CP",
        "activation_between_layers": "tanh",
        "final_activation": "tanh",
        "activation_scale": 0.1
    }),
    ("ReLU Activations", "polynomial", {
        "degree": 3, "rank": 6, "cp_variant": "CP",
        "activation_between_layers": "relu",
        "final_activation": "tanh",
        "activation_scale": 0.1
    }),
    ("GELU Activations", "polynomial", {
        "degree": 3, "rank": 6, "cp_variant": "CP",
        "activation_between_layers": "gelu",
        "final_activation": "tanh",
        "activation_scale": 0.1
    }),
    ("Swish Activations", "polynomial", {
        "degree": 3, "rank": 6, "cp_variant": "CP",
        "activation_between_layers": "swish",
        "final_activation": "swish",
        "activation_scale": 0.1
    })
]

# =============================================================================
# OPTIMIZER COMPARISON CONFIGURATIONS
# =============================================================================

# Use these to test different optimizers:
optimizer_comparison_configs = [
    ("Adam Optimizer", "polynomial", {
        "degree": 3, "rank": 6, "cp_variant": "CP",
        "activation_scale": 0.1
    }),
    ("SGD Optimizer", "polynomial", {
        "degree": 3, "rank": 6, "cp_variant": "CP",
        "activation_scale": 0.1
    }),
    ("RMSprop Optimizer", "polynomial", {
        "degree": 3, "rank": 6, "cp_variant": "CP",
        "activation_scale": 0.1
    }),
    ("Lion Optimizer", "polynomial", {
        "degree": 3, "rank": 6, "cp_variant": "CP",
        "activation_scale": 0.1
    })
]

# =============================================================================
# HOW TO USE IN YOUR NOTEBOOK
# =============================================================================

"""
To use these configurations in your notebook:

1. Replace your existing training_configs with the enhanced version above

2. For rank analysis, use:
   results = train_and_compare(rank_analysis_configs, mol, mf, fci_energy,
                              opt_n_steps=500, learning_rate=0.0001)

3. For activation comparison, use:
   results = train_and_compare(activation_comparison_configs, mol, mf, fci_energy,
                              opt_n_steps=500, learning_rate=0.0001)

4. For optimizer comparison, use:
   # Adam (default)
   adam_results = train_and_compare(optimizer_comparison_configs, mol, mf, fci_energy,
                                   optimizer_type="adam", learning_rate=0.0003)

   # SGD (needs higher learning rate)
   sgd_results = train_and_compare(optimizer_comparison_configs, mol, mf, fci_energy,
                                  optimizer_type="sgd", learning_rate=0.001)

   # RMSprop (with custom parameters)
   rmsprop_results = train_and_compare(optimizer_comparison_configs, mol, mf, fci_energy,
                                      optimizer_type="rmsprop", learning_rate=0.0005,
                                      opt_kwargs={"decay": 0.9, "eps": 1e-6})

   # Lion (needs smaller learning rate)
   lion_results = train_and_compare(optimizer_comparison_configs, mol, mf, fci_energy,
                                   optimizer_type="lion", learning_rate=0.0001,
                                   opt_kwargs={"b1": 0.9, "b2": 0.99})

5. The enhanced configurations will automatically:
   - Use appropriate complexity for each interaction type (EN, EE, EEN)
   - Apply the specified activation functions
   - Handle rank=1 cases correctly (using dense CP variants)
   - Provide individual config defaults

6. You can mix and match configurations:
   combined_configs = training_configs + rank_analysis_configs[:3]  # First 3 ranks
   results = train_and_compare(combined_configs, mol, mf, fci_energy)
"""

# =============================================================================
# EXAMPLE NOTEBOOK CELL
# =============================================================================

example_notebook_cell = '''
# Enhanced Training with New Configuration System
from model_analysis import train_and_compare
from notebook_update_example import training_configs, rank_analysis_configs, optimizer_comparison_configs

# Run enhanced training configurations
print("=== ENHANCED TRAINING CONFIGURATIONS ===")
enhanced_results = train_and_compare(
    training_configs,
    mol, mf, fci_energy,
    opt_n_steps=N_OPT_STEPS,
    learning_rate=LEARNING_RATE,
    opt_n_walkers=32,
    eval_n_walkers=64
)

# Run rank convergence analysis
print("\\n=== RANK CONVERGENCE ANALYSIS ===")
rank_results = train_and_compare(
    rank_analysis_configs,
    mol, mf, fci_energy,
    opt_n_steps=500,  # Shorter for analysis
    learning_rate=0.0001,  # More conservative
    opt_n_walkers=32,
    eval_n_walkers=64
)

# Run optimizer comparison
print("\\n=== OPTIMIZER COMPARISON ===")

# Test different optimizers
adam_results = train_and_compare(
    optimizer_comparison_configs,
    mol, mf, fci_energy,
    optimizer_type="adam",
    learning_rate=0.0003,
    opt_n_steps=300,
    opt_n_walkers=32,
    eval_n_walkers=64
)

sgd_results = train_and_compare(
    optimizer_comparison_configs,
    mol, mf, fci_energy,
    optimizer_type="sgd",
    learning_rate=0.001,  # Higher LR for SGD
    opt_n_steps=300,
    opt_n_walkers=32,
    eval_n_walkers=64
)

rmsprop_results = train_and_compare(
    optimizer_comparison_configs,
    mol, mf, fci_energy,
    optimizer_type="rmsprop",
    learning_rate=0.0005,
    opt_kwargs={"decay": 0.9, "eps": 1e-6},
    opt_n_steps=300,
    opt_n_walkers=32,
    eval_n_walkers=64
)

# Analyze rank convergence
ranks = [1, 2, 3, 4, 6, 8]
energies = [rank_results[f"Rank {r} Analysis"]["final_energy"] for r in ranks]
errors = [rank_results[f"Rank {r} Analysis"]["final_error"] for r in ranks]

plt.figure(figsize=(12, 5))
plt.subplot(1, 2, 1)
plt.plot(ranks, energies, 'o-', label='Final Energy')
plt.axhline(fci_energy, color='red', linestyle='--', label='FCI Energy')
plt.xlabel('Rank')
plt.ylabel('Energy (Hartree)')
plt.title('Energy vs Rank')
plt.legend()
plt.grid(True)

plt.subplot(1, 2, 2)
plt.semilogy(ranks, errors, 'o-', label='Final Error')
plt.xlabel('Rank')
plt.ylabel('Error (Hartree)')
plt.title('Error vs Rank')
plt.legend()
plt.grid(True)
plt.tight_layout()
plt.show()
'''

print("Notebook Update Example")
print("=" * 50)
print("Copy the configurations above into your notebook to use the enhanced features.")
print("\nKey improvements:")
print("✓ Fixed rank=1 multi-layer issue")
print("✓ Per-component configurations (EN, EE, EEN)")
print("✓ Configurable activation functions")
print("✓ Physics-informed presets")
print("✓ Rank convergence analysis support")
print("\nSee example_notebook_cell for complete usage example.")
