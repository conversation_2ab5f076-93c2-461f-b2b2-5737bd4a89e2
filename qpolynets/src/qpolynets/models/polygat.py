"""
Polynomial GAT (minimal): attention-weighted operator + polynomial recursion
"""
from typing import Callable
import jax
import jax.numpy as jnp
from flax import nnx
from netket.utils.types import Array, DType


class PolyGATBlock(nnx.Module):
    def __init__(
        self,
        in_features: int,
        out_features: int,
        degree: int = 2,
        basis: str = "monomial",
        use_cp_weighted: bool = False,
        rank: int | None = None,
        activation: Callable = nnx.elu,
        param_dtype: DType = jnp.float64,
        *,
        rngs: nnx.Rngs,
    ):
        self.in_features = in_features
        self.out_features = out_features
        self.degree = degree
        self.basis = basis
        self.use_cp_weighted = use_cp_weighted
        self.rank = rank
        self.activation = activation
        self.param_dtype = param_dtype

        k1, k2, k3, k4 = jax.random.split(rngs.params(), 4)
        proj_out = rank if (use_cp_weighted and rank is not None) else out_features
        self.linear = nnx.Linear(in_features=in_features, out_features=proj_out, dtype=param_dtype, rngs=nnx.Rngs(k1))
        self.attention_self = nnx.Param(jax.random.normal(k2, (proj_out, 1), dtype=param_dtype))
        self.attention_neighbor = nnx.Param(jax.random.normal(k3, (proj_out, 1), dtype=param_dtype))
        if use_cp_weighted:
            self.C = nnx.Linear(in_features=proj_out, out_features=out_features, dtype=param_dtype, rngs=nnx.Rngs(k4))
        else:
            self.C = None

    def __call__(self, node_features: Array, adjacency_matrix: Array) -> Array:
        h0 = self.linear(node_features)  # [N,F or rank]
        self_att = jnp.dot(h0, self.attention_self.value)
        neigh_att = jnp.dot(h0, self.attention_neighbor.value)
        logits = nnx.leaky_relu(self_att + neigh_att.T, negative_slope=0.2)
        A = nnx.softmax(jnp.where(adjacency_matrix > 0, logits, -1e9), axis=1)  # [N,N]

        if self.basis == "chebyshev":
            T0 = h0
            T1 = A @ h0
            out = T0 + T1
            for _ in range(2, self.degree + 1):
                T2 = 2 * (A @ T1) - T0
                out = out + T2
                T0, T1 = T1, T2
        else:
            Z = h0
            out = Z
            for _ in range(2, self.degree + 1):
                Z = A @ Z
                out = out + Z

        if self.C is not None:
            out = self.C(out)
        return out  # no intermediate activations (polynomial-first)

