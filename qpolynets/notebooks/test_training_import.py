#!/usr/bin/env python3
"""
Test script to verify that the training function can be imported and used.
"""

import sys
import os

# Add PyTC to path
pytc_path = os.path.abspath('../../repos/pytc')
if pytc_path not in sys.path:
    sys.path.insert(0, pytc_path)

# Test import
try:
    from model_analysis import train_and_compare, create_full_ansatz
    print("✅ Successfully imported training functions from model_analysis.py")
    
    # Test function signatures
    import inspect
    
    # Check train_and_compare signature
    sig = inspect.signature(train_and_compare)
    print(f"✅ train_and_compare signature: {sig}")
    
    # Check create_full_ansatz signature  
    sig2 = inspect.signature(create_full_ansatz)
    print(f"✅ create_full_ansatz signature: {sig2}")
    
    print("\n🎉 All imports successful! The training function is ready to use.")
    
except ImportError as e:
    print(f"❌ Import failed: {e}")
    print("Make sure PyTC is properly installed and accessible.")
    
except Exception as e:
    print(f"❌ Unexpected error: {e}")
    import traceback
    traceback.print_exc()

print("\n" + "="*60)
print("USAGE EXAMPLE:")
print("="*60)
print("""
# In your notebook, you can now use:

from model_analysis import train_and_compare

# Define training configurations
configs = [
    ("Neural Network", "neural", {"layer_widths": [8, 8]}),
    ("Polynomial CP", "polynomial", {
        "degree": 4, "rank": 8, "cp_variant": "CP"
    }),
    ("Polynomial CP sparse_LU", "polynomial", {
        "degree": 4, "rank": 8, "cp_variant": "CP_sparse_LU"
    })
]

# Run training
results = train_and_compare(
    mol=mol, mf=mf, fci_energy=fci_energy,
    molecule_name="H2", configs=configs,
    n_opt_steps=200, learning_rate=0.0003
)

# Access results
for name, result in results.items():
    if result is not None:
        print(f"{name}: {result['final_energy']:.6f} Ha")
""")
