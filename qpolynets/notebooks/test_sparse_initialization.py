#!/usr/bin/env python3
"""
Test script to verify that the fixed sparse initialization is working correctly.
"""

import sys
import os
import jax
import jax.numpy as jnp
from jax import random

# Enable float64 precision
jax.config.update("jax_enable_x64", True)

# Add PyTC to path
pytc_path = os.path.abspath('../../repos/pytc')
if pytc_path not in sys.path:
    sys.path.insert(0, pytc_path)

# PySCF for quantum chemistry
from pyscf import gto, scf

# PyTC imports
from pytc.autodiff.jastrow import CPJastrowEEN

def test_sparse_initialization():
    """Test that sparse variants initialize with correct sparsity patterns."""
    print("=== Testing Fixed Sparse Initialization ===")
    
    # Setup molecule
    mol = gto.M(atom='H 0 0 0; H 0 0 1.4', basis='cc-pvtz', unit='bohr', verbose=0)
    mf = scf.RHF(mol)
    mf.kernel()
    
    # Configuration
    POLY_DEGREE = 4
    POLY_RANK = 8
    
    print(f"Test configuration: degree={POLY_DEGREE}, rank={POLY_RANK}")
    
    # Test 1: Standard CP should have no masks and dense initialization
    print(f"\n=== Test 1: Standard CP ===")
    cp_standard = CPJastrowEEN(mol, degree=POLY_DEGREE, rank=POLY_RANK, cp_variant='CP')
    standard_params = cp_standard.init_params(key=random.PRNGKey(42))
    
    print(f"Has masks: {hasattr(cp_standard, 'masks') and cp_standard.masks is not None}")
    print(f"All U matrices are dense: {all(jnp.all(u != 0) for u in standard_params['U'])}")
    
    # Test 2: CP sparse_LU should have masks and sparse initialization
    print(f"\n=== Test 2: CP sparse_LU ===")
    cp_sparse_lu = CPJastrowEEN(mol, degree=POLY_DEGREE, rank=POLY_RANK, cp_variant='CP_sparse_LU')
    sparse_params = cp_sparse_lu.init_params(key=random.PRNGKey(42))
    
    print(f"Has masks: {hasattr(cp_sparse_lu, 'masks') and cp_sparse_lu.masks is not None}")
    print(f"Number of masks: {len(cp_sparse_lu.masks) if cp_sparse_lu.masks else 0}")
    
    # Check that U matrices are initialized with sparsity
    if cp_sparse_lu.masks is not None:
        print(f"\nU matrix sparsity analysis:")
        for i, (u, mask) in enumerate(zip(sparse_params['U'], cp_sparse_lu.masks)):
            # Check that zeros in U correspond to zeros in mask
            u_zeros = (u == 0)
            mask_zeros = (mask == 0)
            sparsity_matches = jnp.allclose(u_zeros, mask_zeros)
            
            total_elements = u.size
            zero_elements = jnp.sum(u == 0)
            mask_zero_elements = jnp.sum(mask == 0)
            
            print(f"  U{i}:")
            print(f"    Total elements: {total_elements}")
            print(f"    Zero elements in U: {zero_elements}")
            print(f"    Zero elements in mask: {mask_zero_elements}")
            print(f"    Sparsity pattern matches: {sparsity_matches} ✅" if sparsity_matches else f"    Sparsity pattern matches: {sparsity_matches} ❌")
            print(f"    Sparsity ratio: {zero_elements/total_elements:.3f}")
    
    # Test 3: Verify that forward pass still works correctly
    print(f"\n=== Test 3: Forward Pass Verification ===")
    
    # Create test input
    test_input = jnp.array([1.0, 2.0, 3.0, 4.0, 5.0])  # 5D input for EEN
    
    # Test standard CP forward pass
    try:
        standard_output = cp_standard._cp_forward(test_input, standard_params)
        print(f"Standard CP forward pass: ✅ (output: {standard_output})")
    except Exception as e:
        print(f"Standard CP forward pass: ❌ (error: {e})")
    
    # Test sparse CP forward pass
    try:
        sparse_output = cp_sparse_lu._cp_forward(test_input, sparse_params)
        print(f"Sparse CP forward pass: ✅ (output: {sparse_output})")
    except Exception as e:
        print(f"Sparse CP forward pass: ❌ (error: {e})")
    
    # Test 4: Verify that masks are not in parameters
    print(f"\n=== Test 4: Parameter Dictionary Verification ===")
    print(f"Standard CP params keys: {list(standard_params.keys())}")
    print(f"Sparse CP params keys: {list(sparse_params.keys())}")
    print(f"Masks in standard params: {'masks' in standard_params}")
    print(f"Masks in sparse params: {'masks' in sparse_params}")
    print(f"Masks properly excluded: {'masks' not in sparse_params} ✅" if 'masks' not in sparse_params else "Masks properly excluded: ❌")
    
    # Test 5: Compare parameter counts
    print(f"\n=== Test 5: Parameter Count Comparison ===")
    
    def count_params(params):
        total = 0
        for key, value in params.items():
            if key == 'U':
                total += sum(u.size for u in value)
            elif hasattr(value, 'size'):
                total += value.size
        return total
    
    standard_count = count_params(standard_params)
    sparse_count = count_params(sparse_params)
    
    print(f"Standard CP parameter count: {standard_count}")
    print(f"Sparse CP parameter count: {sparse_count}")
    print(f"Same parameter count: {standard_count == sparse_count} ✅" if standard_count == sparse_count else f"Same parameter count: {standard_count == sparse_count} ❌")
    
    # Test 6: Verify sparsity is maintained after forward pass masking
    print(f"\n=== Test 6: Forward Pass Masking Verification ===")
    
    # Apply forward pass masking manually
    U_after_masking = cp_sparse_lu._apply_sparsity_masks(sparse_params['U'], cp_sparse_lu.masks)
    
    # Check that the result is the same as the initialized sparse parameters
    masking_preserves_sparsity = all(
        jnp.allclose(u_masked, u_init) 
        for u_masked, u_init in zip(U_after_masking, sparse_params['U'])
    )
    
    print(f"Forward pass masking preserves initialization: {masking_preserves_sparsity} ✅" if masking_preserves_sparsity else f"Forward pass masking preserves initialization: {masking_preserves_sparsity} ❌")
    
    # Summary
    print(f"\n" + "="*60)
    print(f"SUMMARY")
    print(f"="*60)
    
    tests_passed = [
        not (hasattr(cp_standard, 'masks') and cp_standard.masks is not None),  # Standard has no masks
        hasattr(cp_sparse_lu, 'masks') and cp_sparse_lu.masks is not None,  # Sparse has masks
        'masks' not in sparse_params,  # Masks not in params
        standard_count == sparse_count,  # Same storage count
        masking_preserves_sparsity  # Sparsity preserved
    ]
    
    print(f"✅ Standard CP has no masks:           {tests_passed[0]}")
    print(f"✅ Sparse CP has masks:               {tests_passed[1]}")
    print(f"✅ Masks excluded from parameters:    {tests_passed[2]}")
    print(f"✅ Same parameter storage count:      {tests_passed[3]}")
    print(f"✅ Sparsity preserved in forward:     {tests_passed[4]}")
    
    all_passed = all(tests_passed)
    print(f"\n🎉 All tests passed: {all_passed}")
    
    if all_passed:
        print(f"✅ Sparse initialization is working correctly!")
        print(f"✅ Parameters start sparse and remain sparse during computation")
        print(f"✅ No redundant operations - masks are applied once during init")
    else:
        print(f"⚠️  Some tests failed. Check the implementation.")
    
    return all_passed

if __name__ == "__main__":
    test_sparse_initialization()
