"""
Unified polynomial Jastrow factors using CP decomposition.

This module implements polynomial Jastrow factors that use Canonical Polyadic (CP)
decomposition to represent electron correlations through polynomial recursion
without intermediate activations.
"""
from typing import Dict, Any, Optional, List
import jax
import jax.numpy as jnp
from functools import partial

from .jastrow import Jastrow


class PolynomialJastrow(Jastrow):
    """Base class for CP-based Jastrow factors with configurable polynomial variants.
    
    Supports multiple CP variants:
    - 'CP': Standard CP decomposition
    - 'CP_sparse_LU': Alternating upper/lower triangular sparsity
    - 'CP_sparse_degree': Degree-dependent sparsity patterns
    """
    
    def __init__(self, mol, degree: int = 3, rank: int = 8, 
                 cp_variant: str = 'CP', use_final_activation: bool = True,
                 activation_scale: float = 0.1, name: Optional[str] = None):
        super().__init__(name=name)
        self.mol = mol
        self.degree = degree
        self.rank = rank
        self.cp_variant = cp_variant
        self.use_final_activation = use_final_activation
        self.activation_scale = activation_scale
        self.input_dim = None  # To be set by subclasses
        
    def _safe_norm(self, x):
        """Compute norm with epsilon to prevent division by zero."""
        return jnp.sqrt(jnp.sum(x * x, axis=-1) + 1e-12)
    
    def _cp_forward(self, x: jnp.ndarray, params: Dict[str, Any]) -> jnp.ndarray:
        """Core CP recursion logic extracted from qpolynets.
        
        Implements: out = U1*x; for k=2..D: out = (Uk*x) * out + out
        """
        U = params['U']
        C = params['C']
        bias = params.get('bias', jnp.array(0.0))
        
        # Apply sparsity masks if needed
        if self.cp_variant != 'CP' and 'masks' in params:
            U = self._apply_sparsity_masks(U, params['masks'])
        
        # Standard CP recursion
        out = x @ U[0]  # (rank,)
        for k in range(1, len(U)):
            ukx = x @ U[k]
            out = ukx * out + out
            
        raw = jnp.dot(out, C) + bias
        
        # Optional final activation for stability
        if self.use_final_activation:
            return self.activation_scale * jnp.tanh(raw)
        return raw
        
    def _apply_sparsity_masks(self, U: List[jnp.ndarray], 
                             masks: List[jnp.ndarray]) -> List[jnp.ndarray]:
        """Apply sparsity patterns for sparse CP variants."""
        return [U[i] * masks[i] for i in range(len(U))]
    
    def _generate_lu_masks(self) -> List[jnp.ndarray]:
        """Generate alternating upper/lower triangular masks for CP_sparse_LU."""
        mask1 = jnp.triu(jnp.ones((self.rank, self.input_dim)))
        mask2 = jnp.tril(jnp.ones((self.rank, self.input_dim)))
        masks = []
        for i in range(self.degree):
            if i % 2 == 0:
                masks.append(mask1.T)
            else:
                masks.append(mask2.T)
        return masks
    
    def _generate_degree_masks(self, key: jax.Array) -> List[jnp.ndarray]:
        """Generate degree-dependent sparsity masks for CP_sparse_degree."""
        masks = []
        keys = jax.random.split(key, self.degree)
        for i in range(self.degree):
            sparsity_ratio = 1.0 - i * 0.1  # Decreasing sparsity with degree
            mask = jax.random.bernoulli(keys[i], sparsity_ratio, 
                                      (self.rank, self.input_dim))
            masks.append(mask.T)
        return masks
    
    def init_params(self, key: Optional[jax.Array] = None, **kwargs) -> Dict[str, Any]:
        """Initialize parameters compatible with PyTC's functional approach."""
        if key is None:
            key = jax.random.PRNGKey(0)
            
        if self.input_dim is None:
            raise ValueError("input_dim must be set by subclass before calling init_params")
            
        keys = jax.random.split(key, self.degree + 3)
        
        # Core CP parameters (JAX arrays, not nnx modules)
        U = [jax.random.normal(keys[i], (self.input_dim, self.rank)) * 0.1 
             for i in range(self.degree)]
        C = jax.random.normal(keys[-2], (self.rank,)) * 0.1
        bias = jnp.array(0.0)
        
        params = {
            'degree': self.degree,
            'U': U,
            'C': C, 
            'bias': bias
        }
        
        # Add sparsity masks for sparse variants
        if self.cp_variant == 'CP_sparse_LU':
            params['masks'] = self._generate_lu_masks()
        elif self.cp_variant == 'CP_sparse_degree':
            params['masks'] = self._generate_degree_masks(keys[-1])
            
        return params
    
    def _features(self, r1: jnp.ndarray, r2: jnp.ndarray, 
                 params: Dict[str, Any]) -> jnp.ndarray:
        """Extract features for polynomial evaluation. To be implemented by subclasses."""
        raise NotImplementedError("Subclasses must implement _features method")
    
    @partial(jax.jit, static_argnums=(0,))
    def _compute(self, r1, r2, params):
        """Compute polynomial Jastrow exponent."""
        x = self._features(r1, r2, params)
        return self._cp_forward(x, params)


class CPJastrowEN(PolynomialJastrow):
    """CP polynomial for electron-nuclear correlations.
    
    Features: distances from electron to all nuclei
    Mathematical form: u_EN(r1) = CP(|r1 - R_A|) for all nuclei A
    """
    
    def __init__(self, mol, **kwargs):
        super().__init__(mol, **kwargs)
        self.nuclear_pos = jnp.array(mol.atom_coords())
        self.input_dim = len(self.nuclear_pos)
        
    def _features(self, r1: jnp.ndarray, r2: jnp.ndarray, 
                 params: Dict[str, Any]) -> jnp.ndarray:
        """EN features: distances from electron to nuclei."""
        r1n_dist = self._safe_norm(r1[None, :] - self.nuclear_pos)
        return r1n_dist
        
    @partial(jax.jit, static_argnums=(0,))
    def _compute(self, r1, r2, params):
        x = self._features(r1, r2, params)
        result = self._cp_forward(x, params)
        return result / (self.mol.nelectron - 1)  # Normalization
    
    def get_log_grads_r2(self, r1, r2, params):
        """EN correlation is symmetric in electron exchange."""
        return self.get_log_grads_r1(r2, r1, params)


class CPJastrowEE(PolynomialJastrow):
    """CP polynomial for electron-electron correlations.
    
    Features: inter-electron distance
    Mathematical form: u_EE(r1, r2) = CP(|r1 - r2|)
    """
    
    def __init__(self, mol, **kwargs):
        super().__init__(mol, **kwargs)
        self.input_dim = 1
        
    def _features(self, r1: jnp.ndarray, r2: jnp.ndarray, 
                 params: Dict[str, Any]) -> jnp.ndarray:
        """EE features: inter-electron distance."""
        r12_dist = self._safe_norm(r1 - r2)
        return jnp.array([r12_dist])
        
    @partial(jax.jit, static_argnums=(0,))
    def _compute(self, r1, r2, params):
        x = self._features(r1, r2, params)
        return self._cp_forward(x, params)


class CPJastrowEEN(PolynomialJastrow):
    """CP polynomial for electron-electron-nuclear correlations.
    
    Features: r12 + symmetric EN distances (min/max)
    Mathematical form: u_EEN(r1, r2) = CP(|r1-r2|, min(|r1-R_A|, |r2-R_A|), max(|r1-R_A|, |r2-R_A|))
    """
    
    def __init__(self, mol, **kwargs):
        super().__init__(mol, **kwargs)
        self.nuclear_pos = jnp.array(mol.atom_coords())
        self.input_dim = 1 + 2 * len(self.nuclear_pos)
        
    def _features(self, r1: jnp.ndarray, r2: jnp.ndarray, 
                 params: Dict[str, Any]) -> jnp.ndarray:
        """EEN features: r12 + symmetric EN distances."""
        r12 = self._safe_norm(r1 - r2)[None]
        r1n = self._safe_norm(r1[None, :] - self.nuclear_pos)
        r2n = self._safe_norm(r2[None, :] - self.nuclear_pos)
        min_d = jnp.minimum(r1n, r2n)
        max_d = jnp.maximum(r1n, r2n)
        return jnp.concatenate([r12, min_d, max_d], axis=0)
        
    @partial(jax.jit, static_argnums=(0,))
    def _compute(self, r1, r2, params):
        x = self._features(r1, r2, params)
        return self._cp_forward(x, params)
