"""
Base Graph Attention Network Architecture (ported minimal version from qgat)
"""
from abc import ABC, abstractmethod
from typing import Sequence, Callable
import jax
import jax.numpy as jnp
from flax import nnx
from netket.utils.types import Array, DType

try:
    from ..models.gat import MultiHeadGraphAttention
except Exception:
    # fallback for circular import during packaging
    MultiHeadGraphAttention = None  # type: ignore


class BaseGraphAttentionNetwork(nnx.Module, ABC):
    """Minimal nnx base for GAT models with NetKet-compatible scalar output."""

    def __init__(
        self,
        n_nodes: int,
        hidden_features: Sequence[int] = (32, 16),
        n_heads: Sequence[int] = (4, 1),
        dropout_rate: float = 0.1,
        activation: Callable = nnx.elu,
        use_residual: bool = True,
        param_dtype: DType = jnp.float64,
        *,
        rngs: nnx.Rngs,
    ):
        self.n_nodes = n_nodes
        self.hidden_features = hidden_features
        self.n_heads = n_heads
        self.dropout_rate = dropout_rate
        self.activation = activation
        self.use_residual = use_residual
        self.param_dtype = param_dtype

        assert len(hidden_features) == len(n_heads), (
            "Number of hidden features must match number of head specifications"
        )

        self._build_gat_layers(rngs)

    def _build_gat_layers(self, rngs: nnx.Rngs):
        input_size = self.get_input_size()
        key1, remaining_key = jax.random.split(rngs.params())

        self.input_embedding = nnx.Linear(
            in_features=input_size,
            out_features=self.hidden_features[0],
            dtype=self.param_dtype,
            rngs=nnx.Rngs(key1),
        )
        # Ensure param dtype consistency (some backends default to float32)
        self.input_embedding.kernel = nnx.Param(self.input_embedding.kernel.value.astype(self.param_dtype))
        self.input_embedding.bias = nnx.Param(self.input_embedding.bias.value.astype(self.param_dtype))

        # Build GAT layers
        self.gat_layers = []
        current_features = self.hidden_features[0]

        # Late import to avoid circular during module import
        from ..models.gat import MultiHeadGraphAttention as _MHGA  # type: ignore

        for i, (features, heads) in enumerate(zip(self.hidden_features, self.n_heads)):
            is_output = i == len(self.hidden_features) - 1
            layer_key, remaining_key = jax.random.split(remaining_key)
            layer = _MHGA(
                in_features=current_features,
                n_heads=heads,
                out_features=features,
                dropout_rate=self.dropout_rate,
                is_output_layer=is_output,
                activation=self.activation,
                param_dtype=self.param_dtype,
                rngs=nnx.Rngs(layer_key),
            )
            self.gat_layers.append(layer)
            current_features = features if is_output else features * heads

        self.output_projection = nnx.Linear(
            in_features=self.hidden_features[-1],
            out_features=1,
            dtype=self.param_dtype,
            rngs=nnx.Rngs(remaining_key),
        )
        self.output_projection.kernel = nnx.Param(self.output_projection.kernel.value.astype(self.param_dtype))
        self.output_projection.bias = nnx.Param(self.output_projection.bias.value.astype(self.param_dtype))
        # Ensure dtype for all head linears to avoid float32/float64 mismatch in NetKet SR
        for layer in self.gat_layers:
            for head in getattr(layer, "heads", []):
                if hasattr(head, "linear"):
                    head.linear.kernel = nnx.Param(head.linear.kernel.value.astype(self.param_dtype))
                    head.linear.bias = nnx.Param(head.linear.bias.value.astype(self.param_dtype))

    @abstractmethod
    def get_input_size(self) -> int:
        ...

    @abstractmethod
    def input_preprocessing(self, physics_state: Array) -> Array:
        ...

    @abstractmethod
    def create_adjacency(self) -> Array:
        ...

    def output_postprocessing(self, gat_output: Array) -> Array:
        return gat_output

    def __call__(self, physics_state: Array, training: bool = False) -> Array:
        # Batch handling
        original_shape = physics_state.shape
        squeeze_output = False
        if physics_state.ndim == 1:
            physics_state = jnp.expand_dims(physics_state, 0)
            squeeze_output = True
        elif physics_state.ndim > 2:
            batch_shape = original_shape[:-1]
            physics_state = physics_state.reshape(-1, original_shape[-1])
        else:
            batch_shape = original_shape[:-1]

        adjacency = self.create_adjacency()

        def process_single(state):
            node_features = self.input_preprocessing(state)
            x = self.input_embedding(node_features)
            for layer_idx, gat_layer in enumerate(self.gat_layers):
                if self.use_residual and layer_idx > 0 and x.shape[-1] == gat_layer.out_features:
                    residual = x
                    x = gat_layer(x, adjacency, training=training)
                    x = x + residual
                else:
                    x = gat_layer(x, adjacency, training=training)
            global_feature = jnp.mean(x, axis=0)
            output = self.output_projection(global_feature)
            return output.squeeze()

        result = jax.vmap(process_single)(physics_state)
        result = self.output_postprocessing(result)

        if physics_state.ndim > 2 and not squeeze_output:
            result = result.reshape(batch_shape)
        elif squeeze_output:
            result = jnp.squeeze(result)
        return result

