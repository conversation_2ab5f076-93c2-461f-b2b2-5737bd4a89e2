#!/usr/bin/env python3
"""
Test script to verify the polynomial.py changes are working.
This forces a fresh import and tests the functionality.
"""

import sys
import os
import importlib

# Enable float64 precision
import jax
jax.config.update("jax_enable_x64", True)

# Add PyTC to path
pytc_path = os.path.abspath('../../repos/pytc')
if pytc_path not in sys.path:
    sys.path.insert(0, pytc_path)

# Force reload of the module
if 'pytc.autodiff.jastrow.polynomial' in sys.modules:
    importlib.reload(sys.modules['pytc.autodiff.jastrow.polynomial'])

# PySCF for quantum chemistry
from pyscf import gto, scf
from jax import random
import jax.numpy as jnp

# PyTC imports
from pytc.autodiff.jastrow import CPJastrowEEN

def test_changes():
    """Test that the changes are working."""
    print("=== Testing Polynomial.py Changes ===")
    
    # Setup molecule
    mol = gto.M(atom='H 0 0 0; H 0 0 1.4', basis='cc-pvtz', unit='bohr', verbose=0)
    mf = scf.RHF(mol)
    mf.kernel()
    
    # Create CP sparse_LU instance
    cp_sparse_lu = CPJastrowEEN(mol, degree=4, rank=8, cp_variant='CP_sparse_LU')
    params = cp_sparse_lu.init_params(key=random.PRNGKey(42))
    
    # Test 1: Check if masks are in instance
    has_masks_in_instance = hasattr(cp_sparse_lu, 'masks') and cp_sparse_lu.masks is not None
    print(f"✅ Masks in instance: {has_masks_in_instance}")
    
    # Test 2: Check if masks are NOT in parameters
    masks_not_in_params = 'masks' not in params
    print(f"✅ Masks NOT in parameters: {masks_not_in_params}")
    
    # Test 3: Check parameter keys
    print(f"Parameter keys: {list(params.keys())}")
    
    # Test 4: Test forward pass
    try:
        test_input = jnp.array([1.0, 2.0, 3.0, 4.0, 5.0])
        result = cp_sparse_lu._cp_forward(test_input, params)
        forward_pass_works = True
        print(f"✅ Forward pass works: {forward_pass_works}")
        print(f"   Output: {result}")
    except Exception as e:
        forward_pass_works = False
        print(f"❌ Forward pass failed: {e}")
    
    # Test 5: Check mask details
    if has_masks_in_instance:
        print(f"✅ Number of masks: {len(cp_sparse_lu.masks)}")
        for i, mask in enumerate(cp_sparse_lu.masks):
            nonzero = jnp.sum(mask != 0)
            total = mask.size
            print(f"   Mask {i}: {nonzero}/{total} non-zero ({nonzero/total:.3f})")
    
    # Summary
    all_working = has_masks_in_instance and masks_not_in_params and forward_pass_works
    print(f"\n{'🎉' if all_working else '⚠️'} All changes working: {all_working}")
    
    return all_working

if __name__ == "__main__":
    test_changes()
