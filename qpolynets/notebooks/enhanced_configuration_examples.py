"""
Enhanced Configuration Examples for Polynomial Networks

This file demonstrates the new enhanced configuration capabilities:
1. Per-component configurations (EN, EE, EEN)
2. Configurable activation functions
3. Physics-informed presets
4. Rank analysis configurations

Usage:
    from enhanced_configuration_examples import *
    
    # Use in your training code:
    result = train_and_compare(enhanced_configs, mol, mf, fci_energy)
"""

import jax.numpy as jnp
import jax

# Import the enhanced model_analysis functions
from model_analysis import create_full_ansatz, train_and_compare, INDIVIDUAL_CONFIG_PRESETS

# =============================================================================
# ENHANCED CONFIGURATION EXAMPLES
# =============================================================================

# Example 1: Per-component configuration with different activation functions
per_component_config = ("Polynomial Per-Component", "polynomial", {
    "en_config": {
        "degree": 2, "rank": 3, "cp_variant": "CP",
        "activation_between_layers": "tanh", 
        "final_activation": "tanh",
        "activation_scale": 0.05
    },
    "ee_config": {
        "degree": 3, "rank": 4, "cp_variant": "CP",
        "activation_between_layers": "relu", 
        "final_activation": "tanh",
        "activation_scale": 0.1
    },
    "een_config": {
        "degree": 4, "rank": 6, "cp_variant": "CP_sparse_LU",
        "activation_between_layers": "gelu", 
        "final_activation": "swish",
        "activation_scale": 0.1
    },
    "use_final_activation": True
})

# Example 2: Individual config preset with overrides
individual_preset_config = ("Polynomial Individual Preset", "polynomial", {
    "individual_preset": "simple_molecule",
    "activation_scale": 0.05,  # Override global setting
    "use_final_activation": True,
    # Override specific components
    "een_config": {
        "rank": 8,  # Increase EEN complexity
        "cp_variant": "CP_sparse_degree_sawtooth"
    }
})

# Example 3: Rank analysis configuration (for convergence studies)
rank_analysis_configs = [
    ("Rank 1 Analysis", "polynomial", {
        "individual_preset": "rank_analysis",
        "en_config": {"rank": 1},
        "ee_config": {"rank": 1},
        "een_config": {"rank": 1},
        "activation_scale": 0.1
    }),
    ("Rank 2 Analysis", "polynomial", {
        "individual_preset": "rank_analysis",
        "en_config": {"rank": 2},
        "ee_config": {"rank": 2},
        "een_config": {"rank": 2},
        "activation_scale": 0.1
    }),
    ("Rank 4 Analysis", "polynomial", {
        "individual_preset": "rank_analysis",
        "en_config": {"rank": 4},
        "ee_config": {"rank": 4},
        "een_config": {"rank": 4},
        "activation_scale": 0.1
    }),
    ("Rank 8 Analysis", "polynomial", {
        "individual_preset": "rank_analysis",
        "en_config": {"rank": 8},
        "ee_config": {"rank": 8},
        "een_config": {"rank": 8},
        "activation_scale": 0.1
    })
]

# Example 4: Activation function comparison
activation_comparison_configs = [
    ("Tanh Activations", "polynomial", {
        "degree": 3, "rank": 6, "cp_variant": "CP",
        "activation_between_layers": "tanh",
        "final_activation": "tanh",
        "activation_scale": 0.1
    }),
    ("ReLU Activations", "polynomial", {
        "degree": 3, "rank": 6, "cp_variant": "CP",
        "activation_between_layers": "relu",
        "final_activation": "tanh",
        "activation_scale": 0.1
    }),
    ("GELU Activations", "polynomial", {
        "degree": 3, "rank": 6, "cp_variant": "CP",
        "activation_between_layers": "gelu",
        "final_activation": "tanh",
        "activation_scale": 0.1
    }),
    ("Swish Activations", "polynomial", {
        "degree": 3, "rank": 6, "cp_variant": "CP",
        "activation_between_layers": "swish",
        "final_activation": "swish",
        "activation_scale": 0.1
    })
]

# Example 5: Multi-layer with per-layer activation functions
multi_layer_config = ("Multi-layer Enhanced", "polynomial", {
    "hidden_dims": [8, 4],
    "degree": [4, 3, 2],
    "rank": [8, 6, 4],
    "cp_variant": ["CP", "CP_sparse_LU", "CP"],
    "use_activation_between_layers": True,
    "activation_between_layers": "gelu",  # Between polynomial layers
    "final_activation": "tanh",           # Final output activation
    "activation_scale": 0.1,
    "final_activation_scale": 0.05       # Different scale for final activation
})

# Example 6: Sparsity pattern comparison
sparsity_comparison_configs = [
    ("Dense CP", "polynomial", {
        "degree": 4, "rank": 8, "cp_variant": "CP",
        "activation_scale": 0.1
    }),
    ("Sparse LU", "polynomial", {
        "degree": 4, "rank": 8, "cp_variant": "CP_sparse_LU",
        "activation_scale": 0.1
    }),
    ("Sparse Degree", "polynomial", {
        "degree": 4, "rank": 8, "cp_variant": "CP_sparse_degree",
        "activation_scale": 0.1
    }),
    ("Sparse Sawtooth", "polynomial", {
        "degree": 4, "rank": 8, "cp_variant": "CP_sparse_degree_sawtooth",
        "sawtooth_l_offset": -1, "sawtooth_u_offset": 1,
        "activation_scale": 0.1
    })
]

# =============================================================================
# COMPREHENSIVE CONFIGURATION SETS
# =============================================================================

# All enhanced configurations for comprehensive testing
enhanced_configs = [
    per_component_config,
    individual_preset_config,
    multi_layer_config
]

# All configurations combined
all_enhanced_configs = (
    enhanced_configs + 
    rank_analysis_configs + 
    activation_comparison_configs + 
    sparsity_comparison_configs
)

# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

def create_rank_convergence_study(max_rank=8):
    """Create configurations for rank convergence analysis."""
    configs = []
    for rank in range(1, max_rank + 1):
        config = (f"Rank {rank} Convergence", "polynomial", {
            "physics_preset": "rank_analysis",
            "en_config": {"rank": rank},
            "ee_config": {"rank": rank},
            "een_config": {"rank": rank},
            "activation_scale": 0.1
        })
        configs.append(config)
    return configs

def create_activation_study(base_config):
    """Create configurations testing different activation functions."""
    activations = ["tanh", "relu", "gelu", "swish", "elu"]
    configs = []
    for activation in activations:
        config = (f"{activation.upper()} Activation", "polynomial", {
            **base_config,
            "activation_between_layers": activation,
            "final_activation": "tanh"  # Keep final activation consistent
        })
        configs.append(config)
    return configs

def create_component_complexity_study():
    """Create configurations with varying complexity per component."""
    configs = [
        ("Low Complexity", "polynomial", {
            "en_config": {"degree": 2, "rank": 2, "cp_variant": "CP"},
            "ee_config": {"degree": 2, "rank": 3, "cp_variant": "CP"},
            "een_config": {"degree": 3, "rank": 4, "cp_variant": "CP"}
        }),
        ("Medium Complexity", "polynomial", {
            "en_config": {"degree": 2, "rank": 4, "cp_variant": "CP"},
            "ee_config": {"degree": 3, "rank": 6, "cp_variant": "CP"},
            "een_config": {"degree": 4, "rank": 8, "cp_variant": "CP_sparse_LU"}
        }),
        ("High Complexity", "polynomial", {
            "en_config": {"degree": 3, "rank": 6, "cp_variant": "CP"},
            "ee_config": {"degree": 4, "rank": 8, "cp_variant": "CP_sparse_LU"},
            "een_config": {"degree": 5, "rank": 12, "cp_variant": "CP_sparse_degree_sawtooth"}
        })
    ]
    return configs

# =============================================================================
# OPTIMIZER COMPARISON CONFIGURATIONS
# =============================================================================

# Example 7: Optimizer comparison configurations
optimizer_comparison_configs = [
    ("Adam Optimizer", "polynomial", {
        "degree": 4, "rank": 8, "cp_variant": "CP",
        "activation_scale": 0.1
    }),
    ("SGD Optimizer", "polynomial", {
        "degree": 4, "rank": 8, "cp_variant": "CP",
        "activation_scale": 0.1
    }),
    ("RMSprop Optimizer", "polynomial", {
        "degree": 4, "rank": 8, "cp_variant": "CP",
        "activation_scale": 0.1
    }),
    ("Lion Optimizer", "polynomial", {
        "degree": 4, "rank": 8, "cp_variant": "CP",
        "activation_scale": 0.1
    })
]

def create_optimizer_study(base_config):
    """Create configurations testing different optimizers with appropriate learning rates."""
    optimizer_configs = [
        ("Adam", "adam", 0.0003, {}),
        ("SGD", "sgd", 0.001, {}),  # SGD typically needs higher learning rates
        ("RMSprop", "rmsprop", 0.0005, {"decay": 0.9, "eps": 1e-6}),
        ("Lion", "lion", 0.0001, {"b1": 0.9, "b2": 0.99})  # Lion needs smaller learning rates
    ]

    configs = []
    for opt_name, opt_type, lr, opt_kwargs in optimizer_configs:
        config = (f"{opt_name} Optimizer", "polynomial", {
            **base_config,
            "_optimizer_type": opt_type,
            "_learning_rate": lr,
            "_opt_kwargs": opt_kwargs
        })
        configs.append(config)
    return configs

# =============================================================================
# EXAMPLE USAGE
# =============================================================================

if __name__ == "__main__":
    print("Enhanced Configuration Examples")
    print("=" * 50)

    print(f"\nAvailable configuration sets:")
    print(f"  - enhanced_configs: {len(enhanced_configs)} configurations")
    print(f"  - rank_analysis_configs: {len(rank_analysis_configs)} configurations")
    print(f"  - activation_comparison_configs: {len(activation_comparison_configs)} configurations")
    print(f"  - sparsity_comparison_configs: {len(sparsity_comparison_configs)} configurations")
    print(f"  - optimizer_comparison_configs: {len(optimizer_comparison_configs)} configurations")
    print(f"  - all_enhanced_configs: {len(all_enhanced_configs)} configurations")

    print(f"\nUtility functions:")
    print(f"  - create_rank_convergence_study(max_rank=8)")
    print(f"  - create_activation_study(base_config)")
    print(f"  - create_component_complexity_study()")
    print(f"  - create_optimizer_study(base_config)")

    print(f"\nExample usage:")
    print(f"  from enhanced_configuration_examples import enhanced_configs")
    print(f"  result = train_and_compare(enhanced_configs, mol, mf, fci_energy)")

    print(f"\nOptimizer usage:")
    print(f"  # Use different optimizer")
    print(f"  result = train_and_compare(configs, mol, mf, fci_energy,")
    print(f"                            optimizer_type='rmsprop',")
    print(f"                            opt_kwargs={{'decay': 0.9}})")
