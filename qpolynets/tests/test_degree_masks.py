#!/usr/bin/env python3
"""
Test script to verify the new degree mask implementation using polyformer logic.
"""

import sys
import os
import jax
import jax.numpy as jnp
from jax import random

# Enable float64 precision
jax.config.update("jax_enable_x64", True)

# Add PyTC to path
pytc_path = os.path.abspath('../../repos/pytc')
if pytc_path not in sys.path:
    sys.path.insert(0, pytc_path)

# PySCF for quantum chemistry
from pyscf import gto

# Import polynomial Jastrow components
from pytc.autodiff.jastrow.polynomial import CPJastrowEEN


def test_degree_mask_structure():
    """Test the structure of degree masks."""
    print("=== Testing Degree Mask Structure ===")
    
    mol = gto.M(atom='H 0 0 0; H 0 0 1.4', basis='sto-3g', unit='bohr', verbose=0)
    
    # Test different rank/degree combinations
    test_cases = [
        (3, 2),  # rank < degree
        (3, 3),  # rank == degree  
        (3, 4),  # rank > degree
        (4, 2),  # rank < degree (larger difference)
        (2, 6),  # rank >> degree
    ]
    
    for degree, rank in test_cases:
        print(f"\nTesting degree={degree}, rank={rank}:")
        
        cp = CPJastrowEEN(mol, degree=degree, rank=rank, cp_variant='CP_sparse_degree')
        key = random.PRNGKey(42)
        params = cp.init_params(key=key)
        
        # Check mask properties
        masks = cp.masks
        print(f"  Number of masks: {len(masks)} (expected: {degree})")
        assert len(masks) == degree, f"Should have {degree} masks, got {len(masks)}"
        
        for i, mask in enumerate(masks):
            print(f"  Mask {i+1}: shape {mask.shape}, non-zeros: {jnp.sum(mask)}")
            assert mask.shape == (cp.input_dim, rank), f"Mask {i} has wrong shape"
            assert mask.dtype == jnp.float32, f"Mask {i} should be float32"
            
        # Check that masks create different sparsity patterns
        if len(masks) > 1:
            mask_sums = [jnp.sum(mask) for mask in masks]
            print(f"  Sparsity pattern (non-zeros): {mask_sums}")
            
            # First mask should be full (all ones)
            expected_full = cp.input_dim * rank
            assert mask_sums[0] == expected_full, f"First mask should be full ({expected_full}), got {mask_sums[0]}"


def test_degree_mask_logic():
    """Test the specific logic of degree mask generation."""
    print("\n=== Testing Degree Mask Logic ===")
    
    mol = gto.M(atom='H 0 0 0; H 0 0 1.4', basis='sto-3g', unit='bohr', verbose=0)
    
    # Test case 1: rank < degree
    print("\nCase 1: rank < degree (rank=2, degree=4)")
    cp1 = CPJastrowEEN(mol, degree=4, rank=2, cp_variant='CP_sparse_degree')
    key = random.PRNGKey(42)
    params1 = cp1.init_params(key=key)
    
    masks1 = cp1.masks
    print(f"  Expected: {4-2+1} = 3 full masks + 1 sparse mask")
    print(f"  Got {len(masks1)} masks")
    
    # Check first few masks are full
    full_size = cp1.input_dim * cp1.rank
    for i in range(4-2+1):  # First 3 should be full
        if i < len(masks1):
            assert jnp.sum(masks1[i]) == full_size, f"Mask {i} should be full"
    
    # Test case 2: rank >= degree  
    print("\nCase 2: rank >= degree (rank=6, degree=3)")
    cp2 = CPJastrowEEN(mol, degree=3, rank=6, cp_variant='CP_sparse_degree')
    params2 = cp2.init_params(key=key)
    
    masks2 = cp2.masks
    print(f"  Expected: 1 full mask + 2 progressively sparse masks")
    print(f"  Got {len(masks2)} masks")
    
    # First mask should be full
    full_size = cp2.input_dim * cp2.rank
    assert jnp.sum(masks2[0]) == full_size, "First mask should be full"
    
    # Subsequent masks should be progressively sparser
    sparsity_levels = [jnp.sum(mask) for mask in masks2]
    print(f"  Sparsity levels: {sparsity_levels}")
    assert sparsity_levels[0] > sparsity_levels[1], "Second mask should be sparser than first"
    if len(sparsity_levels) > 2:
        assert sparsity_levels[1] > sparsity_levels[2], "Third mask should be sparser than second"


def test_degree_mask_functionality():
    """Test that degree masks work correctly in forward pass."""
    print("\n=== Testing Degree Mask Functionality ===")
    
    mol = gto.M(atom='H 0 0 0; H 0 0 1.4', basis='sto-3g', unit='bohr', verbose=0)
    r1 = jnp.array([0.0, 0.0, 0.5])
    r2 = jnp.array([0.0, 0.0, -0.5])
    key = random.PRNGKey(123)
    
    # Compare standard CP vs degree-sparse CP
    cp_standard = CPJastrowEEN(mol, degree=3, rank=6, cp_variant='CP')
    cp_degree = CPJastrowEEN(mol, degree=3, rank=6, cp_variant='CP_sparse_degree')
    
    params_standard = cp_standard.init_params(key=key)
    params_degree = cp_degree.init_params(key=key)
    
    val_standard = cp_standard._compute(r1, r2, params_standard)
    val_degree = cp_degree._compute(r1, r2, params_degree)
    
    print(f"  Standard CP value: {val_standard:.6f}")
    print(f"  Degree sparse value: {val_degree:.6f}")
    
    # Values should be different (due to sparsity)
    assert not jnp.allclose(val_standard, val_degree), "Sparse and dense should give different values"
    
    # Both should be finite
    assert jnp.isfinite(val_standard), "Standard value should be finite"
    assert jnp.isfinite(val_degree), "Degree sparse value should be finite"
    
    print("  ✓ Both values are finite and different")


def test_degree_mask_with_kfac():
    """Test that degree masks work with KFAC."""
    print("\n=== Testing Degree Masks with KFAC ===")
    
    mol = gto.M(atom='H 0 0 0; H 0 0 1.4', basis='sto-3g', unit='bohr', verbose=0)
    r1 = jnp.array([0.0, 0.0, 0.5])
    r2 = jnp.array([0.0, 0.0, -0.5])
    key = random.PRNGKey(456)
    
    # Test degree masks with and without KFAC
    cp_no_kfac = CPJastrowEEN(mol, degree=3, rank=6, cp_variant='CP_sparse_degree', use_kfac=False)
    cp_kfac = CPJastrowEEN(mol, degree=3, rank=6, cp_variant='CP_sparse_degree', use_kfac=True)
    
    params_no_kfac = cp_no_kfac.init_params(key=key)
    params_kfac = cp_kfac.init_params(key=key)
    
    val_no_kfac = cp_no_kfac._compute(r1, r2, params_no_kfac)
    val_kfac = cp_kfac._compute(r1, r2, params_kfac)
    
    print(f"  No KFAC: {val_no_kfac:.6f}")
    print(f"  With KFAC: {val_kfac:.6f}")
    
    # Values should be identical (KFAC doesn't change forward pass)
    assert jnp.allclose(val_no_kfac, val_kfac, atol=1e-10), "KFAC should not change forward pass values"
    print("  ✓ KFAC compatibility verified")


def main():
    """Run all degree mask tests."""
    print("Testing New Degree Mask Implementation")
    print("=" * 40)
    
    try:
        test_degree_mask_structure()
        test_degree_mask_logic()
        test_degree_mask_functionality()
        test_degree_mask_with_kfac()
        
        print("\n🎉 All degree mask tests passed!")
        print("\nNew degree mask implementation is working correctly:")
        print("  ✓ Proper polyformer logic implemented")
        print("  ✓ Structured sparsity patterns generated")
        print("  ✓ Different rank/degree cases handled")
        print("  ✓ Forward pass functionality verified")
        print("  ✓ KFAC compatibility maintained")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
