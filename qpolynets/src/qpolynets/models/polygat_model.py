"""
Full PolyGAT model: stack of PolyGATBlocks with scalar output.
"""
from typing import Sequence
import jax.numpy as jnp
from flax import nnx
from netket.utils.types import Array, DType

from .polygat import PolyGATBlock


class PolyGAT(nnx.Module):
    def __init__(
        self,
        n_nodes: int,
        hidden_features: Sequence[int] = (32, 16),
        degrees: Sequence[int] = (2, 2),
        heads: Sequence[int] = (2, 1),
        basis: str = "monomial",
        use_cp_weighted: bool = False,
        rank: int | None = None,
        param_dtype: DType = jnp.float64,
        *,
        rngs: nnx.Rngs,
    ):
        assert len(hidden_features) == len(degrees) == len(heads)
        self.n_nodes = n_nodes
        self.layers = []  # list of lists (heads per layer)
        self.is_output_layer = []
        k = rngs
        in_f = hidden_features[0]
        self.input_linear = nnx.Linear(in_features=1, out_features=in_f, dtype=param_dtype, rngs=k)
        cur = in_f
        for i, (out_f, deg, nh) in enumerate(zip(hidden_features, degrees, heads)):
            layer_blocks = []
            for h in range(nh):
                blk = PolyGATBlock(
                    in_features=cur,
                    out_features=out_f,
                    degree=deg,
                    basis=basis,
                    use_cp_weighted=use_cp_weighted,
                    rank=rank,
                    param_dtype=param_dtype,
                    rngs=k,
                )
                layer_blocks.append(blk)
            self.layers.append(layer_blocks)
            last = i == len(hidden_features) - 1
            self.is_output_layer.append(last)
            cur = out_f if last else out_f * nh
        self.output_linear = nnx.Linear(in_features=hidden_features[-1], out_features=1, dtype=param_dtype, rngs=k)

    def __call__(self, x: Array, adjacency: Array) -> Array:
        if x.ndim == 1:
            x = x.reshape(self.n_nodes, 1)
        else:
            x = x.reshape(-1, 1)
        h = self.input_linear(x)
        for (blocks, last) in zip(self.layers, self.is_output_layer):
            outs = [blk(h, adjacency) for blk in blocks]
            if last:
                h = sum(outs) / len(outs)
            else:
                h = jnp.concatenate(outs, axis=-1)
        # global mean pool
        g = jnp.mean(h, axis=0)
        out = self.output_linear(g)
        return out.squeeze()

