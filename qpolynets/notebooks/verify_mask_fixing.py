#!/usr/bin/env python3
"""
Verify that masks remain fixed during optimization.

This script tests that the masks in CP sparse_LU are not being updated
during the optimization process.
"""

import sys
import os
import jax
import jax.numpy as jnp
from jax import random
import optax

# Enable float64 precision
jax.config.update("jax_enable_x64", True)

# Add PyTC to path
pytc_path = os.path.abspath('../../repos/pytc')
if pytc_path not in sys.path:
    sys.path.insert(0, pytc_path)

# PySCF for quantum chemistry
from pyscf import gto, scf

# PyTC imports
from pytc.autodiff.jastrow import CPJastrowEEN

def setup_h2_molecule():
    """Setup H2 molecule for testing."""
    mol = gto.M(atom='H 0 0 0; H 0 0 1.4', basis='cc-pvtz', unit='bohr', verbose=0)
    mf = scf.RHF(mol)
    mf.kernel()
    return mol, mf

def test_mask_immutability():
    """Test that masks remain unchanged during optimization."""
    print("=== Testing Mask Immutability During Optimization ===")
    
    # Setup
    mol_h2, mf_h2 = setup_h2_molecule()
    POLY_DEGREE = 4
    POLY_RANK = 8
    
    # Create CP sparse_LU Jastrow
    cp_sparse_lu = CPJastrowEEN(mol_h2, degree=POLY_DEGREE, rank=POLY_RANK, cp_variant='CP_sparse_LU')
    initial_params = cp_sparse_lu.init_params(key=random.PRNGKey(42))
    
    # Store initial mask values
    initial_masks = [mask.copy() for mask in cp_sparse_lu.masks]
    print(f"Initial masks stored. Number of masks: {len(initial_masks)}")
    
    # Check that masks are not in parameters
    print(f"Masks in parameters: {'masks' in initial_params}")

    # Debug: Check parameter types
    print(f"Parameter keys and types:")
    for key, value in initial_params.items():
        if hasattr(value, 'dtype'):
            print(f"  {key}: {type(value)}, dtype={value.dtype}, shape={getattr(value, 'shape', 'N/A')}")
        else:
            print(f"  {key}: {type(value)}")

    # Create a simple loss function for testing - only use JAX arrays
    def simple_loss(params):
        # Simple quadratic loss for testing
        total_loss = 0.0
        for key, value in params.items():
            if key == 'U':
                for u_matrix in value:
                    total_loss += jnp.sum(u_matrix**2)
            elif key in ['C', 'bias', 'rc_en_raw', 'rc_ee_raw']:
                total_loss += jnp.sum(value**2)
        return total_loss
    
    # Create filtered parameters for optimization (exclude non-differentiable params)
    def filter_params(params):
        filtered = {}
        for key, value in params.items():
            if key == 'U':
                filtered[key] = value
            elif key in ['C', 'bias']:
                filtered[key] = value
            elif key in ['rc_en_raw', 'rc_ee_raw']:
                filtered[key] = jnp.array(value)  # Convert to JAX array
        return filtered

    filtered_initial_params = filter_params(initial_params)

    # Setup optimizer
    optimizer = optax.adam(learning_rate=0.01)
    opt_state = optimizer.init(filtered_initial_params)

    # Perform a few optimization steps
    params = filtered_initial_params
    print(f"\nPerforming optimization steps...")
    
    for step in range(5):
        # Compute gradients
        loss_val, grads = jax.value_and_grad(simple_loss)(params)
        
        # Update parameters
        updates, opt_state = optimizer.update(grads, opt_state, params)
        params = optax.apply_updates(params, updates)
        
        print(f"Step {step}: Loss = {loss_val:.6f}")
        
        # Check if masks have changed
        masks_changed = False
        for i, (initial_mask, current_mask) in enumerate(zip(initial_masks, cp_sparse_lu.masks)):
            if not jnp.allclose(initial_mask, current_mask):
                masks_changed = True
                print(f"  WARNING: Mask {i} has changed!")
                break
        
        if not masks_changed:
            print(f"  ✅ All masks remain unchanged")
    
    # Final verification
    print(f"\n=== Final Verification ===")
    all_masks_unchanged = True
    for i, (initial_mask, final_mask) in enumerate(zip(initial_masks, cp_sparse_lu.masks)):
        if jnp.allclose(initial_mask, final_mask):
            print(f"✅ Mask {i}: UNCHANGED")
        else:
            print(f"❌ Mask {i}: CHANGED")
            all_masks_unchanged = False
            
            # Show differences
            diff = jnp.abs(initial_mask - final_mask)
            max_diff = jnp.max(diff)
            print(f"   Max difference: {max_diff}")
    
    # Check parameter changes
    print(f"\n=== Parameter Changes ===")
    for key in initial_params.keys():
        if key == 'U':
            for i, (initial_u, final_u) in enumerate(zip(initial_params[key], params[key])):
                u_diff = jnp.linalg.norm(initial_u - final_u)
                print(f"U{i} change norm: {u_diff:.6f}")
        elif hasattr(initial_params[key], 'size'):
            param_diff = jnp.linalg.norm(initial_params[key] - params[key])
            print(f"{key} change norm: {param_diff:.6f}")
    
    return all_masks_unchanged

def test_mask_application():
    """Test that masks are correctly applied during forward pass."""
    print(f"\n=== Testing Mask Application ===")
    
    # Setup
    mol_h2, mf_h2 = setup_h2_molecule()
    cp_sparse_lu = CPJastrowEEN(mol_h2, degree=4, rank=8, cp_variant='CP_sparse_LU')
    params = cp_sparse_lu.init_params(key=random.PRNGKey(42))
    
    # Create test input
    test_input = jnp.array([1.0, 2.0, 3.0, 4.0, 5.0])  # 5D input for EEN
    
    # Test forward pass
    try:
        result = cp_sparse_lu._cp_forward(test_input, params)
        print(f"✅ Forward pass successful")
        print(f"   Input shape: {test_input.shape}")
        print(f"   Output shape: {result.shape}")
        print(f"   Output value: {result}")
        
        # Verify masks are being applied
        print(f"\n=== Mask Application Verification ===")
        for i, (u_matrix, mask) in enumerate(zip(params['U'], cp_sparse_lu.masks)):
            masked_u = u_matrix * mask
            zero_elements = jnp.sum(masked_u == 0)
            total_elements = masked_u.size
            print(f"U{i}: {zero_elements}/{total_elements} elements zeroed by mask")
        
        return True
        
    except Exception as e:
        print(f"❌ Forward pass failed: {e}")
        return False

if __name__ == "__main__":
    print("Testing CP sparse_LU mask handling...\n")
    
    # Test 1: Mask immutability
    masks_unchanged = test_mask_immutability()
    
    # Test 2: Mask application
    forward_pass_works = test_mask_application()
    
    # Summary
    print(f"\n" + "="*50)
    print(f"SUMMARY")
    print(f"="*50)
    print(f"✅ Masks remain fixed during optimization: {masks_unchanged}")
    print(f"✅ Forward pass with masks works: {forward_pass_works}")
    
    if masks_unchanged and forward_pass_works:
        print(f"🎉 All tests passed! CP sparse_LU implementation is working correctly.")
    else:
        print(f"⚠️  Some tests failed. Check the implementation.")
