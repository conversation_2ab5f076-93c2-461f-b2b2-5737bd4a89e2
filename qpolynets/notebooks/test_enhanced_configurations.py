"""
Test script for enhanced polynomial network configurations

This script tests the new functionality:
1. Per-component configurations
2. Configurable activation functions  
3. Physics-informed presets
4. Rank analysis capabilities

Run this to verify the implementation works correctly.
"""

import sys
import os
import jax
import jax.numpy as jnp
from jax import random
import numpy as np

# Add the current directory to Python path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from pyscf import gto, scf
    from model_analysis import create_full_ansatz, get_activation_function, PHYSICS_INFORMED_CONFIGS
    from enhanced_configuration_examples import enhanced_configs, rank_analysis_configs
    print("✓ All imports successful")
except ImportError as e:
    print(f"✗ Import error: {e}")
    sys.exit(1)

def test_activation_functions():
    """Test activation function library."""
    print("\n=== Testing Activation Functions ===")
    
    test_input = jnp.array([0.5, -0.5, 1.0, -1.0])
    
    activations_to_test = ['tanh', 'relu', 'gelu', 'swish', 'sigmoid', 'identity']
    
    for activation_name in activations_to_test:
        try:
            activation_fn = get_activation_function(activation_name)
            result = activation_fn(test_input)
            print(f"  ✓ {activation_name}: {result}")
        except Exception as e:
            print(f"  ✗ {activation_name}: {e}")
    
    # Test function input
    try:
        tanh_fn = get_activation_function(jnp.tanh)
        result = tanh_fn(test_input)
        print(f"  ✓ Function input (jnp.tanh): {result}")
    except Exception as e:
        print(f"  ✗ Function input: {e}")

def test_physics_presets():
    """Test physics-informed presets."""
    print("\n=== Testing Physics-Informed Presets ===")
    
    for preset_name, preset_config in PHYSICS_INFORMED_CONFIGS.items():
        print(f"  Preset '{preset_name}':")
        for component, config in preset_config.items():
            print(f"    {component}: degree={config['degree']}, rank={config['rank']}, variant={config['cp_variant']}")

def test_ansatz_creation():
    """Test ansatz creation with enhanced configurations."""
    print("\n=== Testing Ansatz Creation ===")
    
    # Create simple H2 molecule for testing
    mol = gto.M(atom='H 0 0 0; H 0 0 1.4', basis='sto-3g', unit='bohr', verbose=0)
    mf = scf.RHF(mol)
    mf.kernel()
    
    # Test configurations
    test_configs = [
        # Basic configuration (backward compatibility)
        ("Basic", {
            "degree": 3, "rank": 4, "cp_variant": "CP",
            "activation_scale": 0.1
        }),
        
        # Per-component configuration
        ("Per-Component", {
            "en_config": {"degree": 2, "rank": 3, "cp_variant": "CP"},
            "ee_config": {"degree": 3, "rank": 4, "cp_variant": "CP"},
            "een_config": {"degree": 4, "rank": 6, "cp_variant": "CP_sparse_LU"}
        }),
        
        # Physics-informed preset
        ("Physics-Informed", {
            "physics_preset": "simple_molecule"
        }),
        
        # Custom activation functions
        ("Custom Activations", {
            "degree": 3, "rank": 4, "cp_variant": "CP",
            "activation_between_layers": "gelu",
            "final_activation": "swish",
            "activation_scale": 0.1
        }),
        
        # Rank analysis configuration
        ("Rank Analysis", {
            "physics_preset": "rank_analysis",
            "en_config": {"rank": 2},
            "ee_config": {"rank": 2},
            "een_config": {"rank": 2}
        })
    ]
    
    for config_name, config in test_configs:
        try:
            print(f"  Testing {config_name}...")
            
            # Create ansatz
            sj_ansatz, combined_params, composite_params, jastrow_instances = create_full_ansatz(
                mol, mf, "polynomial", **config
            )
            
            # Check components
            en_instance = jastrow_instances['en']
            ee_instance = jastrow_instances['ee'] 
            een_instance = jastrow_instances['een']
            
            print(f"    ✓ EN: degree={en_instance.degree}, rank={en_instance.rank}, variant={en_instance.cp_variant}")
            print(f"    ✓ EE: degree={ee_instance.degree}, rank={ee_instance.rank}, variant={ee_instance.cp_variant}")
            print(f"    ✓ EEN: degree={een_instance.degree}, rank={een_instance.rank}, variant={een_instance.cp_variant}")
            
            # Test evaluation
            r1 = jnp.array([0.0, 0.0, 0.5])
            r2 = jnp.array([0.0, 0.0, -0.5])
            positions = jnp.array([r1, r2])
            
            # Test ansatz evaluation
            log_psi = sj_ansatz(positions, combined_params)
            print(f"    ✓ Ansatz evaluation: log_psi = {log_psi:.6f}")
            
        except Exception as e:
            print(f"    ✗ {config_name} failed: {e}")
            import traceback
            traceback.print_exc()

def test_rank_analysis():
    """Test rank analysis configurations."""
    print("\n=== Testing Rank Analysis ===")
    
    mol = gto.M(atom='H 0 0 0; H 0 0 1.4', basis='sto-3g', unit='bohr', verbose=0)
    mf = scf.RHF(mol)
    mf.kernel()
    
    for config_name, jastrow_type, config in rank_analysis_configs:
        try:
            print(f"  Testing {config_name}...")
            
            sj_ansatz, combined_params, composite_params, jastrow_instances = create_full_ansatz(
                mol, mf, jastrow_type, **config
            )
            
            # Check that all components have the expected rank
            en_rank = jastrow_instances['en'].rank
            ee_rank = jastrow_instances['ee'].rank
            een_rank = jastrow_instances['een'].rank
            
            expected_rank = config['en_config']['rank']
            
            if en_rank == expected_rank and ee_rank == expected_rank and een_rank == expected_rank:
                print(f"    ✓ All components have rank {expected_rank}")
            else:
                print(f"    ✗ Rank mismatch: EN={en_rank}, EE={ee_rank}, EEN={een_rank}, expected={expected_rank}")
                
        except Exception as e:
            print(f"    ✗ {config_name} failed: {e}")

def main():
    """Run all tests."""
    print("Enhanced Configuration Test Suite")
    print("=" * 50)
    
    test_activation_functions()
    test_physics_presets()
    test_ansatz_creation()
    test_rank_analysis()
    
    print("\n" + "=" * 50)
    print("Test suite completed!")
    print("\nIf all tests passed (✓), the enhanced configuration system is working correctly.")
    print("You can now use the new features in your training notebooks.")

if __name__ == "__main__":
    main()
