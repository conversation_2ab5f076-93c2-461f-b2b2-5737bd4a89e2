#!/usr/bin/env python3
"""
Test script to verify all combined mask implementations and new CP variants.
"""

import sys
import os
import jax
import jax.numpy as jnp
from jax import random

# Enable float64 precision
jax.config.update("jax_enable_x64", True)

# Add PyTC to path
pytc_path = os.path.abspath('repos/pytc')
if pytc_path not in sys.path:
    sys.path.insert(0, pytc_path)

# PySCF for quantum chemistry
from pyscf import gto

# Import polynomial Jastrow components
from pytc.autodiff.jastrow.polynomial import CPJastrowEEN


def test_all_cp_variants():
    """Test all CP variants including new combined ones."""
    print("=== Testing All CP Variants ===")
    
    mol = gto.M(atom='H 0 0 0; H 0 0 1.4', basis='sto-3g', unit='bohr', verbose=0)
    
    # All supported variants
    variants = [
        'CP',                           # Standard dense
        'CP_sparse_LU',                 # LU sparsity
        'CP_sparse_degree',             # Degree sparsity
        'CP_sparse_sawtooth',           # Sawtooth sparsity (replaces LU)
        'CP_sparse_degree_LU',          # Degree + LU
        'CP_sparse_degree_sawtooth',    # Degree + Sawtooth (replaces degree+LU+sawtooth)
    ]
    
    key = random.PRNGKey(42)
    r1 = jnp.array([0.0, 0.0, 0.5])
    r2 = jnp.array([0.0, 0.0, -0.5])
    
    results = {}
    
    for variant in variants:
        print(f"\nTesting {variant}:")
        
        try:
            # Create component
            cp = CPJastrowEEN(mol, degree=3, rank=6, cp_variant=variant)
            
            # Initialize parameters
            params = cp.init_params(key=key)
            
            # Compute value
            val = cp._compute(r1, r2, params)
            results[variant] = val
            
            print(f"  ✓ Value: {val:.6f}")
            
            # Check mask properties
            if cp.masks is not None:
                print(f"  ✓ Masks: {len(cp.masks)} masks generated")
                sparsity_levels = [jnp.sum(mask) for mask in cp.masks]
                print(f"  ✓ Sparsity: {sparsity_levels}")
            else:
                print(f"  ✓ No masks (dense variant)")
                
            # Test with KFAC
            cp_kfac = CPJastrowEEN(mol, degree=3, rank=6, cp_variant=variant, use_kfac=True)
            params_kfac = cp_kfac.init_params(key=key)
            val_kfac = cp_kfac._compute(r1, r2, params_kfac)
            
            assert jnp.allclose(val, val_kfac, atol=1e-10), f"KFAC should not change values for {variant}"
            print(f"  ✓ KFAC compatible")
            
        except Exception as e:
            print(f"  ❌ Failed: {e}")
            raise
    
    # Check that different variants give different results
    print(f"\nVariant comparison:")
    dense_val = results['CP']
    for variant, val in results.items():
        if variant != 'CP':
            different = not jnp.allclose(dense_val, val, atol=1e-6)
            print(f"  {variant} vs CP: {'Different' if different else 'Same'} ({val:.6f} vs {dense_val:.6f})")


def test_sawtooth_offsets():
    """Test sawtooth variants with different offsets."""
    print("\n=== Testing Sawtooth Offsets ===")
    
    mol = gto.M(atom='H 0 0 0; H 0 0 1.4', basis='sto-3g', unit='bohr', verbose=0)
    key = random.PRNGKey(123)
    r1 = jnp.array([0.0, 0.0, 0.5])
    r2 = jnp.array([0.0, 0.0, -0.5])
    
    # Test different offset combinations
    offset_cases = [
        (0, 0),   # Standard
        (-1, 1),  # Shifted
        (1, -1),  # Opposite shift
        (0, 1),   # Upper only
        (-1, 0),  # Lower only
    ]
    
    for l_offset, u_offset in offset_cases:
        print(f"\nTesting offsets l={l_offset}, u={u_offset}:")
        
        # Test pure sawtooth
        cp_saw = CPJastrowEEN(mol, degree=3, rank=4, cp_variant='CP_sparse_sawtooth',
                             sawtooth_l_offset=l_offset, sawtooth_u_offset=u_offset)
        params_saw = cp_saw.init_params(key=key)
        val_saw = cp_saw._compute(r1, r2, params_saw)
        
        print(f"  Sawtooth: {val_saw:.6f}")
        
        # Test combined variant
        cp_combined = CPJastrowEEN(mol, degree=3, rank=4, cp_variant='CP_sparse_degree_sawtooth',
                                  sawtooth_l_offset=l_offset, sawtooth_u_offset=u_offset)
        params_combined = cp_combined.init_params(key=key)
        val_combined = cp_combined._compute(r1, r2, params_combined)
        
        print(f"  Combined: {val_combined:.6f}")
        
        # Check mask sparsity
        saw_sparsity = [jnp.sum(mask) for mask in cp_saw.masks]
        combined_sparsity = [jnp.sum(mask) for mask in cp_combined.masks]
        
        print(f"  Sawtooth sparsity: {saw_sparsity}")
        print(f"  Combined sparsity: {combined_sparsity}")
        
        # Combined should be sparser than pure sawtooth
        assert all(c <= s for c, s in zip(combined_sparsity, saw_sparsity)), \
            "Combined masks should be sparser than individual masks"


def test_mask_combination_logic():
    """Test that mask combinations work correctly."""
    print("\n=== Testing Mask Combination Logic ===")
    
    mol = gto.M(atom='H 0 0 0; H 0 0 1.4', basis='sto-3g', unit='bohr', verbose=0)
    
    # Create components to test individual masks
    cp_degree = CPJastrowEEN(mol, degree=3, rank=4, cp_variant='CP_sparse_degree')
    cp_lu = CPJastrowEEN(mol, degree=3, rank=4, cp_variant='CP_sparse_LU')
    cp_sawtooth = CPJastrowEEN(mol, degree=3, rank=4, cp_variant='CP_sparse_sawtooth')
    
    # Initialize to generate masks
    key = random.PRNGKey(456)
    cp_degree.init_params(key=key)
    cp_lu.init_params(key=key)
    cp_sawtooth.init_params(key=key)
    
    # Test degree + LU combination
    cp_degree_lu = CPJastrowEEN(mol, degree=3, rank=4, cp_variant='CP_sparse_degree_LU')
    cp_degree_lu.init_params(key=key)
    
    print("Testing degree + LU combination:")
    for i in range(3):
        degree_mask = cp_degree.masks[i]
        lu_mask = cp_lu.masks[i % len(cp_lu.masks)]
        expected_combined = degree_mask * lu_mask
        actual_combined = cp_degree_lu.masks[i]
        
        print(f"  Mask {i}: Expected sparsity {jnp.sum(expected_combined)}, "
              f"Actual sparsity {jnp.sum(actual_combined)}")
        
        assert jnp.allclose(expected_combined, actual_combined), \
            f"Combined mask {i} should be element-wise product"
    
    # Test degree + sawtooth combination
    cp_degree_sawtooth = CPJastrowEEN(mol, degree=3, rank=4, cp_variant='CP_sparse_degree_sawtooth')
    cp_degree_sawtooth.init_params(key=key)

    print("Testing degree + sawtooth combination:")
    for i in range(3):
        degree_mask = cp_degree.masks[i]
        sawtooth_mask = cp_sawtooth.masks[i]
        expected_combined = degree_mask * sawtooth_mask
        actual_combined = cp_degree_sawtooth.masks[i]

        print(f"  Mask {i}: Expected sparsity {jnp.sum(expected_combined)}, "
              f"Actual sparsity {jnp.sum(actual_combined)}")

        assert jnp.allclose(expected_combined, actual_combined), \
            f"Combined mask {i} should be element-wise product of degree and sawtooth"
    
    print("  ✓ All mask combinations verified")


def test_parameter_passing():
    """Test that parameters are properly passed through constructors."""
    print("\n=== Testing Parameter Passing ===")
    
    mol = gto.M(atom='H 0 0 0; H 0 0 1.4', basis='sto-3g', unit='bohr', verbose=0)
    
    # Test sawtooth offset passing
    cp = CPJastrowEEN(mol, degree=3, rank=4, cp_variant='CP_sparse_sawtooth',
                     sawtooth_l_offset=-1, sawtooth_u_offset=1, use_kfac=True)
    
    assert cp.sawtooth_l_offset == -1, "L offset should be stored"
    assert cp.sawtooth_u_offset == 1, "U offset should be stored"
    assert cp.use_kfac == True, "KFAC flag should be stored"
    assert cp.cp_variant == 'CP_sparse_sawtooth', "Variant should be stored"
    
    print("  ✓ All parameters properly stored")
    
    # Test that offsets affect mask generation
    cp1 = CPJastrowEEN(mol, degree=2, rank=3, cp_variant='CP_sparse_sawtooth',
                      sawtooth_l_offset=0, sawtooth_u_offset=0)
    cp2 = CPJastrowEEN(mol, degree=2, rank=3, cp_variant='CP_sparse_sawtooth',
                      sawtooth_l_offset=-1, sawtooth_u_offset=1)
    
    key = random.PRNGKey(789)
    cp1.init_params(key=key)
    cp2.init_params(key=key)
    
    # Masks should be different due to different offsets
    for i in range(2):
        mask1 = cp1.masks[i]
        mask2 = cp2.masks[i]
        assert not jnp.allclose(mask1, mask2), f"Masks {i} should differ with different offsets"
    
    print("  ✓ Offset parameters affect mask generation")


def main():
    """Run all combined mask tests."""
    print("Testing Combined Mask Implementations and New CP Variants")
    print("=" * 60)
    
    try:
        test_all_cp_variants()
        test_sawtooth_offsets()
        test_mask_combination_logic()
        test_parameter_passing()
        
        print("\n🎉 All combined mask tests passed!")
        print("\nNew CP variants are working correctly:")
        print("  ✓ All 6 CP variants implemented and functional")
        print("  ✓ Mask combination logic verified")
        print("  ✓ Sawtooth offset parameters working")
        print("  ✓ KFAC compatibility maintained for all variants")
        print("  ✓ Parameter passing through constructors verified")
        print("\nAvailable CP variants:")
        print("  • CP (dense)")
        print("  • CP_sparse_LU (alternating upper/lower triangular)")
        print("  • CP_sparse_degree (degree-dependent sparsity)")
        print("  • CP_sparse_sawtooth (sawtooth L/U patterns - replaces LU)")
        print("  • CP_sparse_degree_LU (degree + LU combination)")
        print("  • CP_sparse_degree_sawtooth (degree + sawtooth - most flexible)")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
