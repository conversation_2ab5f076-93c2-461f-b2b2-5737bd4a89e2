"""
Polynomial Jastrow factor (CP-based minimal version)
"""
from typing import Callable
import jax.numpy as jnp
from flax import nnx
from netket.utils.types import Array, DType

from ..layers.polynomial_layers import CP


class PolyJastrow(nnx.Module):
    def __init__(
        self,
        n_sites: int,
        degree: int = 3,
        rank: int = 8,
        output_dim: int = 1,
        activation: Callable = nnx.tanh,
        param_dtype: DType = jnp.float64,
        *,
        rngs: nnx.Rngs,
    ):
        self.n_sites = n_sites
        self.activation = activation
        self.poly = CP(degree=degree, input_dim=n_sites, rank=rank, output_dim=output_dim,
                       param_dtype=param_dtype, rngs=rngs)

    def __call__(self, x: Array) -> Array:
        x = x.reshape(self.n_sites)
        out = self.poly(x)
        return out.squeeze()

