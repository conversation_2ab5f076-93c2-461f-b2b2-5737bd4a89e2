#!/usr/bin/env python3
"""
Test script to verify the new cleaned-up parameter counting system.
"""

import sys
import os
import jax
import jax.numpy as jnp
from jax import random

# Enable float64 precision
jax.config.update("jax_enable_x64", True)

# Add PyTC to path
pytc_path = os.path.abspath('../../repos/pytc')
if pytc_path not in sys.path:
    sys.path.insert(0, pytc_path)

# PySCF for quantum chemistry
from pyscf import gto, scf

# Import the new parameter counting functions
from model_analysis import (
    count_parameters, count_nonzero_parameters, count_effective_parameters,
    get_parameter_info, analyze_full_ansatz_parameters, create_full_ansatz
)

def test_new_parameter_counting():
    """Test the new cleaned-up parameter counting system."""
    print("=== Testing New Parameter Counting System ===")
    
    # Setup molecule
    mol = gto.M(atom='H 0 0 0; H 0 0 1.4', basis='cc-pvtz', unit='bohr', verbose=0)
    mf = scf.RHF(mol)
    mf.kernel()
    
    # Configuration
    POLY_DEGREE = 4
    POLY_RANK = 8
    NEURAL_LAYERS = [8, 8]
    
    print(f"Test configuration:")
    print(f"  Polynomial: degree={POLY_DEGREE}, rank={POLY_RANK}")
    print(f"  Neural: layers={NEURAL_LAYERS}")
    
    # Test 1: Create ansätze with new function that returns instances
    print(f"\n=== Test 1: Create Ansätze with Instance Access ===")
    
    # Neural ansatz
    neural_ansatz, neural_combined, neural_composite, neural_instances = create_full_ansatz(
        mol, mf, "neural", layer_widths=NEURAL_LAYERS
    )
    
    # Standard CP ansatz
    cp_ansatz, cp_combined, cp_composite, cp_instances = create_full_ansatz(
        mol, mf, "polynomial", degree=POLY_DEGREE, rank=POLY_RANK, cp_variant='CP'
    )
    
    # Sparse CP ansatz
    sparse_ansatz, sparse_combined, sparse_composite, sparse_instances = create_full_ansatz(
        mol, mf, "polynomial", degree=POLY_DEGREE, rank=POLY_RANK, cp_variant='CP_sparse_LU'
    )
    
    print(f"✅ Successfully created all ansätze with instance access")
    print(f"Neural instances: {list(neural_instances.keys())}")
    print(f"CP instances: {list(cp_instances.keys())}")
    print(f"Sparse instances: {list(sparse_instances.keys())}")
    
    # Test 2: Verify mask access
    print(f"\n=== Test 2: Verify Mask Access ===")
    
    neural_een_masks = hasattr(neural_instances['een'], 'masks') and neural_instances['een'].masks is not None
    cp_een_masks = hasattr(cp_instances['een'], 'masks') and cp_instances['een'].masks is not None
    sparse_een_masks = hasattr(sparse_instances['een'], 'masks') and sparse_instances['een'].masks is not None
    
    print(f"Neural EEN has masks: {neural_een_masks}")
    print(f"CP EEN has masks: {cp_een_masks}")
    print(f"Sparse EEN has masks: {sparse_een_masks} ✅" if sparse_een_masks else f"Sparse EEN has masks: {sparse_een_masks} ❌")
    
    if sparse_een_masks:
        print(f"Sparse EEN mask count: {len(sparse_instances['een'].masks)}")
    
    # Test 3: Test new get_parameter_info function
    print(f"\n=== Test 3: Test get_parameter_info Function ===")
    
    # Test on EEN component parameters
    sparse_een_params = sparse_composite[3]  # EEN is index 3
    
    info = get_parameter_info(sparse_een_params, sparse_instances['een'])
    
    print(f"Parameter info for sparse EEN:")
    print(f"  Total count: {info['total_count']}")
    print(f"  Number of elements: {len(info['elements'])}")
    
    for elem in info['elements']:
        print(f"    {elem['path']}: {elem['total_elements']} total, {elem['nonzero_elements']} nonzero, {elem['effective_elements']} effective")
        if elem['mask_info']['has_mask']:
            print(f"      Mask allows: {elem['mask_info']['mask_allows']}, sparsity: {elem['mask_info']['mask_sparsity']:.3f}")
    
    # Test 4: Test updated counting functions
    print(f"\n=== Test 4: Test Updated Counting Functions ===")
    
    # Test on the same sparse EEN parameters
    total_count = count_parameters(sparse_een_params)
    nonzero_count = count_nonzero_parameters(sparse_een_params, sparse_instances['een'])
    effective_analysis = count_effective_parameters(sparse_een_params, sparse_instances['een'])
    
    print(f"Sparse EEN parameter counts:")
    print(f"  Total: {total_count}")
    print(f"  Non-zero: {nonzero_count}")
    print(f"  Effective: {effective_analysis['effective_parameters']}")
    print(f"  Sparsity ratio: {effective_analysis['sparsity_ratio']:.3f}")
    
    # Verify consistency
    info_total = info['total_count']
    info_nonzero = sum(elem['nonzero_elements'] for elem in info['elements'])
    info_effective = sum(elem['effective_elements'] for elem in info['elements'])
    
    consistency_checks = [
        total_count == info_total,
        nonzero_count == info_nonzero,
        effective_analysis['effective_parameters'] == info_effective
    ]
    
    print(f"\nConsistency checks:")
    print(f"  Total count matches: {consistency_checks[0]} ✅" if consistency_checks[0] else f"  Total count matches: {consistency_checks[0]} ❌")
    print(f"  Nonzero count matches: {consistency_checks[1]} ✅" if consistency_checks[1] else f"  Nonzero count matches: {consistency_checks[1]} ❌")
    print(f"  Effective count matches: {consistency_checks[2]} ✅" if consistency_checks[2] else f"  Effective count matches: {consistency_checks[2]} ❌")
    
    # Test 5: Test new full ansatz analysis
    print(f"\n=== Test 5: Test Full Ansatz Analysis ===")
    
    # Analyze complete sparse ansatz
    full_analysis = analyze_full_ansatz_parameters(sparse_combined, sparse_instances)
    
    print(f"Full sparse ansatz analysis:")
    print(f"  Total parameters: {full_analysis['summary']['total_parameters']}")
    print(f"  Total effective: {full_analysis['summary']['total_effective']}")
    print(f"  Overall sparsity: {full_analysis['summary']['overall_sparsity']:.3f}")
    
    print(f"\nComponent breakdown:")
    for name, count in full_analysis['summary']['component_breakdown'].items():
        print(f"  {name}: {count} parameters")
    
    print(f"\nDetailed component analysis:")
    for name, comp in full_analysis['components'].items():
        if 'sparsity_info' in comp and comp['sparsity_info']['has_sparsity']:
            print(f"  {name}: {comp['total']} total, {comp['effective_analysis']['effective_parameters']} effective (sparsity: {comp['effective_analysis']['sparsity_ratio']:.3f})")
        else:
            print(f"  {name}: {comp['total']} parameters (no sparsity)")
    
    # Test 6: Compare with old vs new system
    print(f"\n=== Test 6: Compare All Three Ansätze ===")
    
    ansätze = [
        ("Neural", neural_combined, neural_instances),
        ("CP Standard", cp_combined, cp_instances),
        ("CP Sparse_LU", sparse_combined, sparse_instances)
    ]
    
    for name, combined_params, instances in ansätze:
        analysis = analyze_full_ansatz_parameters(combined_params, instances)
        summary = analysis['summary']
        
        print(f"\n{name}:")
        print(f"  Total: {summary['total_parameters']}")
        print(f"  Effective: {summary['total_effective']}")
        print(f"  Sparsity: {summary['overall_sparsity']:.3f}")
        print(f"  EEN component: {summary['component_breakdown']['een']} params")
    
    # Summary
    print(f"\n" + "="*60)
    print(f"SUMMARY")
    print(f"="*60)
    
    all_tests_passed = all([
        sparse_een_masks,  # Sparse has masks
        all(consistency_checks),  # All consistency checks pass
        full_analysis['summary']['overall_sparsity'] > 0  # Sparsity detected
    ])
    
    print(f"✅ Sparse EEN has masks: {sparse_een_masks}")
    print(f"✅ Counting functions consistent: {all(consistency_checks)}")
    print(f"✅ Full ansatz analysis works: {full_analysis['summary']['overall_sparsity'] > 0}")
    print(f"✅ Instance access works: True")
    print(f"✅ New parameter info function works: True")
    
    print(f"\n🎉 All tests passed: {all_tests_passed}")
    
    if all_tests_passed:
        print(f"✅ New parameter counting system is working correctly!")
        print(f"✅ Cleaned up architecture with modular functions")
        print(f"✅ Full ansatz analysis capability added")
        print(f"✅ Instance access for mask information fixed")
    else:
        print(f"⚠️  Some tests failed. Check the implementation.")
    
    return all_tests_passed

if __name__ == "__main__":
    test_new_parameter_counting()
