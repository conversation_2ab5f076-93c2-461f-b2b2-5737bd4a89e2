"""
Polynomial layers migrated from qgat/core/polynomial_layers.py
Includes: CP, CP_sparse_LU, CP_sparse_degree, QuantumPolynomialLayer
"""
import jax
import jax.numpy as jnp
from flax import nnx
from typing import Optional, Any


class CP(nnx.Module):
    def __init__(self, degree: int, input_dim: int, rank: int, output_dim: int,
                 param_dtype: Any = jnp.float64, rngs: Optional[nnx.Rngs] = None):
        if rngs is None:
            rngs = nnx.Rngs(0)
        self.degree = degree
        self.input_dim = input_dim
        self.rank = rank
        self.output_dim = output_dim
        for i in range(1, degree + 1):
            setattr(self, f'U{i}', nnx.Linear(input_dim, rank, use_bias=False, param_dtype=param_dtype, rngs=rngs))
        self.layer_C = nnx.Linear(rank, output_dim, param_dtype=param_dtype, rngs=rngs)

    def __call__(self, x):
        original_shape = x.shape[:-1]
        x_flat = x.reshape(-1, self.input_dim)
        out = getattr(self, 'U1')(x_flat)
        for i in range(2, self.degree + 1):
            ui_out = getattr(self, f'U{i}')(x_flat)
            out = ui_out * out + out
        result = self.layer_C(out)
        return result.reshape(*original_shape, self.output_dim)


class CP_sparse_LU(nnx.Module):
    def __init__(self, degree: int, input_dim: int, rank: int, output_dim: int,
                 param_dtype: Any = jnp.float64, rngs: Optional[nnx.Rngs] = None):
        if rngs is None:
            rngs = nnx.Rngs(0)
        self.degree = degree
        self.input_dim = input_dim
        self.rank = rank
        self.output_dim = output_dim
        for i in range(1, degree + 1):
            setattr(self, f'U{i}', nnx.Linear(input_dim, rank, use_bias=False, param_dtype=param_dtype, rngs=rngs))
        self.mask1, self.mask2 = self._generate_lu_masks()
        self.layer_C = nnx.Linear(rank, output_dim, param_dtype=param_dtype, rngs=rngs)

    def _generate_lu_masks(self):
        mask1 = jnp.triu(jnp.ones((self.rank, self.input_dim)))
        mask2 = jnp.tril(jnp.ones((self.rank, self.input_dim)))
        return mask1, mask2

    def __call__(self, x):
        original_shape = x.shape[:-1]
        x_flat = x.reshape(-1, self.input_dim)
        u1_weight = getattr(self, 'U1').kernel * self.mask1.T
        out = jnp.dot(x_flat, u1_weight)
        for i in range(2, self.degree + 1, 2):
            ui_weight = getattr(self, f'U{i}').kernel * self.mask2.T
            ui_out = jnp.dot(x_flat, ui_weight)
            out = ui_out * out + out
            if i + 1 <= self.degree:
                ui1_weight = getattr(self, f'U{i+1}').kernel * self.mask1.T
                ui1_out = jnp.dot(x_flat, ui1_weight)
                out = ui1_out * out + out
        result = self.layer_C(out)
        return result.reshape(*original_shape, self.output_dim)


class CP_sparse_degree(nnx.Module):
    def __init__(self, degree: int, input_dim: int, rank: int, output_dim: int,
                 param_dtype: Any = jnp.float64, rngs: Optional[nnx.Rngs] = None):
        if rngs is None:
            rngs = nnx.Rngs(0)
        self.degree = degree
        self.input_dim = input_dim
        self.rank = rank
        self.output_dim = output_dim
        for i in range(1, degree + 1):
            setattr(self, f'U{i}', nnx.Linear(input_dim, rank, use_bias=False, param_dtype=param_dtype, rngs=rngs))
        self.masks = self._generate_degree_masks()
        self.layer_C = nnx.Linear(rank, output_dim, param_dtype=param_dtype, rngs=rngs)

    def _generate_degree_masks(self):
        masks = {}
        for i in range(1, self.degree + 1):
            sparsity_ratio = 1.0 - (i - 1) * 0.1
            mask = jax.random.bernoulli(jax.random.PRNGKey(i), sparsity_ratio, (self.rank, self.input_dim))
            masks[f'mask{i}'] = mask
        return masks

    def __call__(self, x):
        original_shape = x.shape[:-1]
        x_flat = x.reshape(-1, self.input_dim)
        u1_weight = getattr(self, 'U1').kernel * self.masks['mask1'].T
        out = jnp.dot(x_flat, u1_weight)
        for i in range(2, self.degree + 1):
            ui_weight = getattr(self, f'U{i}').kernel * self.masks[f'mask{i}'].T
            ui_out = jnp.dot(x_flat, ui_weight)
            out = ui_out * out + out
        result = self.layer_C(out)
        return result.reshape(*original_shape, self.output_dim)


class QuantumPolynomialLayer(nnx.Module):
    def __init__(self, degree: int, input_dim: int, rank: int, output_dim: int,
                 preserve_symmetries: bool = True, param_dtype: Any = jnp.float64,
                 rngs: Optional[nnx.Rngs] = None):
        if rngs is None:
            rngs = nnx.Rngs(0)
        self.degree = degree
        self.input_dim = input_dim
        self.rank = rank
        self.output_dim = output_dim
        self.preserve_symmetries = preserve_symmetries
        self.polynomial_net = CP(degree, input_dim, rank, output_dim, param_dtype, rngs)
        if preserve_symmetries:
            self.symmetry_projection = nnx.Linear(output_dim, output_dim, param_dtype=param_dtype, rngs=rngs)

    def __call__(self, x):
        out = self.polynomial_net(x)
        if self.preserve_symmetries:
            out = self.symmetry_projection(out)
        return out


POLYNOMIAL_CLASSES = {
    'CP': CP,
    'CP_sparse_LU': CP_sparse_LU,
    'CP_sparse_degree': CP_sparse_degree,
    'QuantumPolynomial': QuantumPolynomialLayer,
}


def get_polynomial_class(name: str):
    if name not in POLYNOMIAL_CLASSES:
        raise ValueError(f"Unknown polynomial class: {name}. Available: {list(POLYNOMIAL_CLASSES.keys())}")
    return POLYNOMIAL_CLASSES[name]

