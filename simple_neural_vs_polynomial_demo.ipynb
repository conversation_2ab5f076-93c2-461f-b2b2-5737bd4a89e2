{
 "cells": [
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "# Neural vs Polynomial Jastrow Factors: Quick Demo\n",
    "\n",
    "This notebook demonstrates the key differences between neural network and polynomial-based Jastrow factors in PyTC.\n",
    "\n",
    "## Key Points:\n",
    "- **Neural J<PERSON>row**: Multi-layer perceptrons with tanh activations\n",
    "- **Polynomial Jastrow**: CP decomposition with pure polynomial correlations\n",
    "- **Comparison**: Parameter efficiency, accuracy, and computational performance"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Setup\n",
    "import sys\n",
    "import os\n",
    "sys.path.insert(0, os.path.abspath('../../repos/pytc'))\n",
    "\n",
    "import jax\n",
    "import jax.numpy as jnp\n",
    "from jax import random\n",
    "import matplotlib.pyplot as plt\n",
    "from pyscf import gto, scf\n",
    "\n",
    "# Enable float64 for quantum chemistry\n",
    "jax.config.update(\"jax_enable_x64\", True)\n",
    "\n",
    "# PyTC imports\n",
    "from pytc.autodiff.jastrow import (\n",
    "    CPJastrowEN, CPJastrowEE, CPJastrowEEN,\n",
    "    NeuralEN, NeuralEE, NeuralEEN,\n",
    "    NuclearCusp, CompositeJastrow\n",
    ")\n",
    "from pytc.autodiff.ansatz.sj import SlaterJastrow\n",
    "from pytc.autodiff.ansatz.det import SlaterDet\n",
    "from pytc.autodiff.mcmc import sample\n",
    "from pytc.autodiff.mcmc_utils import analyze_energies\n",
    "\n",
    "print(\"Setup complete!\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 1. Molecular System Setup"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Create H2 molecule\n",
    "mol = gto.M(atom='H 0 0 0; H 0 0 1.4', basis='sto-3g', unit='bohr', verbose=0)\n",
    "mf = scf.RHF(mol)\n",
    "mf.kernel()\n",
    "\n",
    "print(f\"H2 molecule (STO-3G basis)\")\n",
    "print(f\"Number of electrons: {mol.nelectron}\")\n",
    "print(f\"HF energy: {mf.e_tot:.6f} Hartree\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 2. Parameter Count Comparison"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "def count_parameters(params):\n",
    "    \"\"\"Count total parameters in a parameter structure.\"\"\"\n",
    "    if isinstance(params, dict):\n",
    "        total = 0\n",
    "        for key, value in params.items():\n",
    "            if key == 'net_vars':  # Neural network parameters\n",
    "                # Flax stores parameters under 'params' key\n",
    "                if isinstance(value, dict) and 'params' in value:\n",
    "                    total += count_parameters(value['params'])\n",
    "                else:\n",
    "                    total += count_parameters(value)"\n",
    "            elif key == 'U':  # CP U matrices\n",
    "                total += sum(u.size for u in value)\n",
    "            elif hasattr(value, 'size'):  # JAX array\n",
    "                total += value.size\n",
    "            elif isinstance(value, (list, tuple)):\n",
    "                total += sum(count_parameters(v) for v in value)\n",
    "            elif isinstance(value, dict):\n",
    "                total += count_parameters(value)\n",
    "        return total\n",
    "    elif hasattr(params, 'size'):  # JAX array\n",
    "        return params.size\n",
    "    elif isinstance(params, (list, tuple)):\n",
    "        return sum(count_parameters(p) for p in params)\n",
    "    else:\n",
    "        return 0\n",
    "\n",
    "# Compare parameter counts\n",
    "key = random.PRNGKey(42)\n",
    "\n",
    "# Neural Jastrow\n",
    "neural_een = NeuralEEN(mol, layer_widths=[8, 8])\n",
    "neural_params = neural_een.init_params(key=key)\n",
    "neural_count = count_parameters(neural_params)\n",
    "\n",
    "# Polynomial Jastrow\n",
    "cp_een = CPJastrowEEN(mol, degree=3, rank=6)\n",
    "cp_params = cp_een.init_params(key=key)\n",
    "cp_count = count_parameters(cp_params)\n",
    "\n",
    "print(f\"Parameter Comparison (EEN component only):\")\n",
    "print(f\"Neural EEN (8,8): {neural_count} parameters\")\n",
    "print(f\"CP EEN (deg=3, rank=6): {cp_count} parameters\")\n",
    "print(f\"Ratio: {cp_count/neural_count:.1f}x more parameters for CP\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 3. Function Evaluation Comparison"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Test function evaluation\n",
    "r1 = jnp.array([0.0, 0.0, 0.5])\n",
    "r2 = jnp.array([0.0, 0.0, -0.5])\n",
    "\n",
    "# Neural Jastrow evaluation\n",
    "neural_val = neural_een._compute(r1, r2, neural_params)\n",
    "neural_grad, neural_lap = neural_een.get_log_grads_r1(r1, r2, neural_params)\n",
    "\n",
    "# Polynomial Jastrow evaluation\n",
    "cp_val = cp_een._compute(r1, r2, cp_params)\n",
    "cp_grad, cp_lap = cp_een.get_log_grads_r1(r1, r2, cp_params)\n",
    "\n",
    "print(f\"Function Evaluation Comparison:\")\n",
    "print(f\"Neural EEN value: {neural_val:.6f}\")\n",
    "print(f\"CP EEN value: {cp_val:.6f}\")\n",
    "print(f\"\")\n",
    "print(f\"Neural gradient norm: {jnp.linalg.norm(neural_grad):.6f}\")\n",
    "print(f\"CP gradient norm: {jnp.linalg.norm(cp_grad):.6f}\")\n",
    "print(f\"\")\n",
    "print(f\"Neural Laplacian: {neural_lap:.6f}\")\n",
    "print(f\"CP Laplacian: {cp_lap:.6f}\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 4. VMC Energy Comparison"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "def create_full_ansatz(mol, mf, jastrow_type, **kwargs):\n",
    "    \"\"\"Create complete Slater-Jastrow ansatz.\"\"\"\n",
    "    key = random.PRNGKey(42)\n",
    "    \n",
    "    # Nuclear cusp for stability\n",
    "    ncusp = NuclearCusp(mol)\n",
    "    params_ncusp = ncusp.init_params()\n",
    "    \n",
    "    if jastrow_type == \"neural\":\n",
    "        layer_widths = kwargs.get('layer_widths', [8, 8])\n",
    "        neural_en = NeuralEN(mol, layer_widths=layer_widths)\n",
    "        neural_ee = NeuralEE(mol, layer_widths=layer_widths)\n",
    "        neural_een = NeuralEEN(mol, layer_widths=layer_widths)\n",
    "        \n",
    "        params_en = neural_en.init_params(key=key)\n",
    "        params_ee = neural_ee.init_params(key=key)\n",
    "        params_een = neural_een.init_params(key=key)\n",
    "        \n",
    "        composite = CompositeJastrow([ncusp, neural_en, neural_ee, neural_een])\n",
    "        composite_params = [params_ncusp, params_en, params_ee, params_een]\n",
    "        \n",
    "    elif jastrow_type == \"polynomial\":\n",
    "        degree = kwargs.get('degree', 3)\n",
    "        rank = kwargs.get('rank', 6)\n",
    "        activation_scale = kwargs.get('activation_scale', 0.05)\n",
    "        \n",
    "        cp_en = CPJastrowEN(mol, degree=degree, rank=rank, activation_scale=activation_scale)\n",
    "        cp_ee = CPJastrowEE(mol, degree=degree, rank=rank, activation_scale=activation_scale)\n",
    "        cp_een = CPJastrowEEN(mol, degree=degree, rank=rank, activation_scale=activation_scale)\n",
    "        \n",
    "        params_en = cp_en.init_params(key=key)\n",
    "        params_ee = cp_ee.init_params(key=key)\n",
    "        params_een = cp_een.init_params(key=key)\n",
    "        \n",
    "        composite = CompositeJastrow([ncusp, cp_en, cp_ee, cp_een])\n",
    "        composite_params = [params_ncusp, params_en, params_ee, params_een]\n",
    "    \n",
    "    # Create Slater-Jastrow ansatz\n",
    "    det = SlaterDet(mol, mf.mo_coeff)\n",
    "    sj_ansatz = SlaterJastrow(mol, composite, [det])\n",
    "    combined_params = (composite_params, jnp.array([1.0]))\n",
    "    \n",
    "    return sj_ansatz, combined_params\n",
    "\n",
    "# VMC parameters\n",
    "vmc_params = {\n",
    "    'n_walkers': 64,\n",
    "    'n_steps': 100,\n",
    "    'step_size': 0.02,\n",
    "    'burn_in_steps': 20,\n",
    "    'use_importance_sampling': True,\n",
    "    'thinning': 2,\n",
    "}\n",
    "\n",
    "results = {}\n",
    "\n",
    "# Test Neural Jastrow\n",
    "print(\"Testing Neural Jastrow...\")\n",
    "neural_ansatz, neural_combined_params = create_full_ansatz(mol, mf, \"neural\", layer_widths=[8, 8])\n",
    "neural_vmc = sample(neural_ansatz, params=neural_combined_params, key=random.PRNGKey(123), **vmc_params)\n",
    "neural_stats = analyze_energies(neural_vmc)\n",
    "neural_energy = float(neural_stats['mean'])\n",
    "neural_error = float(neural_stats['error'])\n",
    "neural_acceptance = float(jnp.mean(neural_vmc['acceptance_rates']))\n",
    "neural_total_params = count_parameters(neural_combined_params)\n",
    "\n",
    "print(f\"Neural: {neural_energy:.6f} ± {neural_error:.6f} Ha, {neural_total_params} params, {neural_acceptance:.3f} acceptance\")\n",
    "\n",
    "# Test Polynomial Jastrow\n",
    "print(\"Testing Polynomial Jastrow...\")\n",
    "cp_ansatz, cp_combined_params = create_full_ansatz(mol, mf, \"polynomial\", degree=3, rank=6)\n",
    "cp_vmc = sample(cp_ansatz, params=cp_combined_params, key=random.PRNGKey(123), **vmc_params)\n",
    "cp_stats = analyze_energies(cp_vmc)\n",
    "cp_energy = float(cp_stats['mean'])\n",
    "cp_error = float(cp_stats['error'])\n",
    "cp_acceptance = float(jnp.mean(cp_vmc['acceptance_rates']))\n",
    "cp_total_params = count_parameters(cp_combined_params)\n",
    "\n",
    "print(f\"Polynomial: {cp_energy:.6f} ± {cp_error:.6f} Ha, {cp_total_params} params, {cp_acceptance:.3f} acceptance\")\n",
    "\n",
    "# Summary\n",
    "print(f\"\\n=== Summary ===\")\n",
    "print(f\"HF energy: {mf.e_tot:.6f} Ha\")\n",
    "print(f\"Neural improvement: {neural_energy - mf.e_tot:.6f} Ha\")\n",
    "print(f\"Polynomial improvement: {cp_energy - mf.e_tot:.6f} Ha\")\n",
    "print(f\"Polynomial is {abs(cp_energy - mf.e_tot)/abs(neural_energy - mf.e_tot):.1f}x better energy improvement\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 5. Visualization"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Create comparison plot\n",
    "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))\n",
    "\n",
    "# Energy comparison\n",
    "methods = ['Neural (8,8)', 'Polynomial (deg=3, rank=6)']\n",
    "energies = [neural_energy, cp_energy]\n",
    "errors = [neural_error, cp_error]\n",
    "colors = ['lightcoral', 'lightblue']\n",
    "\n",
    "ax1.bar(methods, energies, yerr=errors, capsize=5, color=colors, alpha=0.7)\n",
    "ax1.axhline(y=mf.e_tot, color='red', linestyle='--', label='HF Energy')\n",
    "ax1.set_ylabel('Energy (Hartree)')\n",
    "ax1.set_title('VMC Energies')\n",
    "ax1.legend()\n",
    "ax1.grid(alpha=0.3)\n",
    "\n",
    "# Parameter comparison\n",
    "param_counts = [neural_total_params, cp_total_params]\n",
    "ax2.bar(methods, param_counts, color=colors, alpha=0.7)\n",
    "ax2.set_ylabel('Number of Parameters')\n",
    "ax2.set_title('Parameter Counts')\n",
    "ax2.grid(alpha=0.3)\n",
    "\n",
    "plt.tight_layout()\n",
    "plt.show()\n",
    "\n",
    "print(\"\\n✓ Comparison complete!\")"
   ]
  }
 ],
 "metadata": {
  "kernelspec": {
   "display_name": "Python 3",
   "language": "python",
   "name": "python3"
  },
  "language_info": {
   "codemirror_mode": {
    "name": "ipython",
    "version": 3
   },
   "file_extension": ".py",
   "mimetype": "text/x-python",
   "name": "python",
   "nbconvert_exporter": "python",
   "pygments_lexer": "ipython3",
   "version": "3.8.5"
  }
 },
 "nbformat": 4,
 "nbformat_minor": 4
}
