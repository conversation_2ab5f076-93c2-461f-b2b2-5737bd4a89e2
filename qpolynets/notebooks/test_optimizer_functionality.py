"""
Test script for the new optimizer functionality in train_and_compare

This script demonstrates how to use different optimizers with the enhanced
train_and_compare function.
"""

import sys
import os

# Add the current directory to Python path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_optimizer_parameters():
    """Test that the train_and_compare function accepts optimizer parameters."""
    print("=== Testing Optimizer Parameter Support ===")
    
    try:
        from model_analysis import train_and_compare
        import inspect
        
        # Get function signature
        sig = inspect.signature(train_and_compare)
        params = list(sig.parameters.keys())
        
        print("✓ train_and_compare function imported successfully")
        print(f"Function parameters: {params}")
        
        # Check for new optimizer parameters
        required_params = ['optimizer_type', 'opt_kwargs']
        missing_params = [p for p in required_params if p not in params]
        
        if missing_params:
            print(f"✗ Missing optimizer parameters: {missing_params}")
            return False
        else:
            print("✓ All optimizer parameters present")
            
        # Check default values
        optimizer_type_default = sig.parameters['optimizer_type'].default
        opt_kwargs_default = sig.parameters['opt_kwargs'].default
        
        print(f"✓ optimizer_type default: {optimizer_type_default}")
        print(f"✓ opt_kwargs default: {opt_kwargs_default}")
        
        return True
        
    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False
    except Exception as e:
        print(f"✗ Unexpected error: {e}")
        return False

def show_optimizer_examples():
    """Show examples of how to use different optimizers."""
    print("\n=== Optimizer Usage Examples ===")
    
    examples = [
        ("Adam (default)", {
            "optimizer_type": "adam",
            "learning_rate": 0.0003,
            "opt_kwargs": None
        }),
        ("SGD", {
            "optimizer_type": "sgd", 
            "learning_rate": 0.001,
            "opt_kwargs": None
        }),
        ("RMSprop", {
            "optimizer_type": "rmsprop",
            "learning_rate": 0.0005,
            "opt_kwargs": {"decay": 0.9, "eps": 1e-6}
        }),
        ("Lion", {
            "optimizer_type": "lion",
            "learning_rate": 0.0001,
            "opt_kwargs": {"b1": 0.9, "b2": 0.99}
        }),
        ("K-FAC (advanced)", {
            "optimizer_type": "kfac",
            "learning_rate": 0.0003,
            "opt_kwargs": {
                "value_and_grad_func": "your_value_and_grad_function",
                "l2_reg": 0.001,
                "use_adaptive_learning_rate": True
            }
        })
    ]
    
    for name, config in examples:
        print(f"\n{name}:")
        print("  train_and_compare(")
        print("      configs, mol, mf, fci_energy,")
        for key, value in config.items():
            if value is None:
                continue
            if isinstance(value, str):
                print(f"      {key}=\"{value}\",")
            else:
                print(f"      {key}={value},")
        print("      # ... other parameters")
        print("  )")

def show_notebook_integration():
    """Show how to integrate optimizer selection in notebooks."""
    print("\n=== Notebook Integration Examples ===")
    
    notebook_code = '''
# In your notebook, you can now specify different optimizers:

# 1. Basic optimizer change
training_results = train_and_compare(
    mol=mol, mf=mf, fci_energy=fci_energy,
    molecule_name=MOLECULE_NAME,
    configs=training_configs,
    n_opt_steps=N_OPT_STEPS,
    learning_rate=LEARNING_RATE,
    optimizer_type="rmsprop",  # NEW: Use RMSprop instead of Adam
    opt_n_walkers=OPT_N_WALKERS,
    eval_n_walkers=EVAL_N_WALKERS
)

# 2. Optimizer with custom parameters
training_results = train_and_compare(
    mol=mol, mf=mf, fci_energy=fci_energy,
    molecule_name=MOLECULE_NAME,
    configs=training_configs,
    n_opt_steps=N_OPT_STEPS,
    learning_rate=0.0005,  # Adjusted for RMSprop
    optimizer_type="rmsprop",
    opt_kwargs={"decay": 0.95, "eps": 1e-7},  # Custom RMSprop parameters
    opt_n_walkers=OPT_N_WALKERS,
    eval_n_walkers=EVAL_N_WALKERS
)

# 3. Optimizer comparison loop
optimizers_to_test = [
    ("adam", 0.0003, {}),
    ("sgd", 0.001, {}),
    ("rmsprop", 0.0005, {"decay": 0.9}),
    ("lion", 0.0001, {"b1": 0.9, "b2": 0.99})
]

optimizer_results = {}
for opt_name, lr, opt_kwargs in optimizers_to_test:
    print(f"\\nTesting {opt_name} optimizer...")
    
    results = train_and_compare(
        mol=mol, mf=mf, fci_energy=fci_energy,
        molecule_name=f"{MOLECULE_NAME}_{opt_name}",
        configs=training_configs,
        n_opt_steps=200,  # Shorter for comparison
        learning_rate=lr,
        optimizer_type=opt_name,
        opt_kwargs=opt_kwargs,
        opt_n_walkers=32,
        eval_n_walkers=64
    )
    
    optimizer_results[opt_name] = results

# Compare optimizer performance
for opt_name, results in optimizer_results.items():
    for config_name, result in results.items():
        if result is not None:
            print(f"{opt_name} - {config_name}: {result['final_energy']:.6f} Ha")
'''
    
    print(notebook_code)

def main():
    """Run all tests and show examples."""
    print("Optimizer Functionality Test Suite")
    print("=" * 50)
    
    # Test that the function has the new parameters
    success = test_optimizer_parameters()
    
    if success:
        print("\n✓ Optimizer functionality is properly implemented!")
        show_optimizer_examples()
        show_notebook_integration()
        
        print("\n" + "=" * 50)
        print("✓ All tests passed!")
        print("\nYou can now use different optimizers in your training workflow.")
        print("See the examples above for how to integrate this into your notebooks.")
        
    else:
        print("\n✗ Optimizer functionality test failed!")
        print("Please check that the train_and_compare function has been properly updated.")

if __name__ == "__main__":
    main()
