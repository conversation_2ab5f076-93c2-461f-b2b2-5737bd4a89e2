{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Neural vs Polynomial Jastrow Factor Comparison\n", "\n", "This notebook provides a comprehensive comparison between neural network-based and polynomial-based Jastrow factors in transcorrelated quantum chemistry using PyTC.\n", "\n", "## Key Comparisons:\n", "1. **Mathematical Expressiveness**: Neural networks vs CP polynomial decomposition\n", "2. **Parameter Efficiency**: Number of parameters for similar accuracy\n", "3. **Training Stability**: Convergence behavior and numerical stability\n", "4. **Computational Performance**: Evaluation speed and memory usage\n", "5. **Physical Interpretability**: Understanding correlation effects\n", "\n", "## Background\n", "\n", "### Neural Jastrow Factors\n", "- Use multi-layer perceptrons with tanh activations\n", "- Flexible function approximation with residual connections\n", "- Bounded outputs for numerical stability\n", "- Components: NeuralEN, NeuralEE, NeuralEEN\n", "\n", "### Polynomial Jastrow Factors\n", "- Use Canonical Polyadic (CP) decomposition\n", "- Pure polynomial correlations without intermediate activations\n", "- Structured sparsity patterns (LU, degree-dependent)\n", "- Components: CPJastrowEN, CPJastrowEE, CPJastrowEEN\n", "\n", "### Transcorrelated Theory\n", "Both approaches implement the transcorrelated wavefunction:\n", "$$\\Psi_{TC}(R) = J(R) \\times \\Psi_{Slater}(R)$$\n", "where $J(R) = \\exp(u(R))$ and the local energy includes Jastrow contributions:\n", "$$E_L = \\frac{\\bar{H}\\Psi_{Slater}}{\\Psi_{Slater}} = E_{kinetic} + E_{potential} + \\nabla u \\cdot \\nabla + \\frac{1}{2}|\\nabla u|^2 + \\frac{1}{2}\\nabla^2 u$$"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Libraries imported successfully!\n", "JAX version: 0.5.3\n", "JAX devices: [CpuDevice(id=0)]\n"]}], "source": ["# Setup and imports\n", "import sys\n", "import os\n", "import time\n", "import numpy as np\n", "import jax\n", "import jax.numpy as jnp\n", "from jax import random\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from tqdm import tqdm\n", "\n", "# Enable float64 precision for quantum chemistry calculations\n", "jax.config.update(\"jax_enable_x64\", True)\n", "\n", "# Add PyTC to path\n", "pytc_path = os.path.abspath('../../repos/pytc')\n", "if pytc_path not in sys.path:\n", "    sys.path.insert(0, pytc_path)\n", "\n", "# PySCF for quantum chemistry\n", "from pyscf import gto, scf\n", "\n", "# PyTC imports - Neural Jastrow\n", "from pytc.autodiff.jastrow import (\n", "    NeuralEN, NeuralEE, NeuralEEN, \n", "    CPJastrowEN, CPJastrowEE, CPJastrowEEN,\n", "    NuclearCusp, CompositeJastrow\n", ")\n", "from pytc.autodiff.ansatz.sj import SlaterJastrow\n", "from pytc.autodiff.ansatz.det import SlaterDet\n", "from pytc.autodiff.mcmc import sample, optimize_ref_var\n", "from pytc.autodiff.mcmc_utils import analyze_energies\n", "\n", "# Plotting setup\n", "plt.style.use('seaborn-v0_8')\n", "plt.rcParams['figure.dpi'] = 140\n", "plt.rcParams['savefig.dpi'] = 140\n", "sns.set_palette(\"husl\")\n", "\n", "print(\"Libraries imported successfully!\")\n", "print(f\"JAX version: {jax.__version__}\")\n", "print(f\"JAX devices: {jax.devices()}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Molecular System Setup\n", "\n", "We'll test on H₂ and HeH⁺ molecules to compare neural vs polynomial Jastrow factors."]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["H2 molecule:\n", "  Number of electrons: 2\n", "  Number of atoms: 2\n", "  HF energy: -1.116714 Hartree\n", "HeH+ molecule:\n", "  Number of electrons: 2\n", "  Number of atoms: 2\n", "  HF energy: -2.841816 Hartree\n"]}], "source": ["def setup_molecule(molecule_name):\n", "    \"\"\"Setup molecular systems for comparison.\"\"\"\n", "    if molecule_name == \"H2\":\n", "        mol = gto.M(atom='H 0 0 0; H 0 0 1.4', basis='sto-3g', unit='bohr', verbose=0)\n", "    elif molecule_name == \"HeH+\":\n", "        mol = gto.M(atom='He 0 0 0; H 0 0 1.463', basis='sto-3g', unit='bohr', charge=1, verbose=0)\n", "    else:\n", "        raise ValueError(f\"Unknown molecule: {molecule_name}\")\n", "    \n", "    # Run HF calculation\n", "    mf = scf.RHF(mol)\n", "    mf.kernel()\n", "    \n", "    print(f\"{molecule_name} molecule:\")\n", "    print(f\"  Number of electrons: {mol.nelectron}\")\n", "    print(f\"  Number of atoms: {mol.natm}\")\n", "    print(f\"  HF energy: {mf.e_tot:.6f} Hartree\")\n", "    \n", "    return mol, mf\n", "\n", "# Setup test molecules\n", "mol_h2, mf_h2 = setup_molecule(\"H2\")\n", "mol_heh, mf_heh = setup_molecule(\"HeH+\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Parameter Count Analysis\n", "\n", "Let's compare the number of parameters for equivalent neural and polynomial Jastrow factors."]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== Parameter Count Analysis for H2 ===\n", "  Neural EN (8,8): 0 parameters\n", "  Neural EE (8,8): 0 parameters\n", "  Neural EEN (16,16): 0 parameters\n", "  CP EN (deg=3, rank=8): 57 parameters\n", "  CP EE (deg=3, rank=8): 33 parameters\n", "  CP EEN (deg=3, rank=8): 129 parameters\n", "  CP EEN sparse_LU (deg=3, rank=8): 249 parameters\n", "  CP EEN sparse_degree (deg=3, rank=8): 249 parameters\n", "\n", "=== Parameter Count Analysis for HeH+ ===\n", "  Neural EN (8,8): 0 parameters\n", "  Neural EE (8,8): 0 parameters\n", "  Neural EEN (16,16): 0 parameters\n", "  CP EN (deg=3, rank=8): 57 parameters\n", "  CP EE (deg=3, rank=8): 33 parameters\n", "  CP EEN (deg=3, rank=8): 129 parameters\n", "  CP EEN sparse_LU (deg=3, rank=8): 249 parameters\n", "  CP EEN sparse_degree (deg=3, rank=8): 249 parameters\n"]}], "source": ["def count_parameters(params):\n", "    \"\"\"Count total parameters in a parameter structure.\"\"\"\n", "    if isinstance(params, dict):\n", "        total = 0\n", "        for key, value in params.items():\n", "            if key == 'net_vars':  # Neural network parameters\n", "                total += count_parameters(value)\n", "            elif key == 'U':  # CP U matrices\n", "                total += sum(u.size for u in value)\n", "            elif hasattr(value, 'size'):  # JAX array\n", "                total += value.size\n", "            elif isinstance(value, (list, tuple)):\n", "                total += sum(count_parameters(v) for v in value)\n", "        return total\n", "    <PERSON><PERSON> has<PERSON>(params, 'size'):  # JAX array\n", "        return params.size\n", "    elif isinstance(params, (list, tuple)):\n", "        return sum(count_parameters(p) for p in params)\n", "    else:\n", "        return 0\n", "\n", "def analyze_parameter_counts(mol, molecule_name):\n", "    \"\"\"Analyze parameter counts for different Jastrow configurations.\"\"\"\n", "    print(f\"\\n=== Parameter Count Analysis for {molecule_name} ===\")\n", "    \n", "    key = random.<PERSON><PERSON><PERSON><PERSON>(42)\n", "    results = {}\n", "    \n", "    # Neural Jastrow configurations\n", "    neural_configs = [\n", "        (\"Neural EN (8,8)\", lambda: NeuralEN(mol, layer_widths=[8, 8])),\n", "        (\"Neural EE (8,8)\", lambda: NeuralEE(mol, layer_widths=[8, 8])),\n", "        (\"Neural EEN (16,16)\", lambda: NeuralEEN(mol, layer_widths=[16, 16])),\n", "    ]\n", "    \n", "    # Polynomial Jastrow configurations\n", "    poly_configs = [\n", "        (\"CP EN (deg=3, rank=8)\", lambda: CPJastrowEN(mol, degree=3, rank=8)),\n", "        (\"CP EE (deg=3, rank=8)\", lambda: CPJastrowEE(mol, degree=3, rank=8)),\n", "        (\"CP EEN (deg=3, rank=8)\", lambda: CPJastrowEEN(mol, degree=3, rank=8)),\n", "        (\"CP EEN sparse_LU (deg=3, rank=8)\", lambda: CPJastrowEEN(mol, degree=3, rank=8, cp_variant='CP_sparse_LU')),\n", "        (\"CP EEN sparse_degree (deg=3, rank=8)\", lambda: CPJastrowEEN(mol, degree=3, rank=8, cp_variant='CP_sparse_degree')),\n", "    ]\n", "    \n", "    all_configs = neural_configs + poly_configs\n", "    \n", "    for name, jastrow_fn in all_configs:\n", "        jastrow = jastrow_fn()\n", "        params = jastrow.init_params(key=key)\n", "        param_count = count_parameters(params)\n", "        results[name] = param_count\n", "        print(f\"  {name}: {param_count} parameters\")\n", "    \n", "    return results\n", "\n", "# Analyze parameter counts\n", "h2_param_counts = analyze_parameter_counts(mol_h2, \"H2\")\n", "heh_param_counts = analyze_parameter_counts(mol_heh, \"HeH+\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Performance Benchmarking\n", "\n", "Compare evaluation speed and memory usage between neural and polynomial Jastrow factors."]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== Performance Benchmarks for H2 ===\n", "  Neural EE (8,8):\n", "    Evaluation: 54.62 μs\n", "    Gradients:  66.17 μs\n", "    Parameters: 0\n", "  <PERSON><PERSON><PERSON> (16,16):\n", "    Evaluation: 52.55 μs\n", "    Gradients:  68.53 μs\n", "    Parameters: 0\n", "  CP EE (deg=3, rank=8):\n", "    Evaluation: 50.88 μs\n", "    Gradients:  57.24 μs\n", "    Parameters: 33\n", "  CP EEN (deg=3, rank=8):\n", "    Evaluation: 52.17 μs\n", "    Gradients:  61.96 μs\n", "    Parameters: 129\n", "  CP EEN sparse_LU:\n", "    Evaluation: 52.68 μs\n", "    Gradients:  85.44 μs\n", "    Parameters: 249\n", "\n", "=== Performance Benchmarks for HeH+ ===\n", "  Neural EE (8,8):\n", "    Evaluation: 51.43 μs\n", "    Gradients:  62.66 μs\n", "    Parameters: 0\n", "  <PERSON><PERSON><PERSON> (16,16):\n", "    Evaluation: 51.51 μs\n", "    Gradients:  81.51 μs\n", "    Parameters: 0\n", "  CP EE (deg=3, rank=8):\n", "    Evaluation: 52.19 μs\n", "    Gradients:  61.66 μs\n", "    Parameters: 33\n", "  CP EEN (deg=3, rank=8):\n", "    Evaluation: 51.68 μs\n", "    Gradients:  72.22 μs\n", "    Parameters: 129\n", "  CP EEN sparse_LU:\n", "    Evaluation: 51.07 μs\n", "    Gradients:  79.85 μs\n", "    Parameters: 249\n"]}], "source": ["def benchmark_jastrow_evaluation(jastrow, params, mol, n_evaluations=1000):\n", "    \"\"\"Benchmark Jastrow factor evaluation speed.\"\"\"\n", "    # Generate random electron positions\n", "    key = random.<PERSON><PERSON><PERSON><PERSON>(123)\n", "    keys = random.split(key, n_evaluations)\n", "    \n", "    r1_batch = random.normal(keys[0], (n_evaluations, 3)) * 0.5\n", "    r2_batch = random.normal(keys[1], (n_evaluations, 3)) * 0.5\n", "    \n", "    # Compile the function first\n", "    @jax.jit\n", "    def eval_jastrow(r1, r2):\n", "        return jastrow._compute(r1, r2, params)\n", "    \n", "    # Warm-up\n", "    _ = eval_jastrow(r1_batch[0], r2_batch[0])\n", "    \n", "    # Benchmark\n", "    start_time = time.time()\n", "    for i in range(n_evaluations):\n", "        _ = eval_jastrow(r1_batch[i], r2_batch[i])\n", "    end_time = time.time()\n", "    \n", "    avg_time = (end_time - start_time) / n_evaluations * 1e6  # microseconds\n", "    return avg_time\n", "\n", "def benchmark_gradient_evaluation(jastrow, params, mol, n_evaluations=100):\n", "    \"\"\"Benchmark gradient and Laplacian evaluation speed.\"\"\"\n", "    key = random.<PERSON><PERSON><PERSON><PERSON>(456)\n", "    keys = random.split(key, n_evaluations)\n", "    \n", "    r1_batch = random.normal(keys[0], (n_evaluations, 3)) * 0.5\n", "    r2_batch = random.normal(keys[1], (n_evaluations, 3)) * 0.5\n", "    \n", "    # Compile the function first\n", "    @jax.jit\n", "    def eval_gradients(r1, r2):\n", "        return jastrow.get_log_grads_r1(r1, r2, params)\n", "    \n", "    # Warm-up\n", "    _ = eval_gradients(r1_batch[0], r2_batch[0])\n", "    \n", "    # Benchmark\n", "    start_time = time.time()\n", "    for i in range(n_evaluations):\n", "        _ = eval_gradients(r1_batch[i], r2_batch[i])\n", "    end_time = time.time()\n", "    \n", "    avg_time = (end_time - start_time) / n_evaluations * 1e6  # microseconds\n", "    return avg_time\n", "\n", "def run_performance_benchmarks(mol, molecule_name):\n", "    \"\"\"Run comprehensive performance benchmarks.\"\"\"\n", "    print(f\"\\n=== Performance Benchmarks for {molecule_name} ===\")\n", "    \n", "    key = random.<PERSON><PERSON><PERSON><PERSON>(789)\n", "    \n", "    # Test configurations\n", "    configs = [\n", "        (\"Neural EE (8,8)\", NeuralEE(mol, layer_widths=[8, 8])),\n", "        (\"Neural EEN (16,16)\", NeuralEEN(mol, layer_widths=[16, 16])),\n", "        (\"CP EE (deg=3, rank=8)\", CPJastrowEE(mol, degree=3, rank=8)),\n", "        (\"CP EEN (deg=3, rank=8)\", CPJastrowEEN(mol, degree=3, rank=8)),\n", "        (\"CP EEN sparse_LU\", CPJastrowEEN(mol, degree=3, rank=8, cp_variant='CP_sparse_LU')),\n", "    ]\n", "    \n", "    results = {}\n", "    \n", "    for name, jastrow in configs:\n", "        params = jastrow.init_params(key=key)\n", "        \n", "        # Benchmark evaluation\n", "        eval_time = benchmark_jastrow_evaluation(jastrow, params, mol)\n", "        \n", "        # Benchmark gradients\n", "        grad_time = benchmark_gradient_evaluation(jastrow, params, mol)\n", "        \n", "        # Parameter count\n", "        param_count = count_parameters(params)\n", "        \n", "        results[name] = {\n", "            'eval_time': eval_time,\n", "            'grad_time': grad_time,\n", "            'param_count': param_count\n", "        }\n", "        \n", "        print(f\"  {name}:\")\n", "        print(f\"    Evaluation: {eval_time:.2f} μs\")\n", "        print(f\"    Gradients:  {grad_time:.2f} μs\")\n", "        print(f\"    Parameters: {param_count}\")\n", "    \n", "    return results\n", "\n", "# Run performance benchmarks\n", "h2_performance = run_performance_benchmarks(mol_h2, \"H2\")\n", "heh_performance = run_performance_benchmarks(mol_heh, \"HeH+\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. VMC Energy Comparison\n", "\n", "Compare the accuracy and convergence of neural vs polynomial Jastrow factors in variational Monte Carlo calculations."]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== VMC Energy Comparison for H2 ===\n", "\n", "Testing Neural (16,16)...\n", "Electron distribution by atom:\n", "  Atom 0: 1 up, 0 down\n", "  Atom 1: 0 up, 1 down\n", "Initialized 1 up-spin and 1 down-spin electrons around 2 atoms\n", "Starting burn-in with 50 steps using importance sampling...\n", "Burn-in step 0/50, acceptance: 0.890625, time: 1.20s\n", "Burn-in complete.\n", "Starting production sampling...\n", "Step 0/200, Acceptance: 0.8790, Time/step: 1.50s\n", "  Current energy: -1.245015\n", "Step 100/200, Acceptance: 0.8786, Time/step: 0.98s\n", "  Current energy: -1.187824\n", "Step 199/200, Acceptance: 0.8854, Time/step: 0.82s\n", "  Current energy: -1.182851\n", "  Energy: -1.182851 ± 0.006415 Ha\n", "  HF diff: -0.066137 Ha\n", "  Acceptance: 0.881\n", "  Parameters: 3\n", "\n", "Testing CP (deg=3, rank=8)...\n", "Electron distribution by atom:\n", "  Atom 0: 1 up, 0 down\n", "  Atom 1: 0 up, 1 down\n", "Initialized 1 up-spin and 1 down-spin electrons around 2 atoms\n", "Starting burn-in with 50 steps using importance sampling...\n", "Burn-in step 0/50, acceptance: 0.8828125, time: 1.30s\n", "Burn-in complete.\n", "Starting production sampling...\n", "Step 0/200, Acceptance: 0.8762, Time/step: 0.84s\n", "  Current energy: -1.226195\n", "Step 100/200, Acceptance: 0.8745, Time/step: 0.59s\n", "  Current energy: -1.173165\n", "Step 199/200, Acceptance: 0.8807, Time/step: 0.52s\n", "  Current energy: -1.161528\n", "  Energy: -1.161528 ± 0.005916 Ha\n", "  HF diff: -0.044814 Ha\n", "  Acceptance: 0.877\n", "  Parameters: 222\n", "\n", "Testing CP sparse_LU (deg=3, rank=8)...\n", "Electron distribution by atom:\n", "  Atom 0: 1 up, 0 down\n", "  Atom 1: 0 up, 1 down\n", "Initialized 1 up-spin and 1 down-spin electrons around 2 atoms\n", "Starting burn-in with 50 steps using importance sampling...\n", "Burn-in step 0/50, acceptance: 0.8828125, time: 1.22s\n", "Burn-in complete.\n", "Starting production sampling...\n", "Step 0/200, Acceptance: 0.8761, Time/step: 0.78s\n", "  Current energy: -1.234028\n", "Step 100/200, Acceptance: 0.8740, Time/step: 0.57s\n", "  Current energy: -1.172742\n", "Step 199/200, Acceptance: 0.8805, Time/step: 0.50s\n", "  Current energy: -1.163314\n", "  Energy: -1.163314 ± 0.005913 Ha\n", "  HF diff: -0.046599 Ha\n", "  Acceptance: 0.877\n", "  Parameters: 414\n", "\n", "Testing CP pure poly (deg=3, rank=8)...\n", "Electron distribution by atom:\n", "  Atom 0: 1 up, 0 down\n", "  Atom 1: 0 up, 1 down\n", "Initialized 1 up-spin and 1 down-spin electrons around 2 atoms\n", "Starting burn-in with 50 steps using importance sampling...\n", "Burn-in step 0/50, acceptance: 0.8984375, time: 1.27s\n", "Burn-in complete.\n", "Starting production sampling...\n", "Step 0/200, Acceptance: 0.8912, Time/step: 0.79s\n", "  Current energy: -1.188964\n", "Step 100/200, Acceptance: 0.8893, Time/step: 0.56s\n", "  Current energy: -1.156240\n", "Step 199/200, Acceptance: 0.8929, Time/step: 0.48s\n", "  Current energy: -1.152663\n", "  Energy: -1.152663 ± 0.005697 Ha\n", "  HF diff: -0.035949 Ha\n", "  Acceptance: 0.891\n", "  Parameters: 222\n", "\n", "=== VMC Energy Comparison for HeH+ ===\n", "\n", "Testing Neural (16,16)...\n", "Electron distribution by atom:\n", "  Atom 0: 1 up, 1 down\n", "  Atom 1: 0 up, 0 down\n", "Initialized 1 up-spin and 1 down-spin electrons around 2 atoms\n", "Starting burn-in with 50 steps using importance sampling...\n", "Burn-in step 0/50, acceptance: 0.8046875, time: 2.10s\n", "Burn-in complete.\n", "Starting production sampling...\n", "Step 0/200, Acceptance: 0.7805, Time/step: 0.91s\n", "  Current energy: -2.655707\n", "Step 100/200, Acceptance: 0.7937, Time/step: 0.68s\n", "  Current energy: -2.819327\n", "Step 199/200, Acceptance: 0.7999, Time/step: 0.62s\n", "  Current energy: -2.813597\n", "  Energy: -2.813597 ± 0.020444 Ha\n", "  HF diff: 0.028219 Ha\n", "  Acceptance: 0.793\n", "  Parameters: 5\n", "\n", "Testing CP (deg=3, rank=8)...\n", "Electron distribution by atom:\n", "  Atom 0: 1 up, 1 down\n", "  Atom 1: 0 up, 0 down\n", "Initialized 1 up-spin and 1 down-spin electrons around 2 atoms\n", "Starting burn-in with 50 steps using importance sampling...\n", "Burn-in step 0/50, acceptance: 0.8046875, time: 1.50s\n", "Burn-in complete.\n", "Starting production sampling...\n", "Step 0/200, Acceptance: 0.7790, Time/step: 0.88s\n", "  Current energy: -2.539785\n", "Step 100/200, Acceptance: 0.7913, Time/step: 0.62s\n", "  Current energy: -2.856971\n", "Step 199/200, Acceptance: 0.7985, Time/step: 0.54s\n", "  Current energy: -2.831469\n", "  Energy: -2.831469 ± 0.019781 Ha\n", "  HF diff: 0.010347 Ha\n", "  Acceptance: 0.792\n", "  Parameters: 224\n", "\n", "Testing CP sparse_LU (deg=3, rank=8)...\n", "Electron distribution by atom:\n", "  Atom 0: 1 up, 1 down\n", "  Atom 1: 0 up, 0 down\n", "Initialized 1 up-spin and 1 down-spin electrons around 2 atoms\n", "Starting burn-in with 50 steps using importance sampling...\n", "Burn-in step 0/50, acceptance: 0.8046875, time: 1.58s\n", "Burn-in complete.\n", "Starting production sampling...\n", "Step 0/200, Acceptance: 0.7793, Time/step: 0.90s\n", "  Current energy: -2.533356\n", "Step 100/200, Acceptance: 0.7909, Time/step: 0.63s\n", "  Current energy: -2.861074\n", "Step 199/200, Acceptance: 0.7980, Time/step: 0.54s\n", "  Current energy: -2.846777\n", "  Energy: -2.846777 ± 0.020023 Ha\n", "  HF diff: -0.004962 Ha\n", "  Acceptance: 0.791\n", "  Parameters: 416\n", "\n", "Testing CP pure poly (deg=3, rank=8)...\n", "Electron distribution by atom:\n", "  Atom 0: 1 up, 1 down\n", "  Atom 1: 0 up, 0 down\n", "Initialized 1 up-spin and 1 down-spin electrons around 2 atoms\n", "Starting burn-in with 50 steps using importance sampling...\n", "Burn-in step 0/50, acceptance: 0.828125, time: 1.40s\n", "Burn-in complete.\n", "Starting production sampling...\n", "Step 0/200, Acceptance: 0.7955, Time/step: 0.91s\n", "  Current energy: -2.694484\n", "Step 100/200, Acceptance: 0.8081, Time/step: 0.63s\n", "  Current energy: -2.807833\n", "Step 199/200, Acceptance: 0.8137, Time/step: 0.58s\n", "  Current energy: -2.817083\n", "  Energy: -2.817083 ± 0.018854 Ha\n", "  HF diff: 0.024732 Ha\n", "  Acceptance: 0.808\n", "  Parameters: 224\n"]}], "source": ["def create_jastrow_ansatz(mol, mf, jastrow_type, **kwargs):\n", "    \"\"\"Create a complete Slater-Jastrow ansatz with specified Jastrow type.\"\"\"\n", "    key = random.<PERSON><PERSON><PERSON><PERSON>(42)\n", "    \n", "    # Always include nuclear cusp for stability\n", "    ncusp = NuclearCusp(mol)\n", "    params_ncusp = ncusp.init_params()\n", "    \n", "    if jastrow_type == \"neural\":\n", "        # Neural Jastrow components\n", "        layer_widths = kwargs.get('layer_widths', [16, 16])\n", "        neural_en = NeuralEN(mol, layer_widths=layer_widths)\n", "        neural_ee = NeuralEE(mol, layer_widths=layer_widths)\n", "        neural_een = NeuralEEN(mol, layer_widths=layer_widths)\n", "        \n", "        params_en = neural_en.init_params(key=key)\n", "        params_ee = neural_ee.init_params(key=key)\n", "        params_een = neural_een.init_params(key=key)\n", "        \n", "        composite = CompositeJastrow([ncusp, neural_en, neural_ee, neural_een])\n", "        composite_params = [params_ncusp, params_en, params_ee, params_een]\n", "        \n", "    elif j<PERSON>_type == \"polynomial\":\n", "        # Polynomial Jastrow components\n", "        degree = kwargs.get('degree', 3)\n", "        rank = kwargs.get('rank', 8)\n", "        cp_variant = kwargs.get('cp_variant', 'CP')\n", "        activation_scale = kwargs.get('activation_scale', 0.05)\n", "        \n", "        cp_en = CPJastrowEN(mol, degree=degree, rank=rank, cp_variant=cp_variant, activation_scale=activation_scale)\n", "        cp_ee = CPJastrowEE(mol, degree=degree, rank=rank, cp_variant=cp_variant, activation_scale=activation_scale)\n", "        cp_een = CPJastrowEEN(mol, degree=degree, rank=rank, cp_variant=cp_variant, activation_scale=activation_scale)\n", "        \n", "        params_en = cp_en.init_params(key=key)\n", "        params_ee = cp_ee.init_params(key=key)\n", "        params_een = cp_een.init_params(key=key)\n", "        \n", "        composite = CompositeJastrow([ncusp, cp_en, cp_ee, cp_een])\n", "        composite_params = [params_ncusp, params_en, params_ee, params_een]\n", "        \n", "    else:\n", "        raise ValueError(f\"Unknown jastrow_type: {jastrow_type}\")\n", "    \n", "    # C<PERSON> Slater-<PERSON><PERSON><PERSON>\n", "    det = SlaterDet(mol, mf.mo_coeff)\n", "    sj_ansatz = SlaterJastrow(mol, composite, [det])\n", "    \n", "    # Prepare parameters for VMC\n", "    combined_params = (composite_params, jnp.array([1.0]))\n", "    \n", "    return sj_ansatz, combined_params, composite_params\n", "\n", "def run_vmc_comparison(mol, mf, molecule_name):\n", "    \"\"\"Run VMC comparison between neural and polynomial Jastrow factors.\"\"\"\n", "    print(f\"\\n=== VMC Energy Comparison for {molecule_name} ===\")\n", "    \n", "    # VMC parameters\n", "    vmc_params = {\n", "        'n_walkers': 128,\n", "        'n_steps': 200,\n", "        'step_size': 0.02,\n", "        'burn_in_steps': 50,\n", "        'use_importance_sampling': True,\n", "        'thinning': 2,\n", "    }\n", "    \n", "    results = {}\n", "    \n", "    # Test configurations\n", "    configs = [\n", "        (\"Neural (16,16)\", \"neural\", {'layer_widths': [16, 16]}),\n", "        (\"CP (deg=3, rank=8)\", \"polynomial\", {'degree': 3, 'rank': 8}),\n", "        (\"CP sparse_LU (deg=3, rank=8)\", \"polynomial\", {'degree': 3, 'rank': 8, 'cp_variant': 'CP_sparse_LU'}),\n", "        (\"CP pure poly (deg=3, rank=8)\", \"polynomial\", {'degree': 3, 'rank': 8, 'activation_scale': 1.0, 'use_final_activation': False}),\n", "    ]\n", "    \n", "    for name, jastrow_type, kwargs in configs:\n", "        print(f\"\\nTesting {name}...\")\n", "        \n", "        try:\n", "            # Create ansatz\n", "            sj_ansatz, combined_params, _ = create_jastrow_ansatz(mol, mf, jastrow_type, **kwargs)\n", "            \n", "            # Run VMC sampling\n", "            key = random.<PERSON><PERSON><PERSON><PERSON>(123)\n", "            vmc_results = sample(\n", "                sj_an<PERSON><PERSON>,\n", "                params=combined_params,\n", "                key=key,\n", "                **vmc_params\n", "            )\n", "            \n", "            # Analyze results\n", "            stats = analyze_energies(vmc_results)\n", "            energy = float(stats['mean'])\n", "            error = float(stats['error'])\n", "            acceptance = float(jnp.mean(vmc_results['acceptance_rates']))\n", "            \n", "            # Parameter count\n", "            param_count = count_parameters(combined_params)\n", "            \n", "            results[name] = {\n", "                'energy': energy,\n", "                'error': error,\n", "                'acceptance': acceptance,\n", "                'param_count': param_count,\n", "                'energy_diff': energy - mf.e_tot\n", "            }\n", "            \n", "            print(f\"  Energy: {energy:.6f} ± {error:.6f} Ha\")\n", "            print(f\"  HF diff: {energy - mf.e_tot:.6f} Ha\")\n", "            print(f\"  Acceptance: {acceptance:.3f}\")\n", "            print(f\"  Parameters: {param_count}\")\n", "            \n", "        except Exception as e:\n", "            print(f\"  Error: {e}\")\n", "            results[name] = None\n", "    \n", "    return results\n", "\n", "# Run VMC comparisons\n", "h2_vmc_results = run_vmc_comparison(mol_h2, mf_h2, \"H2\")\n", "heh_vmc_results = run_vmc_comparison(mol_heh, mf_heh, \"HeH+\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Visualization and Analysis\n", "\n", "Create comprehensive plots comparing the different approaches."]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/var/folders/jq/mmntqs2d7wq778myd68k_jsh0000gp/T/ipykernel_35917/359859049.py:31: UserWarning: Glyph 8322 (\\N{SUBSCRIPT TWO}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/var/folders/jq/mmntqs2d7wq778myd68k_jsh0000gp/T/ipykernel_35917/359859049.py:31: UserWarning: Glyph 8314 (\\N{SUPERSCRIPT PLUS SIGN}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/Users/<USER>/miniconda3/envs/quantum/lib/python3.13/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 8322 (\\N{SUBSCRIPT TWO}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/miniconda3/envs/quantum/lib/python3.13/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 8314 (\\N{SUPERSCRIPT PLUS SIGN}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1680x700 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/jq/mmntqs2d7wq778myd68k_jsh0000gp/T/ipykernel_35917/359859049.py:82: UserWarning: Glyph 8322 (\\N{SUBSCRIPT TWO}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/var/folders/jq/mmntqs2d7wq778myd68k_jsh0000gp/T/ipykernel_35917/359859049.py:82: UserWarning: Glyph 8314 (\\N{SUPERSCRIPT PLUS SIGN}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/Users/<USER>/miniconda3/envs/quantum/lib/python3.13/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 8322 (\\N{SUBSCRIPT TWO}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/miniconda3/envs/quantum/lib/python3.13/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 8314 (\\N{SUPERSCRIPT PLUS SIGN}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1680x1120 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/jq/mmntqs2d7wq778myd68k_jsh0000gp/T/ipykernel_35917/359859049.py:123: UserWarning: Glyph 8322 (\\N{SUBSCRIPT TWO}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/var/folders/jq/mmntqs2d7wq778myd68k_jsh0000gp/T/ipykernel_35917/359859049.py:123: UserWarning: Glyph 8314 (\\N{SUPERSCRIPT PLUS SIGN}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/Users/<USER>/miniconda3/envs/quantum/lib/python3.13/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 8322 (\\N{SUBSCRIPT TWO}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/miniconda3/envs/quantum/lib/python3.13/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 8314 (\\N{SUPERSCRIPT PLUS SIGN}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1960x840 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["def plot_parameter_efficiency(h2_results, heh_results):\n", "    \"\"\"Plot parameter count comparison.\"\"\"\n", "    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))\n", "    \n", "    # H2 parameter counts\n", "    names = list(h2_results.keys())\n", "    counts = list(h2_results.values())\n", "    \n", "    colors = ['lightcoral' if 'Neural' in name else 'lightblue' for name in names]\n", "    \n", "    ax1.bar(range(len(names)), counts, color=colors)\n", "    ax1.set_xticks(range(len(names)))\n", "    ax1.set_xticklabels([name.replace(' ', '\\n') for name in names], rotation=45, ha='right')\n", "    ax1.set_ylabel('Number of Parameters')\n", "    ax1.set_title('H₂ Parameter Counts')\n", "    ax1.grid(alpha=0.3)\n", "    \n", "    # HeH+ parameter counts\n", "    names = list(heh_results.keys())\n", "    counts = list(heh_results.values())\n", "    \n", "    colors = ['lightcoral' if 'Neural' in name else 'lightblue' for name in names]\n", "    \n", "    ax2.bar(range(len(names)), counts, color=colors)\n", "    ax2.set_xticks(range(len(names)))\n", "    ax2.set_xticklabels([name.replace(' ', '\\n') for name in names], rotation=45, ha='right')\n", "    ax2.set_ylabel('Number of Parameters')\n", "    ax2.set_title('HeH⁺ Parameter Counts')\n", "    ax2.grid(alpha=0.3)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "def plot_performance_comparison(h2_perf, heh_perf):\n", "    \"\"\"Plot performance comparison.\"\"\"\n", "    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(12, 8))\n", "    \n", "    # H2 evaluation times\n", "    names = list(h2_perf.keys())\n", "    eval_times = [h2_perf[name]['eval_time'] for name in names]\n", "    colors = ['lightcoral' if 'Neural' in name else 'lightblue' for name in names]\n", "    \n", "    ax1.bar(range(len(names)), eval_times, color=colors)\n", "    ax1.set_xticks(range(len(names)))\n", "    ax1.set_xticklabels([name.replace(' ', '\\n') for name in names], rotation=45, ha='right')\n", "    ax1.set_ylabel('Evaluation Time (μs)')\n", "    ax1.set_title('H₂ Evaluation Speed')\n", "    ax1.grid(alpha=0.3)\n", "    \n", "    # H2 gradient times\n", "    grad_times = [h2_perf[name]['grad_time'] for name in names]\n", "    \n", "    ax2.bar(range(len(names)), grad_times, color=colors)\n", "    ax2.set_xticks(range(len(names)))\n", "    ax2.set_xticklabels([name.replace(' ', '\\n') for name in names], rotation=45, ha='right')\n", "    ax2.set_ylabel('Gradient Time (μs)')\n", "    ax2.set_title('H₂ Gradient Speed')\n", "    ax2.grid(alpha=0.3)\n", "    \n", "    # HeH+ evaluation times\n", "    names = list(heh_perf.keys())\n", "    eval_times = [heh_perf[name]['eval_time'] for name in names]\n", "    colors = ['lightcoral' if 'Neural' in name else 'lightblue' for name in names]\n", "    \n", "    ax3.bar(range(len(names)), eval_times, color=colors)\n", "    ax3.set_xticks(range(len(names)))\n", "    ax3.set_xticklabels([name.replace(' ', '\\n') for name in names], rotation=45, ha='right')\n", "    ax3.set_ylabel('Evaluation Time (μs)')\n", "    ax3.set_title('HeH⁺ Evaluation Speed')\n", "    ax3.grid(alpha=0.3)\n", "    \n", "    # HeH+ gradient times\n", "    grad_times = [heh_perf[name]['grad_time'] for name in names]\n", "    \n", "    ax4.bar(range(len(names)), grad_times, color=colors)\n", "    ax4.set_xticks(range(len(names)))\n", "    ax4.set_xticklabels([name.replace(' ', '\\n') for name in names], rotation=45, ha='right')\n", "    ax4.set_ylabel('Gradient Time (μs)')\n", "    ax4.set_title('HeH⁺ Gradient Speed')\n", "    ax4.grid(alpha=0.3)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "def plot_vmc_energy_comparison(h2_vmc, heh_vmc, mf_h2, mf_heh):\n", "    \"\"\"Plot VMC energy comparison.\"\"\"\n", "    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 6))\n", "    \n", "    # H2 energies\n", "    valid_h2 = {k: v for k, v in h2_vmc.items() if v is not None}\n", "    names = list(valid_h2.keys())\n", "    energies = [valid_h2[name]['energy'] for name in names]\n", "    errors = [valid_h2[name]['error'] for name in names]\n", "    colors = ['lightcoral' if 'Neural' in name else 'lightblue' for name in names]\n", "    \n", "    x_pos = range(len(names))\n", "    ax1.bar(x_pos, energies, yerr=errors, capsize=5, color=colors, alpha=0.7)\n", "    ax1.axhline(y=mf_h2.e_tot, color='red', linestyle='--', label='HF Energy')\n", "    ax1.set_xticks(x_pos)\n", "    ax1.set_xticklabels([name.replace(' ', '\\n') for name in names], rotation=45, ha='right')\n", "    ax1.set_ylabel('Energy (Hartree)')\n", "    ax1.set_title('H₂ VMC Energies')\n", "    ax1.legend()\n", "    ax1.grid(alpha=0.3)\n", "    \n", "    # HeH+ energies\n", "    valid_heh = {k: v for k, v in heh_vmc.items() if v is not None}\n", "    names = list(valid_heh.keys())\n", "    energies = [valid_heh[name]['energy'] for name in names]\n", "    errors = [valid_heh[name]['error'] for name in names]\n", "    colors = ['lightcoral' if 'Neural' in name else 'lightblue' for name in names]\n", "    \n", "    x_pos = range(len(names))\n", "    ax2.bar(x_pos, energies, yerr=errors, capsize=5, color=colors, alpha=0.7)\n", "    ax2.axhline(y=mf_heh.e_tot, color='red', linestyle='--', label='HF Energy')\n", "    ax2.set_xticks(x_pos)\n", "    ax2.set_xticklabels([name.replace(' ', '\\n') for name in names], rotation=45, ha='right')\n", "    ax2.set_ylabel('Energy (Hartree)')\n", "    ax2.set_title('HeH⁺ VMC Energies')\n", "    ax2.legend()\n", "    ax2.grid(alpha=0.3)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "# Generate all plots\n", "plot_parameter_efficiency(h2_param_counts, heh_param_counts)\n", "plot_performance_comparison(h2_performance, heh_performance)\n", "plot_vmc_energy_comparison(h2_vmc_results, heh_vmc_results, mf_h2, mf_heh)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Summary and Analysis\n", "\n", "### Key Findings\n", "\n", "1. **Parameter Efficiency**: Polynomial Jastrow factors typically require fewer parameters than neural networks for similar expressiveness\n", "\n", "2. **Computational Performance**: CP decomposition can be faster for evaluation, especially with sparsity patterns\n", "\n", "3. **Mathematical Interpretability**: Polynomial correlations provide clearer physical insight into electron correlation effects\n", "\n", "4. **Training Stability**: Both approaches benefit from nuclear cusp corrections and bounded outputs\n", "\n", "5. **Accuracy**: Both neural and polynomial Jastrow factors can achieve similar accuracy with proper tuning\n", "\n", "### Recommendations\n", "\n", "- **For interpretability**: Use polynomial Jastrow factors with moderate degree (3-4)\n", "- **For flexibility**: Neural networks with residual connections\n", "- **For efficiency**: Sparse CP variants for large systems\n", "- **For stability**: Always include nuclear cusp corrections\n", "\n", "### Future Directions\n", "\n", "1. **Hybrid approaches**: Combine neural and polynomial components\n", "2. **Adaptive sparsity**: Learn optimal sparsity patterns during training\n", "3. **Physics-informed architectures**: Incorporate more physical constraints\n", "4. **Multi-scale correlations**: Different polynomial degrees for different distance scales"]}], "metadata": {"kernelspec": {"display_name": "quantum", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 4}