{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Comprehensive Molecular System Benchmarking Suite\n", "\n", "This notebook provides a comprehensive benchmarking framework for comparing all available neural network architectures on molecular quantum chemistry problems.\n", "\n", "## 🎯 **Models Included:**\n", "- **PolyGCNN variants**: Standard, CP-weighted, different degrees\n", "- **Baseline GCNN**: NetKet standard implementation\n", "- **GAT models**: MolecularGAT, PolyGAT from qpolynets\n", "- **Polynomial models**: CP, CP_sparse_LU, CP_sparse_degree\n", "- **Classical baselines**: RBM, Jastrow, Feed-forward networks\n", "\n", "## 🧪 **Molecular Systems:**\n", "- **H₂**: Hydrogen molecule (2 electrons, 4 orbitals)\n", "- **H₂O**: Water molecule (10 electrons, 6 orbitals)\n", "- **LiH**: Lithium hydride (4 electrons, 6 orbitals)\n", "- **BeH₂**: Beryllium hydride (6 electrons, 8 orbitals)\n", "\n", "## 🚀 **Features:**\n", "- Easy model and molecule swapping via comment/uncomment blocks\n", "- Fermionic Hilbert space handling\n", "- Molecular adjacency matrix generation\n", "- Chemical accuracy analysis (~1 mHa)\n", "- Comprehensive performance comparison plots"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["⚠️ qgat molecular utilities not available - using simplified molecular models\n", "✅ Imports successful!\n", "JAX devices: [CpuDevice(id=0)]\n", "NetKet version: 3.19.0\n", "QGAT molecular utilities: ❌\n"]}], "source": ["# ============================================================================\n", "# IMPORTS AND SETUP\n", "# ============================================================================\n", "\n", "import sys\n", "import os\n", "sys.path.append('..')\n", "\n", "import jax\n", "import jax.numpy as jnp\n", "import netket as nk\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import time\n", "from typing import Dict, List, Any, Tuple, Optional\n", "from flax import nnx\n", "\n", "# Import qpolynets models\n", "sys.path.append('/Users/<USER>/Projects/Quantum/qpolynets/src')\n", "from qpolynets.models import PolyGCNN, MolecularGAT, PolyGAT, Jastrow\n", "from qpolynets.layers.polynomial_layers import CP, CP_sparse_LU, CP_sparse_degree\n", "\n", "# Try to import qgat molecular utilities (fallback if not available)\n", "try:\n", "    sys.path.append('../../qgat')\n", "    from qgat.physics.molecular.hamiltonians import create_molecular_hamiltonian\n", "    from qgat.physics.molecular.utils import (\n", "        create_h2_adjacency, create_water_adjacency, \n", "        create_molecular_adjacency_from_positions\n", "    )\n", "    QGAT_AVAILABLE = True\n", "except ImportError:\n", "    print(\"⚠️ qgat molecular utilities not available - using simplified molecular models\")\n", "    QGAT_AVAILABLE = False\n", "\n", "# Set up plotting\n", "plt.style.use('default')\n", "plt.rcParams['figure.figsize'] = (14, 10)\n", "plt.rcParams['font.size'] = 12\n", "\n", "print(\"✅ Imports successful!\")\n", "print(f\"JAX devices: {jax.devices()}\")\n", "print(f\"NetKet version: {nk.__version__}\")\n", "print(f\"QGAT molecular utilities: {'✅' if QGAT_AVAILABLE else '❌'}\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🧪 Molecular System Configuration:\n", "   Molecule: H2\n", "   Atoms: 2\n", "   Electrons: 2\n", "   Orbitals: 4\n", "   VMC iterations: 200\n"]}], "source": ["# ============================================================================\n", "# MOLECULAR SYSTEM CONFIGURATION\n", "# ============================================================================\n", "\n", "# Choose molecular system to benchmark\n", "MOLECULE_CONFIG = {\n", "    # Uncomment one of the following molecules:\n", "    \n", "    # Hydrogen molecule (default - simplest case)\n", "    'molecule': 'h2',\n", "    'n_atoms': 2,\n", "    'n_electrons': 2,\n", "    'n_orbitals': 4,\n", "    'bond_length': 0.74,  # <PERSON><PERSON><PERSON>\n", "    'basis_set': 'sto-3g',\n", "    'charge': 0,\n", "    'spin': 0,\n", "    \n", "    # # Water molecule\n", "    # 'molecule': 'h2o',\n", "    # 'n_atoms': 3,\n", "    # 'n_electrons': 10,\n", "    # 'n_orbitals': 6,\n", "    # 'bond_length': 0.96,  # O-H bond length\n", "    # 'basis_set': 'sto-3g',\n", "    # 'charge': 0,\n", "    # 'spin': 0,\n", "    \n", "    # # Lithium hydride\n", "    # 'molecule': 'lih',\n", "    # 'n_atoms': 2,\n", "    # 'n_electrons': 4,\n", "    # 'n_orbitals': 6,\n", "    # 'bond_length': 1.60,\n", "    # 'basis_set': 'sto-3g',\n", "    # 'charge': 0,\n", "    # 'spin': 0,\n", "}\n", "\n", "# VMC optimization parameters (adapted for molecular systems)\n", "VMC_CONFIG = {\n", "    'n_samples': 2048,  # Larger for molecular systems\n", "    'n_chains': 16,\n", "    'n_discard_per_chain': 20,\n", "    'learning_rate': 0.005,  # Lower for stability\n", "    'sr_diag_shift': 0.1,   # Higher for molecular systems\n", "    'n_iterations': 200,    # More iterations needed\n", "    'log_every': 20,\n", "}\n", "\n", "print(f\"🧪 Molecular System Configuration:\")\n", "print(f\"   Molecule: {MOLECULE_CONFIG['molecule'].upper()}\")\n", "print(f\"   Atoms: {MOLECULE_CONFIG['n_atoms']}\")\n", "print(f\"   Electrons: {MOLECULE_CONFIG['n_electrons']}\")\n", "print(f\"   Orbitals: {MOLECULE_CONFIG['n_orbitals']}\")\n", "print(f\"   VMC iterations: {VMC_CONFIG['n_iterations']}\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Using simplified molecular Hamiltonian\n", "\n", "🧪 Created H2 molecular system:\n", "   Hilbert space size: 4\n", "   Number of electrons: 2\n", "   Number of orbitals: 4\n", "   Molecular adjacency shape: (2, 2)\n", "   Exact ground state energy: -1.137200\n"]}], "source": ["# ============================================================================\n", "# MOLECULAR SYSTEM CREATION\n", "# ============================================================================\n", "\n", "def create_simple_molecular_hamiltonian(n_orbitals: int, n_electrons: int):\n", "    \"\"\"Create simplified molecular Hamiltonian when qgat is not available.\"\"\"\n", "    \n", "    # Create fermionic Hilbert space\n", "    hilbert = nk.hilbert.SpinOrbitalFermions(\n", "        n_orbitals=n_orbitals, \n", "        n_fermions=n_electrons\n", "    )\n", "    \n", "    # Simplified molecular Hamiltonian using <PERSON><PERSON> strings\n", "    operators = []\n", "    coefficients = []\n", "    \n", "    # Kinetic energy terms (hopping between adjacent orbitals)\n", "    for i in range(n_orbitals):\n", "        for j in range(n_orbitals):\n", "            if abs(i - j) == 1:  # Adjacent orbitals\n", "                op_str = ['I'] * n_orbitals\n", "                op_str[i] = 'X'\n", "                op_str[j] = 'X'\n", "                operators.append(''.join(op_str))\n", "                coefficients.append(-1.0)  # Kinetic energy\n", "    \n", "    # On-site energy terms\n", "    for i in range(n_orbitals):\n", "        op_str = ['I'] * n_orbitals\n", "        op_str[i] = 'Z'\n", "        operators.append(''.join(op_str))\n", "        coefficients.append(0.5)  # On-site energy\n", "    \n", "    # Coulomb interaction terms\n", "    for i in range(n_orbitals):\n", "        for j in range(i+1, n_orbitals):\n", "            op_str = ['I'] * n_orbitals\n", "            op_str[i] = 'Z'\n", "            op_str[j] = 'Z'\n", "            operators.append(''.join(op_str))\n", "            coefficients.append(0.25)  # Coulomb repulsion\n", "    \n", "    # Create Hamiltonian\n", "    hamiltonian = nk.operator.PauliStrings(hilbert, operators, coefficients)\n", "    \n", "    # Approximate exact energy (would need proper quantum chemistry calculation)\n", "    if MOLECULE_CONFIG['molecule'] == 'h2':\n", "        exact_energy = -1.1372  # Approximate H2 ground state\n", "    else:\n", "        exact_energy = None  # Unknown for other molecules\n", "    \n", "    return hilbert, hamiltonian, exact_energy\n", "\n", "def create_molecular_adjacency(molecule: str, n_atoms: int):\n", "    \"\"\"Create molecular adjacency matrix.\"\"\"\n", "    \n", "    if QGAT_AVAILABLE:\n", "        # Use qgat utilities if available\n", "        if molecule == 'h2':\n", "            return create_h2_adjacency()\n", "        elif molecule == 'h2o':\n", "            return create_water_adjacency()\n", "    \n", "    # Fallback: simple adjacency matrices\n", "    adjacency = jnp.zeros((n_atoms, n_atoms))\n", "    \n", "    if molecule == 'h2':\n", "        # H-<PERSON> bond\n", "        adjacency = adjacency.at[0, 1].set(1.0)\n", "        adjacency = adjacency.at[1, 0].set(1.0)\n", "    elif molecule == 'h2o':\n", "        # O-H bonds (O is atom 0, H atoms are 1,2)\n", "        adjacency = adjacency.at[0, 1].set(1.0)\n", "        adjacency = adjacency.at[1, 0].set(1.0)\n", "        adjacency = adjacency.at[0, 2].set(1.0)\n", "        adjacency = adjacency.at[2, 0].set(1.0)\n", "    elif molecule == 'lih':\n", "        # <PERSON><PERSON><PERSON> bond\n", "        adjacency = adjacency.at[0, 1].set(1.0)\n", "        adjacency = adjacency.at[1, 0].set(1.0)\n", "    else:\n", "        # Default: fully connected for unknown molecules\n", "        adjacency = jnp.ones((n_atoms, n_atoms)) - jnp.eye(n_atoms)\n", "    \n", "    return adjacency\n", "\n", "# Create molecular system\n", "if QGAT_AVAILABLE:\n", "    try:\n", "        # Use qgat molecular Hamiltonian\n", "        hilbert = nk.hilbert.SpinOrbitalFermions(\n", "            n_orbitals=MOLECULE_CONFIG['n_orbitals'],\n", "            n_fermions=MOLECULE_CONFIG['n_electrons']\n", "        )\n", "        hamiltonian, exact_energy = create_molecular_hamiltonian(\n", "            MOLECULE_CONFIG['molecule'], \n", "            hi<PERSON>,\n", "            bond_length=MOLECULE_CONFIG['bond_length']\n", "        )\n", "        print(\"✅ Using qgat molecular Hamiltonian\")\n", "    except Exception as e:\n", "        print(f\"⚠️ qgat Hamiltonian failed: {e}\")\n", "        print(\"   Falling back to simplified Hamiltonian\")\n", "        hilbert, hamiltonian, exact_energy = create_simple_molecular_hamiltonian(\n", "            MOLECULE_CONFIG['n_orbitals'], MOLECULE_CONFIG['n_electrons']\n", "        )\n", "else:\n", "    # Use simplified Hamiltonian\n", "    hilbert, hamiltonian, exact_energy = create_simple_molecular_hamiltonian(\n", "        MOLECULE_CONFIG['n_orbitals'], MOLECULE_CONFIG['n_electrons']\n", "    )\n", "    print(\"✅ Using simplified molecular Hamiltonian\")\n", "\n", "# Create molecular adjacency matrix\n", "molecular_adjacency = create_molecular_adjacency(\n", "    MOLECULE_CONFIG['molecule'], MOLECULE_CONFIG['n_atoms']\n", ")\n", "\n", "print(f\"\\n🧪 Created {MOLECULE_CONFIG['molecule'].upper()} molecular system:\")\n", "print(f\"   Hilbert space size: {hilbert.size}\")\n", "print(f\"   Number of electrons: {MOLECULE_CONFIG['n_electrons']}\")\n", "print(f\"   Number of orbitals: {MOLECULE_CONFIG['n_orbitals']}\")\n", "print(f\"   Molecular adjacency shape: {molecular_adjacency.shape}\")\n", "if exact_energy is not None:\n", "    print(f\"   Exact ground state energy: {exact_energy:.6f}\")\n", "else:\n", "    print(f\"   Exact energy: Not available\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "🧠 Created 4 molecular models for benchmarking\n"]}], "source": ["# ============================================================================\n", "# MOLECULAR MODEL DEFINITIONS\n", "# ============================================================================\n", "\n", "def create_molecular_models(hilbert, molecular_adjacency):\n", "    \"\"\"Create all molecular models for benchmarking.\"\"\"\n", "    models = {}\n", "    n_orbitals = hilbert.size\n", "    n_atoms = MOLECULE_CONFIG['n_atoms']\n", "    \n", "    # ========================================\n", "    # POLYGCNN MODELS (adapted for molecular systems)\n", "    # ========================================\n", "    \n", "    # Create a simple graph for PolyGCNN (chain of orbitals)\n", "    orbital_graph = nk.graph.Chain(length=n_orbitals, pbc=False)\n", "    \n", "    # Standard PolyGCNN (degree 2)\n", "    models['PolyGCNN_Std'] = PolyGCNN(\n", "        symmetries=orbital_graph,\n", "        layers=2,\n", "        features=(8, 16),  # Smaller for molecular systems\n", "        degree=2,\n", "        mode=\"fft\",\n", "        use_cp_weighted=False,\n", "        complex_output=False,\n", "        param_dtype=jnp.float64\n", "    )\n", "    \n", "    # PolyGCNN with CP weighting\n", "    models['PolyGCNN_CP'] = PolyGCNN(\n", "        symmetries=orbital_graph,\n", "        layers=2,\n", "        features=(8, 16),\n", "        degree=2,\n", "        mode=\"fft\",\n", "        use_cp_weighted=True,\n", "        rank=min(6, n_orbitals//2),\n", "        complex_output=False,\n", "        param_dtype=jnp.float64\n", "    )\n", "    \n", "    # Higher degree PolyGCNN\n", "    models['PolyGCNN_Deg3'] = PolyGCNN(\n", "        symmetries=orbital_graph,\n", "        layers=2,\n", "        features=(6, 12),  # Even smaller for higher degree\n", "        degree=3,\n", "        mode=\"fft\",\n", "        use_cp_weighted=True,\n", "        rank=min(4, n_orbitals//3),\n", "        complex_output=False,\n", "        param_dtype=jnp.float64\n", "    )\n", "    \n", "    # ========================================\n", "    # BASELINE GCNN (adapted for molecular systems)\n", "    # ========================================\n", "    \n", "    # Standard NetKet GCNN\n", "    models['GCNN_Baseline'] = nk.models.GCNN(\n", "        symmetries=orbital_graph,\n", "        layers=3,\n", "        features=(6, 8, 10),  # Parameter-matched for molecular systems\n", "        mode=\"fft\",\n", "        complex_output=False,\n", "        param_dtype=jnp.float64\n", "    )\n", "    \n", "    return models\n", "\n", "# Create molecular models\n", "models = create_molecular_models(hilbert, molecular_adjacency)\n", "print(f\"\\n🧠 Created {len(models)} molecular models for benchmarking\")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "📊 Final molecular model count: 4\n", "   - PolyGCNN_Std\n", "   - PolyGCNN_CP\n", "   - PolyGCNN_Deg3\n", "   - GCNN_Baseline\n"]}], "source": ["# ============================================================================\n", "# ADDITIONAL MOLECULAR MODELS (COMMENT/UNCOMMENT TO SELECT)\n", "# ============================================================================\n", "\n", "def add_additional_molecular_models(models, hilbert, molecular_adjacency):\n", "    \"\"\"Add additional molecular models - comment/uncomment as needed.\"\"\"\n", "    n_orbitals = hilbert.size\n", "    n_atoms = MOLECULE_CONFIG['n_atoms']\n", "    \n", "    # ========================================\n", "    # MOLECULAR GAT MODELS (Uncomment to include)\n", "    # ========================================\n", "    \n", "    # # MolecularGAT from qpolynets\n", "    # models['MolecularGAT'] = MolecularGAT(\n", "    #     n_atoms=n_atoms,\n", "    #     n_orbitals=n_orbitals,\n", "    #     molecular_adjacency=molecular_adjacency,\n", "    #     hidden_features=[16, 8],\n", "    #     n_heads=[4, 1],\n", "    #     rngs=nnx.Rngs(42)\n", "    # )\n", "    \n", "    # # PolyGAT adapted for molecules\n", "    # models['PolyGAT_Mol'] = PolyGAT(\n", "    #     n_nodes=n_atoms,\n", "    #     hidden_features=[12, 6],\n", "    #     degrees=[2, 2],\n", "    #     heads=[2, 1],\n", "    #     use_cp_weighted=True,\n", "    #     rank=min(4, n_atoms),\n", "    #     rngs=nnx.Rngs(43)\n", "    # )\n", "    \n", "    # ========================================\n", "    # POLYNOMIAL MODELS (Uncomment to include)\n", "    # ========================================\n", "    \n", "    # # Standard CP polynomial\n", "    # models['CP_Poly'] = CP(\n", "    #     degree=2,\n", "    #     input_dim=n_orbitals,\n", "    #     rank=min(6, n_orbitals//2),\n", "    #     output_dim=1,\n", "    #     param_dtype=jnp.complex128,\n", "    #     rngs=nnx.Rngs(44)\n", "    # )\n", "    \n", "    # # CP sparse LU (good for molecular systems)\n", "    # models['CP_SparseLU'] = CP_sparse_LU(\n", "    #     degree=2,\n", "    #     input_dim=n_orbitals,\n", "    #     rank=min(4, n_orbitals//3),\n", "    #     output_dim=1,\n", "    #     param_dtype=jnp.complex128,\n", "    #     rngs=nnx.Rngs(45)\n", "    # )\n", "    \n", "    # # CP sparse degree\n", "    # models['CP_SparseDeg'] = CP_sparse_degree(\n", "    #     degree=3,\n", "    #     input_dim=n_orbitals,\n", "    #     rank=min(3, n_orbitals//4),\n", "    #     output_dim=1,\n", "    #     param_dtype=jnp.complex128,\n", "    #     rngs=nnx.Rngs(46)\n", "    # )\n", "    \n", "    # ========================================\n", "    # CLASSICAL MOLECULAR BASELINES (Uncomment to include)\n", "    # ========================================\n", "    \n", "    # # RBM (not ideal for fermions but included for comparison)\n", "    # models['RBM'] = nk.models.RBM(\n", "    #     alpha=1,\n", "    #     param_dtype=jnp.float64\n", "    # )\n", "    \n", "    # # J<PERSON>row factor\n", "    # models['J<PERSON><PERSON>'] = J<PERSON><PERSON>(\n", "    #     hilbert_size=n_orbitals,\n", "    #     rngs=nnx.Rngs(47)\n", "    # )\n", "    \n", "    # ========================================\n", "    # MOLECULAR FEED-<PERSON><PERSON><PERSON><PERSON> NETWORKS (Uncomment to include)\n", "    # ========================================\n", "    \n", "    # # Molecular-specific feed-forward network\n", "    # class MolecularFFN(nnx.Module):\n", "    #     def __init__(self, n_orbitals: int, *, rngs: nnx.Rngs):\n", "    #         # Smaller networks for molecular systems\n", "    #         self.linear1 = nnx.Linear(\n", "    #             in_features=n_orbitals, \n", "    #             out_features=n_orbitals * 2, \n", "    #             dtype=jnp.complex128, \n", "    #             rngs=rngs\n", "    #         )\n", "    #         self.linear2 = nnx.Linear(\n", "    #             in_features=n_orbitals * 2, \n", "    #             out_features=n_orbitals, \n", "    #             dtype=jnp.complex128, \n", "    #             rngs=rngs\n", "    #         )\n", "    \n", "    #     def __call__(self, x: jax.Array):\n", "    #         x = self.linear1(x)\n", "    #         x = nk.nn.activation.log_cosh(x)\n", "    #         x = self.linear2(x)\n", "    #         x = nk.nn.activation.log_cosh(x)\n", "    #         return jnp.sum(x, axis=-1)\n", "    \n", "    # models['MolecularFFN'] = MolecularFFN(n_orbitals=n_orbitals, rngs=nnx.Rngs(48))\n", "    \n", "    return models\n", "\n", "# Add additional models (uncomment function calls as needed)\n", "models = add_additional_molecular_models(models, hilbert, molecular_adjacency)\n", "\n", "print(f\"\\n📊 Final molecular model count: {len(models)}\")\n", "for name in models.keys():\n", "    print(f\"   - {name}\")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Molecular VMC framework ready!\n"]}], "source": ["# ============================================================================\n", "# MOLECULAR VMC OPTIMIZATION FRAMEWORK\n", "# ============================================================================\n", "\n", "def setup_molecular_vmc(model, hamiltonian, hilbert, config):\n", "    \"\"\"Setup VMC calculation for molecular systems.\"\"\"\n", "    \n", "    # Use MetropolisExchange for fermionic systems (preserves particle number)\n", "    sampler = nk.sampler.MetropolisExchange(\n", "        hilbert=hilbert,\n", "        n_chains=config['n_chains'],\n", "        d_max=2  # Exchange up to 2nd nearest neighbors\n", "    )\n", "    \n", "    # Create variational state\n", "    vs = nk.vqs.MCState(\n", "        sampler=sampler,\n", "        model=model,\n", "        n_samples=config['n_samples'],\n", "        n_discard_per_chain=config['n_discard_per_chain'],\n", "        seed=42\n", "    )\n", "    \n", "    # Create optimizer (lower learning rate for molecular systems)\n", "    optimizer = nk.optimizer.Sgd(learning_rate=config['learning_rate'])\n", "    \n", "    # Create stochastic reconfiguration (higher diag_shift for stability)\n", "    try:\n", "        sample_params = nnx.state(model) if hasattr(model, '__dict__') else model.init(jax.random.PRNGKey(0), jnp.ones(hilbert.size))\n", "        is_complex = any(jnp.iscomplexobj(p) for p in jax.tree.leaves(sample_params))\n", "        sr = nk.optimizer.SR(\n", "            diag_shift=config['sr_diag_shift'],\n", "            holomorphic=is_complex\n", "        )\n", "    except:\n", "        sr = nk.optimizer.SR(diag_shift=config['sr_diag_shift'])\n", "    \n", "    # Create VMC driver\n", "    vmc = nk.VMC(\n", "        hamiltonian=hamiltonian,\n", "        optimizer=optimizer,\n", "        preconditioner=sr,\n", "        variational_state=vs\n", "    )\n", "    \n", "    return vmc, vs\n", "\n", "def run_molecular_vmc_optimization(model_name, model, hamiltonian, hilbert, config):\n", "    \"\"\"Run VMC optimization for molecular systems.\"\"\"\n", "    print(f\"\\n🧪 Optimizing {model_name} for {MOLECULE_CONFIG['molecule'].upper()}...\")\n", "    \n", "    try:\n", "        # Setup VMC\n", "        vmc, vs = setup_molecular_vmc(model, hamiltonian, hilbert, config)\n", "        \n", "        # Storage for results\n", "        energies = []\n", "        variances = []\n", "        iterations = []\n", "        \n", "        # Optimization loop with logging\n", "        start_time = time.time()\n", "        \n", "        for i in range(config['n_iterations']):\n", "            vmc.advance(1)\n", "            \n", "            if i % config['log_every'] == 0 or i == config['n_iterations'] - 1:\n", "                energy_stats = vs.expect(hamiltonian)\n", "                energy = energy_stats.mean.real\n", "                variance = energy_stats.variance.real\n", "                \n", "                energies.append(energy)\n", "                variances.append(variance)\n", "                iterations.append(i)\n", "                \n", "                error_str = \"\"\n", "                chemical_accuracy_str = \"\"\n", "                if exact_energy is not None:\n", "                    error = abs(energy - exact_energy)\n", "                    error_str = f\", Error: {error:.6f}\"\n", "                    \n", "                    # Check chemical accuracy (~1 mHa = 0.001 Hartree)\n", "                    if error < 0.001:\n", "                        chemical_accuracy_str = \" 🎯\"\n", "                    elif error < 0.01:\n", "                        chemical_accuracy_str = \" ✅\"\n", "                \n", "                print(f\"   Step {i:3d}: E = {energy:.6f}, Var = {variance:.6f}{error_str}{chemical_accuracy_str}\")\n", "        \n", "        end_time = time.time()\n", "        \n", "        # Final results\n", "        final_energy = energies[-1]\n", "        final_variance = variances[-1]\n", "        optimization_time = end_time - start_time\n", "        \n", "        results = {\n", "            'model_name': model_name,\n", "            'final_energy': final_energy,\n", "            'final_variance': final_variance,\n", "            'energies': energies,\n", "            'variances': variances,\n", "            'iterations': iterations,\n", "            'optimization_time': optimization_time,\n", "            'converged': True,\n", "            'error_message': None\n", "        }\n", "        \n", "        if exact_energy is not None:\n", "            final_error = abs(final_energy - exact_energy)\n", "            results['final_error'] = final_error\n", "            results['chemical_accuracy'] = final_error < 0.001  # 1 mHa\n", "        \n", "        print(f\"   ✅ Completed in {optimization_time:.1f}s\")\n", "        if exact_energy is not None and results.get('chemical_accuracy', False):\n", "            print(f\"   🎯 Achieved chemical accuracy!\")\n", "        \n", "        return results\n", "        \n", "    except Exception as e:\n", "        print(f\"   ❌ Failed: {e}\")\n", "        return {\n", "            'model_name': model_name,\n", "            'converged': <PERSON><PERSON><PERSON>,\n", "            'error_message': str(e),\n", "            'optimization_time': 0\n", "        }\n", "\n", "print(\"✅ Molecular VMC framework ready!\")"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "📊 Molecular Model Parameter Analysis:\n", "============================================================\n", "   PolyGCNN_Std        : Error - cannot reshape array of size 2 into shape (4,0,1)\n", "   PolyGCNN_CP         : Error - cannot reshape array of size 2 into shape (4,0,1)\n", "   PolyGCNN_Deg3       : Error - cannot reshape array of size 2 into shape (4,0,1)\n", "   GCNN_Baseline       : Error - cannot reshape array of size 2 into shape (4,0,1)\n"]}], "source": ["# ============================================================================\n", "# MOLECULAR PARAMETER ANALYSIS\n", "# ============================================================================\n", "\n", "def analyze_molecular_model_parameters(models, hilbert):\n", "    \"\"\"Analyze parameter counts for molecular models.\"\"\"\n", "    print(\"\\n📊 Molecular Model Parameter Analysis:\")\n", "    print(\"=\" * 60)\n", "    \n", "    param_counts = {}\n", "    test_input = jnp.ones((2, hilbert.size))  # Batch of 2 configurations\n", "    \n", "    for name, model in models.items():\n", "        try:\n", "            # Initialize parameters\n", "            if hasattr(model, 'init'):\n", "                params = model.init(jax.random.PRNGKey(0), test_input)\n", "                param_count = nk.jax.tree_size(params)\n", "            else:\n", "                # For nnx models\n", "                param_count = nk.jax.tree_size(nnx.state(model))\n", "            \n", "            param_counts[name] = param_count\n", "            \n", "            # Calculate parameter efficiency for molecular systems\n", "            params_per_orbital = param_count / hilbert.size\n", "            params_per_electron = param_count / MOLECULE_CONFIG['n_electrons']\n", "            \n", "            print(f\"   {name:20s}: {param_count:6d} params ({params_per_orbital:.1f}/orbital, {params_per_electron:.1f}/electron)\")\n", "            \n", "        except Exception as e:\n", "            print(f\"   {name:20s}: Error - {e}\")\n", "            param_counts[name] = 0\n", "    \n", "    return param_counts\n", "\n", "# Analyze molecular model parameters\n", "param_counts = analyze_molecular_model_parameters(models, hilbert)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "🚀 Starting molecular benchmark with 4 models...\n", "\n", "🏁 Starting Molecular Benchmark Suite for H2\n", "======================================================================\n", "\n", "========================= PolyGCNN_Std =========================\n", "\n", "🧪 Optimizing PolyGCNN_Std for H2...\n", "   ❌ Failed: You must either provide the list of exchange-clusters or a netket graph, from\n", "                              which clusters will be computed using the maximum distance d_max. \n", "   ❌ Failed: You must either provide the list of exchange-clusters or a netket graph, from\n", "                              which clusters will be computed using the maximum distance d_max. \n", "\n", "========================= PolyGCNN_CP =========================\n", "\n", "🧪 Optimizing PolyGCNN_CP for H2...\n", "   ❌ Failed: You must either provide the list of exchange-clusters or a netket graph, from\n", "                              which clusters will be computed using the maximum distance d_max. \n", "   ❌ Failed: You must either provide the list of exchange-clusters or a netket graph, from\n", "                              which clusters will be computed using the maximum distance d_max. \n", "\n", "========================= PolyGCNN_Deg3 =========================\n", "\n", "🧪 Optimizing PolyGCNN_Deg3 for H2...\n", "   ❌ Failed: You must either provide the list of exchange-clusters or a netket graph, from\n", "                              which clusters will be computed using the maximum distance d_max. \n", "   ❌ Failed: You must either provide the list of exchange-clusters or a netket graph, from\n", "                              which clusters will be computed using the maximum distance d_max. \n", "\n", "========================= GCNN_Baseline =========================\n", "\n", "🧪 Optimizing GCNN_Baseline for H2...\n", "   ❌ Failed: You must either provide the list of exchange-clusters or a netket graph, from\n", "                              which clusters will be computed using the maximum distance d_max. \n", "   ❌ Failed: You must either provide the list of exchange-clusters or a netket graph, from\n", "                              which clusters will be computed using the maximum distance d_max. \n"]}], "source": ["# ============================================================================\n", "# RUN MOLECULAR BENCHMARKING\n", "# ============================================================================\n", "\n", "def run_molecular_benchmark(models, hamiltonian, hilbert, config):\n", "    \"\"\"Run complete molecular benchmarking suite.\"\"\"\n", "    print(f\"\\n🏁 Starting Molecular Benchmark Suite for {MOLECULE_CONFIG['molecule'].upper()}\")\n", "    print(\"=\" * 70)\n", "    \n", "    all_results = {}\n", "    \n", "    for model_name, model in models.items():\n", "        print(f\"\\n{'='*25} {model_name} {'='*25}\")\n", "        \n", "        # Run molecular VMC optimization\n", "        result = run_molecular_vmc_optimization(\n", "            model_name, model, hamiltonian, hilbert, config\n", "        )\n", "        \n", "        all_results[model_name] = result\n", "        \n", "        # Print summary\n", "        if result['converged']:\n", "            print(f\"   Final Energy: {result['final_energy']:.6f} Hartree\")\n", "            if exact_energy is not None:\n", "                error = result.get('final_error', float('inf'))\n", "                print(f\"   Final Error:  {error:.6f} Hartree ({error*1000:.3f} mHa)\")\n", "                if result.get('chemical_accuracy', False):\n", "                    print(f\"   🎯 Chemical accuracy achieved!\")\n", "            print(f\"   Optimization time: {result['optimization_time']:.1f}s\")\n", "        else:\n", "            print(f\"   ❌ Failed: {result['error_message']}\")\n", "    \n", "    return all_results\n", "\n", "# Run the molecular benchmark\n", "print(f\"\\n🚀 Starting molecular benchmark with {len(models)} models...\")\n", "molecular_results = run_molecular_benchmark(models, hamiltonian, hilbert, VMC_CONFIG)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["❌ No successful molecular results to plot\n"]}], "source": ["# ============================================================================\n", "# MOLECULAR RESULTS ANALYSIS AND PLOTTING\n", "# ============================================================================\n", "\n", "def plot_molecular_benchmark_results(results, exact_energy=None, param_counts=None):\n", "    \"\"\"Create comprehensive molecular benchmark plots.\"\"\"\n", "    \n", "    # Filter successful results\n", "    successful_results = {k: v for k, v in results.items() if v['converged']}\n", "    \n", "    if not successful_results:\n", "        print(\"❌ No successful molecular results to plot\")\n", "        return\n", "    \n", "    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))\n", "    \n", "    # Colors for different model types\n", "    colors = plt.cm.tab10(np.linspace(0, 1, len(successful_results)))\n", "    \n", "    # Plot 1: Energy convergence\n", "    ax1.set_title(f\"Energy Convergence - {MOLECULE_CONFIG['molecule'].upper()} Molecule\")\n", "    for i, (name, result) in enumerate(successful_results.items()):\n", "        ax1.plot(result['iterations'], result['energies'], \n", "                label=name, color=colors[i], linewidth=2)\n", "    \n", "    if exact_energy is not None:\n", "        ax1.axhline(y=exact_energy, color='black', linestyle='--', \n", "                   linewidth=2, label='Exact')\n", "        # Add chemical accuracy band\n", "        ax1.axhspan(exact_energy - 0.001, exact_energy + 0.001, \n", "                   alpha=0.2, color='green', label='Chemical Accuracy (±1 mHa)')\n", "    \n", "    ax1.set_xlabel('Iteration')\n", "    ax1.set_ylabel('Energy (Hartree)')\n", "    ax1.legend()\n", "    ax1.grid(True, alpha=0.3)\n", "    \n", "    # Plot 2: Error convergence in mHa (if exact energy available)\n", "    if exact_energy is not None:\n", "        ax2.set_title('Error Convergence (Chemical Accuracy Scale)')\n", "        for i, (name, result) in enumerate(successful_results.items()):\n", "            errors_mha = [abs(e - exact_energy) * 1000 for e in result['energies']]  # Convert to mHa\n", "            ax2.semilogy(result['iterations'], errors_mha, \n", "                        label=name, color=colors[i], linewidth=2)\n", "        \n", "        # Chemical accuracy line\n", "        ax2.axhline(y=1.0, color='red', linestyle='--', \n", "                   linewidth=2, label='Chemical Accuracy (1 mHa)')\n", "        \n", "        ax2.set_xlabel('Iteration')\n", "        ax2.set_ylabel('Error (mHa)')\n", "        ax2.legend()\n", "        ax2.grid(True, alpha=0.3)\n", "    else:\n", "        ax2.text(0.5, 0.5, 'No exact energy\\navailable', \n", "                ha='center', va='center', transform=ax2.transAxes)\n", "        ax2.set_title('Error Analysis')\n", "    \n", "    # Plot 3: Final energies comparison\n", "    ax3.set_title(f'Final Energies - {MOLECULE_CONFIG[\"molecule\"].upper()}')\n", "    names = list(successful_results.keys())\n", "    final_energies = [successful_results[name]['final_energy'] for name in names]\n", "    \n", "    # Color bars based on chemical accuracy\n", "    bar_colors = []\n", "    for name in names:\n", "        if exact_energy is not None and successful_results[name].get('chemical_accuracy', False):\n", "            bar_colors.append('green')  # Chemical accuracy achieved\n", "        else:\n", "            bar_colors.append('lightblue')  # Standard\n", "    \n", "    bars = ax3.bar(range(len(names)), final_energies, color=bar_colors)\n", "    \n", "    if exact_energy is not None:\n", "        ax3.axhline(y=exact_energy, color='black', linestyle='--', \n", "                   linewidth=2, label='Exact')\n", "        ax3.axhspan(exact_energy - 0.001, exact_energy + 0.001, \n", "                   alpha=0.2, color='green', label='Chemical Accuracy')\n", "        ax3.legend()\n", "    \n", "    ax3.set_xticks(range(len(names)))\n", "    ax3.set_xticklabels(names, rotation=45, ha='right')\n", "    ax3.set_ylabel('Final Energy (Hartree)')\n", "    ax3.grid(True, alpha=0.3)\n", "    \n", "    # Plot 4: Parameter efficiency for molecular systems\n", "    if param_counts:\n", "        ax4.set_title('Parameter Efficiency (Molecular Systems)')\n", "        param_list = [param_counts.get(name, 0) for name in names]\n", "        \n", "        # Color points based on chemical accuracy\n", "        point_colors = []\n", "        for name in names:\n", "            if exact_energy is not None and successful_results[name].get('chemical_accuracy', False):\n", "                point_colors.append('green')\n", "            else:\n", "                point_colors.append('red')\n", "        \n", "        scatter = ax4.scatter(param_list, final_energies, \n", "                            c=point_colors, s=100, alpha=0.7)\n", "        \n", "        for i, name in enumerate(names):\n", "            ax4.annotate(name, (param_list[i], final_energies[i]), \n", "                        xytext=(5, 5), textcoords='offset points', fontsize=8)\n", "        \n", "        if exact_energy is not None:\n", "            ax4.axhline(y=exact_energy, color='black', linestyle='--', \n", "                       linewidth=2, label='Exact')\n", "            ax4.legend()\n", "        \n", "        ax4.set_xlabel('Number of Parameters')\n", "        ax4.set_ylabel('Final Energy (Hartree)')\n", "        ax4.grid(True, alpha=0.3)\n", "    else:\n", "        ax4.text(0.5, 0.5, 'Parameter counts\\nnot available', \n", "                ha='center', va='center', transform=ax4.transAxes)\n", "        ax4.set_title('Parameter Analysis')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # Molecular-specific summary table\n", "    print(f\"\\n📊 MOLECULAR BENCHMARK SUMMARY - {MOLECULE_CONFIG['molecule'].upper()}\")\n", "    print(\"=\" * 90)\n", "    print(f\"{'Model':<20} {'Energy (Ha)':<12} {'Error (mHa)':<12} {'Chem. Acc.':<12} {'Time (s)':<10} {'Params':<8}\")\n", "    print(\"-\" * 90)\n", "    \n", "    for name in names:\n", "        result = successful_results[name]\n", "        energy = result['final_energy']\n", "        time_taken = result['optimization_time']\n", "        params = param_counts.get(name, 0) if param_counts else 0\n", "        \n", "        error_str = \"N/A\"\n", "        chem_acc_str = \"N/A\"\n", "        if exact_energy is not None:\n", "            error_mha = abs(energy - exact_energy) * 1000\n", "            error_str = f\"{error_mha:.3f}\"\n", "            chem_acc_str = \"✅\" if result.get('chemical_accuracy', False) else \"❌\"\n", "        \n", "        print(f\"{name:<20} {energy:<12.6f} {error_str:<12} {chem_acc_str:<12} {time_taken:<10.1f} {params:<8d}\")\n", "    \n", "    if exact_energy is not None:\n", "        print(f\"\\nExact ground state energy: {exact_energy:.6f} Hartree\")\n", "        print(f\"Chemical accuracy threshold: 1.0 mHa (0.001 Hartree)\")\n", "        \n", "        # Count models achieving chemical accuracy\n", "        chem_acc_count = sum(1 for result in successful_results.values() \n", "                           if result.get('chemical_accuracy', False))\n", "        print(f\"Models achieving chemical accuracy: {chem_acc_count}/{len(successful_results)}\")\n", "\n", "# Generate molecular plots\n", "plot_molecular_benchmark_results(molecular_results, exact_energy, param_counts)"]}], "metadata": {"kernelspec": {"display_name": "quantum", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 4}