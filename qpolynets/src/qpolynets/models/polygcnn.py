"""
Polynomial GCNN models and factory (migrated minimal versions from qgat/core/polygcnn)
"""
import jax.numpy as jnp
import numpy as np
from flax import linen as nn
from typing import Any
from jax.nn.initializers import zeros, lecun_normal

from netket.utils import HashableArray
from netket.utils.types import NNInitFunc, Array
from netket.nn.activation import reim_selu
from netket.nn.symmetric_linear import DenseSymmFFT, DenseSymmMatrix
from netket.jax import logsumexp_cplx
from jax.scipy.special import logsumexp

from ..layers.poly_dense_equivariant import PolyDenseEquivariantFFT, PolyDenseEquivariantIrrep

# Initializers
default_gcnn_initializer = lecun_normal(in_axis=1, out_axis=0)

def identity(x):
    return x


class PolyGCNN_FFT(nn.Module):
    # Standard GCNN params
    symmetries: HashableArray
    product_table: HashableArray
    shape: tuple
    layers: int
    features: tuple
    characters: HashableArray

    # Polynomial params
    degree: int = 2
    poly_degrees: tuple | None = None

    # CP-style controls
    use_cp_weighted: bool = False
    rank: int | None = None

    # Control flags
    no_activation: bool = False
    poly_output: bool = False

    # Other params
    param_dtype: Any = np.float64
    activation: Any = reim_selu
    output_activation: Any = identity
    use_bias: bool = True
    precision: Any = None
    kernel_init: NNInitFunc = default_gcnn_initializer
    bias_init: NNInitFunc = zeros
    complex_output: bool = True
    equal_amplitudes: bool = False
    input_mask: Array = None
    hidden_mask: Array = None

    def setup(self):
        self.n_symm = np.asarray(self.symmetries).shape[0]
        self.dense_symm = DenseSymmFFT(
            space_group=self.symmetries,
            shape=self.shape,
            features=self.features[0],
            param_dtype=self.param_dtype,
            use_bias=self.use_bias,
            kernel_init=self.kernel_init,
            bias_init=self.bias_init,
            precision=self.precision,
            mask=self.input_mask,
        )
        self.poly_equivariant_layers = [
            PolyDenseEquivariantFFT(
                product_table=self.product_table,
                shape=self.shape,
                features=self.features[layer + 1],
                degree=(self.poly_degrees[layer] if self.poly_degrees is not None and layer < len(self.poly_degrees) else self.degree),
                use_bias=self.use_bias,
                param_dtype=self.param_dtype,
                precision=self.precision,
                kernel_init=self.kernel_init,
                bias_init=self.bias_init,
                mask=self.hidden_mask,
                use_cp_weighted=self.use_cp_weighted,
                rank=self.rank,
            )
            for layer in range(self.layers - 1)
        ]

    @nn.compact
    def __call__(self, x):
        if x.ndim < 3:
            x = jnp.expand_dims(x, -2)
        x = self.dense_symm(x)

        for layer in range(self.layers - 1):
            if not self.no_activation:
                x = self.activation(x)
            x = self.poly_equivariant_layers[layer](x)

        x = self.output_activation(x)

        if self.poly_output:
            return jnp.sum(x, axis=(-2, -1))

        if self.complex_output:
            x = logsumexp_cplx(x, axis=(-2, -1), b=jnp.asarray(self.characters))
        else:
            x = logsumexp(x, axis=(-2, -1), b=jnp.asarray(self.characters))

        if self.equal_amplitudes:
            return 1j * jnp.imag(x)
        else:
            return x


class PolyGCNN_Irrep(nn.Module):
    # Standard GCNN params
    symmetries: HashableArray
    irreps: tuple[HashableArray, ...]
    layers: int
    features: tuple
    characters: HashableArray

    # Polynomial params
    degree: int = 2
    poly_degrees: tuple | None = None

    # Control flags
    no_activation: bool = False
    poly_output: bool = False

    # Other params
    param_dtype: Any = np.float64
    activation: Any = reim_selu
    output_activation: Any = identity
    use_bias: bool = True
    precision: Any = None
    kernel_init: NNInitFunc = default_gcnn_initializer
    bias_init: NNInitFunc = zeros
    complex_output: bool = True
    equal_amplitudes: bool = False
    input_mask: Array = None
    hidden_mask: Array = None

    def setup(self):
        # Import from NetKet when needed
        from netket.nn.symmetric_linear import DenseSymmIrrep
        self.dense_symm = DenseSymmIrrep(
            irreps=self.irreps,
            features=self.features[0],
            param_dtype=self.param_dtype,
            use_bias=self.use_bias,
            kernel_init=self.kernel_init,
            bias_init=self.bias_init,
            precision=self.precision,
            mask=self.input_mask,
        )
        self.poly_equivariant_layers = [
            PolyDenseEquivariantIrrep(
                irreps=self.irreps,
                features=self.features[layer + 1],
                degree=(self.poly_degrees[layer] if self.poly_degrees is not None and layer < len(self.poly_degrees) else self.degree),
                use_bias=self.use_bias,
                param_dtype=self.param_dtype,
                precision=self.precision,
                kernel_init=self.kernel_init,
                bias_init=self.bias_init,
                mask=self.hidden_mask,
                use_cp_weighted=self.use_cp_weighted,
                rank=self.rank,
            )
            for layer in range(self.layers - 1)
        ]

    @nn.compact
    def __call__(self, x):
        if x.ndim < 3:
            x = jnp.expand_dims(x, -2)
        x = self.dense_symm(x)
        for layer in range(self.layers - 1):
            if not self.no_activation:
                x = self.activation(x)
            x = self.poly_equivariant_layers[layer](x)
        x = self.output_activation(x)
        if self.poly_output:
            return jnp.sum(x, axis=(-2, -1))
        if self.complex_output:
            x = logsumexp_cplx(x, axis=(-2, -1), b=jnp.asarray(self.characters))
        else:
            x = logsumexp(x, axis=(-2, -1), b=jnp.asarray(self.characters))
        if self.equal_amplitudes:
            return 1j * jnp.imag(x)
        else:
            return x


class PolyGCNN_Parity_FFT(nn.Module):
    # Same signature as qgat version (abridged)
    symmetries: HashableArray
    product_table: HashableArray
    shape: tuple
    layers: int
    features: tuple
    characters: HashableArray
    parity: int

    degree: int = 2
    poly_degrees: tuple | None = None

    # CP-style controls
    use_cp_weighted: bool = False
    rank: int | None = None

    # Control flags
    no_activation: bool = False
    poly_output: bool = False

    param_dtype: Any = np.float64
    activation: Any = reim_selu
    output_activation: Any = identity
    use_bias: bool = True
    precision: Any = None
    kernel_init: NNInitFunc = default_gcnn_initializer
    bias_init: NNInitFunc = zeros
    complex_output: bool = True
    equal_amplitudes: bool = False
    input_mask: Array = None
    hidden_mask: Array = None

    def setup(self):
        self.n_symm = np.asarray(self.symmetries).shape[0]
        self.dense_symm = DenseSymmFFT(
            space_group=self.symmetries,
            shape=self.shape,
            features=self.features[0],
            param_dtype=self.param_dtype,
            use_bias=self.use_bias,
            kernel_init=self.kernel_init,
            bias_init=self.bias_init,
            precision=self.precision,
            mask=self.input_mask,
        )
        self.poly_equivariant_layers = [
            PolyDenseEquivariantFFT(
                product_table=self.product_table,
                shape=self.shape,
                features=self.features[layer + 1],
                degree=(self.poly_degrees[layer] if self.poly_degrees is not None and layer < len(self.poly_degrees) else self.degree),
                use_bias=self.use_bias,
                param_dtype=self.param_dtype,
                precision=self.precision,
                kernel_init=self.kernel_init,
                bias_init=self.bias_init,
                mask=self.hidden_mask,
                use_cp_weighted=self.use_cp_weighted,
                rank=self.rank,
            )
            for layer in range(self.layers - 1)
        ]

    @nn.compact
    def __call__(self, x):
        if x.ndim < 3:
            x = jnp.expand_dims(x, -2)
        x = self.dense_symm(x)
        for layer in range(self.layers - 1):
            if not self.no_activation:
                x = self.activation(x)
            x = self.poly_equivariant_layers[layer](x)
        x = self.output_activation(x)
        x_parity = x[..., ::-1]
        x = x + self.parity * x_parity
        if self.poly_output:
            return jnp.sum(x, axis=(-2, -1))
        if self.complex_output:
            x = logsumexp_cplx(x, axis=(-2, -1), b=jnp.asarray(self.characters))
        else:
            x = logsumexp(x, axis=(-2, -1), b=jnp.asarray(self.characters))
        if self.equal_amplitudes:
            return 1j * jnp.imag(x)
        else:
            return x


class PolyGCNN_Parity_Irrep(nn.Module):
    # Placeholder for completeness; can be added later
    pass


def PolyGCNN(
    symmetries=None,
    product_table=None,
    irreps=None,
    degree=2,
    poly_degrees=None,
    no_activation=False,
    poly_output=False,
    parity=None,
    mode="auto",
    use_cp_weighted=False,
    rank=None,
    **gcnn_kwargs,
):
    """Factory for polynomial GCNN variants mirroring NetKet's GCNN."""
    import netket as nk
    from netket.utils import HashableArray

    # Resolve symmetries and space group
    if isinstance(symmetries, (nk.graph.Graph, nk.graph.Lattice)):
        sg = symmetries.space_group()
        shape = tuple(symmetries.extent)
    else:
        sg = nk.utils.group.PermutationGroup(symmetries)
        shape = gcnn_kwargs.get("shape")

    # characters
    characters = gcnn_kwargs.get("characters", HashableArray(np.ones(len(np.asarray(sg)))))
    gcnn_kwargs = {**gcnn_kwargs, "characters": characters, "shape": shape}

    if mode == "fft":
        if parity is not None:
            return PolyGCNN_Parity_FFT(
                symmetries=HashableArray(np.asarray(sg)),
                product_table=HashableArray(sg.product_table),
                degree=degree,
                poly_degrees=poly_degrees,
                no_activation=no_activation,
                poly_output=poly_output,
                parity=parity,
                **{**gcnn_kwargs, "use_cp_weighted": use_cp_weighted, "rank": rank},
            )
        else:
            return PolyGCNN_FFT(
                symmetries=HashableArray(np.asarray(sg)),
                product_table=HashableArray(sg.product_table),
                degree=degree,
                poly_degrees=poly_degrees,
                no_activation=no_activation,
                poly_output=poly_output,
                **{**gcnn_kwargs, "use_cp_weighted": use_cp_weighted, "rank": rank},
            )
    elif mode in ["irreps", "auto"] and irreps is not None:
        return PolyGCNN_Irrep(
            symmetries=HashableArray(np.asarray(sg)),
            irreps=tuple(HashableArray(irrep) for irrep in irreps),
            degree=degree,
            poly_degrees=poly_degrees,
            no_activation=no_activation,
            poly_output=poly_output,
            **{**gcnn_kwargs, "use_cp_weighted": use_cp_weighted, "rank": rank},
        )
    else:
        raise ValueError("Unsupported mode or missing irreps for PolyGCNN")

