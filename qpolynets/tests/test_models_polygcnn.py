import pytest
import jax
import jax.numpy as jnp
import numpy as np
import netket as nk

from qpolynets.models import PolyGCNN, PolyGCNN_FFT


def test_polygcnn_fft_forward():
    g = nk.graph.Hypercube(length=4, n_dim=1, pbc=True)
    model = PolyGCNN(symmetries=g, layers=2, features=(8, 16), degree=2, mode="fft")
    x = jnp.ones((2, g.n_nodes))
    params = model.init(jax.random.PRNGKey(0), x)
    y = model.apply(params, x)
    assert y.shape == (2,)


def test_polygcnn_fft_flags():
    g = nk.graph.Hypercube(length=4, n_dim=1, pbc=True)
    m = PolyGCNN_FFT(
        symmetries=g.space_group(),
        product_table=g.space_group().product_table,
        shape=tuple(g.extent),
        layers=2,
        features=(8, 16),
        characters=np.ones(len(np.asarray(g.space_group()))),
        degree=2,
        no_activation=True,
        poly_output=True,
    )
    x = jnp.ones((3, g.n_nodes))
    p = m.init(jax.random.PRNGKey(1), x)
    y = m.apply(p, x)
    assert y.shape == (3,)

