#!/usr/bin/env python3
"""
Test script to verify the fixed parameter counting functions work correctly.
"""

import sys
import os
import jax
import jax.numpy as jnp
from jax import random

# Enable float64 precision
jax.config.update("jax_enable_x64", True)

# Add PyTC to path
pytc_path = os.path.abspath('../../repos/pytc')
if pytc_path not in sys.path:
    sys.path.insert(0, pytc_path)

# PySCF for quantum chemistry
from pyscf import gto, scf

# PyTC imports
from pytc.autodiff.jastrow import CPJastrowEEN

# Import the fixed counting functions
from model_analysis import (
    count_parameters, count_nonzero_parameters, 
    count_effective_parameters, analyze_parameter_sparsity
)

def test_fixed_counting_functions():
    """Test that the fixed counting functions properly distinguish between CP and CP sparse_LU."""
    print("=== Testing Fixed Parameter Counting Functions ===")
    
    # Setup molecule
    mol = gto.M(atom='H 0 0 0; H 0 0 1.4', basis='cc-pvtz', unit='bohr', verbose=0)
    mf = scf.RHF(mol)
    mf.kernel()
    
    # Configuration
    POLY_DEGREE = 4
    POLY_RANK = 8
    
    print(f"Test configuration: degree={POLY_DEGREE}, rank={POLY_RANK}")
    
    # Create both variants
    print(f"\nCreating CP variants...")
    cp_standard = CPJastrowEEN(mol, degree=POLY_DEGREE, rank=POLY_RANK, cp_variant='CP')
    cp_sparse_lu = CPJastrowEEN(mol, degree=POLY_DEGREE, rank=POLY_RANK, cp_variant='CP_sparse_LU')
    
    # Initialize parameters
    key = random.PRNGKey(42)
    standard_params = cp_standard.init_params(key=key)
    sparse_params = cp_sparse_lu.init_params(key=key)
    
    print(f"\n=== Basic Parameter Counts ===")
    
    # Test 1: Total stored parameters should be the same
    standard_total = count_parameters(standard_params)
    sparse_total = count_parameters(sparse_params)
    
    print(f"CP standard total:     {standard_total}")
    print(f"CP sparse_LU total:    {sparse_total}")
    print(f"Same total count:      {standard_total == sparse_total} ✅" if standard_total == sparse_total else f"Different total count: ❌")
    
    # Test 2: Effective parameters should be different
    standard_effective = count_effective_parameters(standard_params, cp_standard)
    sparse_effective = count_effective_parameters(sparse_params, cp_sparse_lu)
    
    print(f"\n=== Effective Parameter Analysis ===")
    print(f"CP standard effective: {standard_effective['effective_parameters']}")
    print(f"CP sparse_LU effective: {sparse_effective['effective_parameters']}")
    print(f"Sparse has fewer:      {sparse_effective['effective_parameters'] < standard_effective['effective_parameters']} ✅" if sparse_effective['effective_parameters'] < standard_effective['effective_parameters'] else "Sparse should have fewer: ❌")
    
    # Test 3: Non-zero computational parameters
    standard_nonzero = count_nonzero_parameters(standard_params, cp_standard)
    sparse_nonzero = count_nonzero_parameters(sparse_params, cp_sparse_lu)
    
    print(f"\n=== Non-zero Computational Parameters ===")
    print(f"CP standard non-zero:  {standard_nonzero}")
    print(f"CP sparse_LU non-zero: {sparse_nonzero}")
    print(f"Sparse has fewer:      {sparse_nonzero < standard_nonzero} ✅" if sparse_nonzero < standard_nonzero else "Sparse should have fewer: ❌")
    
    # Test 4: Comprehensive analysis
    print(f"\n=== Comprehensive Analysis ===")
    
    standard_analysis = analyze_parameter_sparsity(standard_params, cp_standard, "CP Standard")
    sparse_analysis = analyze_parameter_sparsity(sparse_params, cp_sparse_lu, "CP Sparse_LU")
    
    print(f"\nCP Standard:")
    print(f"  Total stored:        {standard_analysis['total_stored_parameters']}")
    print(f"  Effective:           {standard_analysis['effective_parameters']}")
    print(f"  Computational non-zero: {standard_analysis['computational_nonzero']}")
    print(f"  Has sparsity:        {standard_analysis['has_sparsity']}")
    print(f"  Sparsity ratio:      {standard_analysis['overall_sparsity']:.3f}")
    
    print(f"\nCP Sparse_LU:")
    print(f"  Total stored:        {sparse_analysis['total_stored_parameters']}")
    print(f"  Effective:           {sparse_analysis['effective_parameters']}")
    print(f"  Computational non-zero: {sparse_analysis['computational_nonzero']}")
    print(f"  Has sparsity:        {sparse_analysis['has_sparsity']}")
    print(f"  Sparsity ratio:      {sparse_analysis['overall_sparsity']:.3f}")
    print(f"  Parameter reduction: {sparse_analysis['parameter_reduction']:.1f}%")
    print(f"  Number of masks:     {sparse_analysis['masks_count']}")
    
    # Test 5: Mask verification
    print(f"\n=== Mask Verification ===")
    print(f"Standard has masks:    {hasattr(cp_standard, 'masks') and cp_standard.masks is not None}")
    print(f"Sparse_LU has masks:   {hasattr(cp_sparse_lu, 'masks') and cp_sparse_lu.masks is not None}")
    print(f"Masks not in params:   {'masks' not in sparse_params} ✅" if 'masks' not in sparse_params else "Masks should not be in params: ❌")
    
    if hasattr(cp_sparse_lu, 'masks') and cp_sparse_lu.masks is not None:
        print(f"\nMask details:")
        for i, mask in enumerate(cp_sparse_lu.masks):
            nonzero = int(jnp.sum(mask != 0))
            total = mask.size
            sparsity = 1.0 - (nonzero / total)
            print(f"  Mask {i}: {nonzero}/{total} non-zero, sparsity={sparsity:.3f}")
    
    # Test 6: U matrix specific analysis
    if 'u_matrix_analysis' in sparse_analysis and sparse_analysis['u_matrix_analysis']:
        print(f"\n=== U Matrix Specific Analysis ===")
        for u_name, u_info in sparse_analysis['u_matrix_analysis'].items():
            print(f"{u_name}:")
            print(f"  Total elements:      {u_info['total_elements']}")
            print(f"  Stored non-zero:     {u_info['stored_nonzero']}")
            print(f"  Mask allows:         {u_info['mask_allows']}")
            print(f"  Effective non-zero:  {u_info['effective_nonzero']}")
            print(f"  Mask sparsity:       {u_info['mask_sparsity']:.3f}")
            print(f"  Effective sparsity:  {u_info['effective_sparsity']:.3f}")
    
    # Summary
    print(f"\n" + "="*60)
    print(f"SUMMARY")
    print(f"="*60)
    
    tests_passed = [
        standard_total == sparse_total,  # Same storage
        sparse_effective['effective_parameters'] < standard_effective['effective_parameters'],  # Different effective
        sparse_nonzero < standard_nonzero,  # Different computational
        sparse_analysis['has_sparsity'],  # Sparsity detected
        'masks' not in sparse_params  # Masks not in params
    ]
    
    print(f"✅ Same total stored parameters:     {tests_passed[0]}")
    print(f"✅ Sparse has fewer effective params: {tests_passed[1]}")
    print(f"✅ Sparse has fewer computational:   {tests_passed[2]}")
    print(f"✅ Sparsity properly detected:       {tests_passed[3]}")
    print(f"✅ Masks not in parameters:          {tests_passed[4]}")
    
    all_passed = all(tests_passed)
    print(f"\n🎉 All tests passed: {all_passed}")
    
    if not all_passed:
        print(f"⚠️  Some tests failed. Check the implementation.")
    
    return all_passed

if __name__ == "__main__":
    test_fixed_counting_functions()
