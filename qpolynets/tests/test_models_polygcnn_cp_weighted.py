import jax
import jax.numpy as jnp
import numpy as np
import netket as nk

from qpolynets.models import PolyGCNN


def test_polygcnn_cp_weighted_forward_shapes():
    g = nk.graph.Hypercube(length=4, n_dim=1, pbc=True)
    model = PolyGCNN(
        symmetries=g,
        layers=2,
        features=(8, 16),
        degree=3,
        mode="fft",
        use_cp_weighted=True,
        rank=4,
    )
    x = jnp.ones((2, g.n_nodes))
    params = model.init(jax.random.PRNGKey(0), x)
    y = model.apply(params, x)
    assert y.shape == (2,)

