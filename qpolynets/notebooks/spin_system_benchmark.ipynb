{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Comprehensive Spin System Benchmarking Suite\n", "\n", "This notebook provides a comprehensive benchmarking framework for comparing all available neural network architectures on spin system quantum many-body problems.\n", "\n", "## 🎯 **Models Included:**\n", "- **PolyGCNN variants**: Standard, CP-weighted, different degrees\n", "- **Baseline GCNN**: NetKet standard implementation\n", "- **GAT models**: SpinGAT, PolyGAT from qpolynets\n", "- **Polynomial models**: CP, CP_sparse_LU, CP_sparse_degree\n", "- **Classical baselines**: RBM, RBM_Symm, Jastrow, Feed-forward networks\n", "\n", "## 🚀 **Features:**\n", "- Easy model swapping via comment/uncomment blocks\n", "- Consistent VMC optimization methodology\n", "- Comprehensive performance comparison plots\n", "- Parameter count analysis\n", "- Convergence analysis with exact ground state comparison"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Imports successful!\n", "JAX devices: [CpuDevice(id=0)]\n", "NetKet version: 3.19.0\n", "JAX backend: cpu\n"]}], "source": ["# ============================================================================\n", "# IMPORTS AND SETUP\n", "# ============================================================================\n", "\n", "import sys\n", "import os\n", "sys.path.append('..')\n", "\n", "import jax\n", "import jax.numpy as jnp\n", "import netket as nk\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import time\n", "from typing import Dict, List, Any, Tuple\n", "from flax import nnx\n", "\n", "# Import qpolynets models\n", "sys.path.append('/Users/<USER>/Projects/Quantum/qpolynets/src')\n", "from qpolynets.models import PolyGCNN, SpinGAT, PolyGAT, Jastrow, PolyJastrow\n", "from qpolynets.layers.polynomial_layers import CP, CP_sparse_LU, CP_sparse_degree\n", "\n", "# Set up plotting\n", "plt.style.use('default')\n", "plt.rcParams['figure.figsize'] = (14, 10)\n", "plt.rcParams['font.size'] = 12\n", "\n", "print(\"✅ Imports successful!\")\n", "print(f\"JAX devices: {jax.devices()}\")\n", "print(f\"NetKet version: {nk.__version__}\")\n", "print(f\"JAX backend: {jax.default_backend()}\")"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔬 System Configuration:\n", "   Type: heisenberg_1d\n", "   Size: 12\n", "   VMC iterations: 100\n"]}], "source": ["# ============================================================================\n", "# SYSTEM CONFIGURATION\n", "# ============================================================================\n", "\n", "# Choose spin system to benchmark\n", "SYSTEM_CONFIG = {\n", "    # Uncomment one of the following systems:\n", "    \n", "    # 1D Heisenberg Chain (default)\n", "    'system_type': 'heisenberg_1d',\n", "    'system_size': 12,\n", "    'lattice_params': {'pbc': True},\n", "    \n", "    # # 2D Heisenberg Square Lattice\n", "    # 'system_type': 'heisenberg_2d',\n", "    # 'system_size': (4, 4),\n", "    # 'lattice_params': {'pbc': True},\n", "    \n", "    # # 1D Ising Chain\n", "    # 'system_type': 'ising_1d',\n", "    # 'system_size': 20,\n", "    # 'lattice_params': {'pbc': True, 'h': 1.0},\n", "    \n", "    # # <PERSON><PERSON>\n", "    # 'system_type': 'honeycomb_heisenberg',\n", "    # 'system_size': (3, 3),\n", "    # 'lattice_params': {'pbc': True},\n", "}\n", "\n", "# VMC optimization parameters\n", "VMC_CONFIG = {\n", "    'n_samples': 1024,\n", "    'n_chains': 8,\n", "    'n_discard_per_chain': 10,\n", "    'learning_rate': 0.01,\n", "    'sr_diag_shift': 0.01,\n", "    'n_iterations': 100,  # Adjust based on system complexity\n", "    'log_every': 10,\n", "}\n", "\n", "print(f\"🔬 System Configuration:\")\n", "print(f\"   Type: {SYSTEM_CONFIG['system_type']}\")\n", "print(f\"   Size: {SYSTEM_CONFIG['system_size']}\")\n", "print(f\"   VMC iterations: {VMC_CONFIG['n_iterations']}\")"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["   ⚠️ Could not compute exact energy: not enough values to unpack (expected 2, got 1)\n", "\n", "🔬 Created heisenberg_1d system:\n", "   Graph nodes: 12\n", "   Graph edges: 12\n", "   Hilbert space size: 12\n"]}], "source": ["# ============================================================================\n", "# SYSTEM CREATION FUNCTIONS\n", "# ============================================================================\n", "\n", "def create_spin_system(system_type: str, system_size, lattice_params: dict):\n", "    \"\"\"Create spin system with Hamiltonian and exact ground state.\"\"\"\n", "    \n", "    if system_type == 'heisenberg_1d':\n", "        # 1D Heisenberg chain\n", "        graph = nk.graph.Chain(length=system_size, pbc=lattice_params.get('pbc', True))\n", "        hilbert = nk.hilbert.Spin(s=1/2, N=graph.n_nodes)\n", "        hamiltonian = nk.operator.Heisenberg(hilbert=hilbert, graph=graph)\n", "        \n", "    elif system_type == 'heisenberg_2d':\n", "        # 2D Heisenberg square lattice\n", "        Lx, Ly = system_size\n", "        graph = nk.graph.Square(length=[Lx, Ly], pbc=lattice_params.get('pbc', True))\n", "        hilbert = nk.hilbert.Spin(s=1/2, N=graph.n_nodes)\n", "        hamiltonian = nk.operator.Heisenberg(hilbert=hilbert, graph=graph)\n", "        \n", "    elif system_type == 'ising_1d':\n", "        # 1D Ising chain with transverse field\n", "        graph = nk.graph.Chain(length=system_size, pbc=lattice_params.get('pbc', True))\n", "        hilbert = nk.hilbert.Spin(s=1/2, N=graph.n_nodes)\n", "        h = lattice_params.get('h', 1.0)\n", "        hamiltonian = nk.operator.Ising(hilbert=hilbert, graph=graph, h=h)\n", "        \n", "    elif system_type == 'honeycomb_heisenberg':\n", "        # Honeycomb Heisenberg model\n", "        Lx, Ly = system_size\n", "        graph = nk.graph.Honeycomb(length=[Lx, Ly], pbc=lattice_params.get('pbc', True))\n", "        hilbert = nk.hilbert.Spin(s=1/2, N=graph.n_nodes, total_sz=0)  # Singlet constraint\n", "        hamiltonian = nk.operator.Heisenberg(hilbert=hilbert, graph=graph)\n", "        \n", "    else:\n", "        raise ValueError(f\"Unknown system type: {system_type}\")\n", "    \n", "    # Compute exact ground state (for small systems)\n", "    exact_energy = None\n", "    if hilbert.size <= 20:  # Only for small systems\n", "        try:\n", "            eigenvalues, _ = nk.exact.lanczos_ed(hamiltonian, k=1, compute_eigenvectors=False)\n", "            exact_energy = eigenvalues[0].real\n", "            print(f\"   ✅ Exact ground state energy: {exact_energy:.6f}\")\n", "        except Exception as e:\n", "            print(f\"   ⚠️ Could not compute exact energy: {e}\")\n", "    else:\n", "        print(f\"   ⚠️ System too large for exact diagonalization\")\n", "    \n", "    return graph, hilbert, hamiltonian, exact_energy\n", "\n", "# Create the system\n", "graph, hilbert, hamiltonian, exact_energy = create_spin_system(\n", "    SYSTEM_CONFIG['system_type'],\n", "    SYSTEM_CONFIG['system_size'],\n", "    SYSTEM_CONFIG['lattice_params']\n", ")\n", "\n", "print(f\"\\n🔬 Created {SYSTEM_CONFIG['system_type']} system:\")\n", "print(f\"   Graph nodes: {graph.n_nodes}\")\n", "print(f\"   Graph edges: {graph.n_edges}\")\n", "print(f\"   Hilbert space size: {hilbert.size}\")"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "🧠 Created 4 models for benchmarking\n"]}], "source": ["# ============================================================================\n", "# MODEL DEFINITIONS\n", "# ============================================================================\n", "\n", "def create_models(graph, hilbert):\n", "    \"\"\"Create all models for benchmarking.\"\"\"\n", "    models = {}\n", "    n_sites = hilbert.size\n", "    \n", "    # ========================================\n", "    # POLYGCNN MODELS\n", "    # ========================================\n", "    \n", "    # Standard PolyGCNN (degree 2)\n", "    models['PolyGCNN_Std'] = PolyGCNN(\n", "        symmetries=graph,\n", "        layers=2,\n", "        features=(12, 12),\n", "        degree=2,\n", "        mode=\"fft\",\n", "        use_cp_weighted=False,\n", "        complex_output=False,\n", "        param_dtype=jnp.float64\n", "    )\n", "    \n", "    # PolyGCNN with CP weighting\n", "    models['PolyGCNN_CP'] = PolyGCNN(\n", "        symmetries=graph,\n", "        layers=2,\n", "        features=(12, 12),\n", "        degree=2,\n", "        mode=\"fft\",\n", "        use_cp_weighted=True,\n", "        rank=8,\n", "        complex_output=False,\n", "        param_dtype=jnp.float64\n", "    )\n", "    \n", "    # Higher degree PolyGCNN\n", "    models['PolyGCNN_Deg3'] = PolyGCNN(\n", "        symmetries=graph,\n", "        layers=2,\n", "        features=(12, 12),  # Smaller features for higher degree\n", "        degree=3,\n", "        mode=\"fft\",\n", "        use_cp_weighted=True,\n", "        rank=6,\n", "        complex_output=False,\n", "        param_dtype=jnp.float64\n", "    )\n", "    \n", "    # ========================================\n", "    # BASELINE GCNN\n", "    # ========================================\n", "    \n", "    # Standard NetKet GCNN (parameter-matched)\n", "    models['GCNN_Baseline'] = nk.models.GCNN(\n", "        symmetries=graph,\n", "        layers=3,\n", "        features=(12, 16, 12),  # Tuned for similar parameter count\n", "        mode=\"fft\",\n", "        complex_output=False,\n", "        param_dtype=jnp.float64\n", "    )\n", "    \n", "    return models\n", "\n", "# Create models\n", "models = create_models(graph, hilbert)\n", "print(f\"\\n🧠 Created {len(models)} models for benchmarking\")"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "📊 Final model count: 5\n", "   - PolyGCNN_Std\n", "   - PolyGCNN_CP\n", "   - PolyGCNN_Deg3\n", "   - GCNN_Baseline\n", "   - CP_<PERSON>y\n"]}], "source": ["# ============================================================================\n", "# ADDITIONAL MODELS (COMMENT/UNCOMMENT TO SELECT)\n", "# ============================================================================\n", "\n", "def add_additional_models(models, graph, hilbert):\n", "    \"\"\"Add additional models - comment/uncomment as needed.\"\"\"\n", "    n_sites = hilbert.size\n", "    \n", "    # ========================================\n", "    # GAT MODELS (Uncomment to include)\n", "    # ========================================\n", "    \n", "    # # SpinGA<PERSON> from qpolynets\n", "    # models['SpinGAT'] = SpinGAT(\n", "    #     n_sites=n_sites,\n", "    #     lattice_type=\"chain\" if 'chain' in SYSTEM_CONFIG['system_type'] else \"square\",\n", "    #     hidden_features=[16, 8],\n", "    #     n_heads=[4, 1],\n", "    #     rngs=nnx.Rngs(42)\n", "    # )\n", "    \n", "    # # PolyGAT from qpolynets\n", "    # models['PolyGAT'] = PolyGAT(\n", "    #     n_nodes=n_sites,\n", "    #     hidden_features=[16, 8],\n", "    #     degrees=[2, 2],\n", "    #     heads=[2, 1],\n", "    #     use_cp_weighted=True,\n", "    #     rank=6,\n", "    #     rngs=nnx.Rngs(43)\n", "    # )\n", "    \n", "    # ========================================\n", "    # POLYNOMIAL MODELS (Uncomment to include)\n", "    # ========================================\n", "    \n", "     # Standard CP polynomial\n", "    models['CP_Poly'] = CP(\n", "         degree=2,\n", "         input_dim=n_sites,\n", "         rank=min(8, n_sites//2),\n", "         output_dim=1,\n", "         param_dtype=jnp.complex128,\n", "         rngs=nnx.Rngs(44)\n", "    )\n", "    \n", "    # # CP sparse LU\n", "    # models['CP_SparseLU'] = CP_sparse_LU(\n", "    #     degree=2,\n", "    #     input_dim=n_sites,\n", "    #     rank=min(6, n_sites//3),\n", "    #     output_dim=1,\n", "    #     param_dtype=jnp.complex128,\n", "    #     rngs=nnx.Rngs(45)\n", "    # )\n", "    \n", "    # ========================================\n", "    # CLASSICAL BASELINES (Uncomment to include)\n", "    # ========================================\n", "    \n", "    # # RBM\n", "    # models['RBM'] = nk.models.RBM(\n", "    #     alpha=1,\n", "    #     param_dtype=jnp.float64\n", "    # )\n", "    \n", "    # # Symmetric RBM\n", "    # models['RBM_Symm'] = nk.models.RBMSymm(\n", "    #     symmetries=graph.translation_group(),\n", "    #     alpha=1,\n", "    #     param_dtype=jnp.float64\n", "    # )\n", "    \n", "    # # <PERSON><PERSON><PERSON>\n", "    # models['J<PERSON><PERSON>'] = J<PERSON><PERSON>(\n", "    #     hilbert_size=n_sites,\n", "    #     rngs=nnx.Rngs(46)\n", "    # )\n", "    \n", "    return models\n", "\n", "# Add additional models (uncomment function calls as needed)\n", "models = add_additional_models(models, graph, hilbert)\n", "\n", "print(f\"\\n📊 Final model count: {len(models)}\")\n", "for name in models.keys():\n", "    print(f\"   - {name}\")"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ VMC framework ready!\n"]}], "source": ["# ============================================================================\n", "# VMC OPTIMIZATION FRAMEWORK\n", "# ============================================================================\n", "\n", "def setup_vmc(model, hamiltonian, hilbert, graph, config):\n", "    \"\"\"Setup VMC calculation for a given model.\"\"\"\n", "    \n", "    # Create appropriate sampler based on system\n", "    if 'honeycomb' in SYSTEM_CONFIG['system_type'] or hilbert.constrained:\n", "        # Use exchange sampler for constrained systems\n", "        sampler = nk.sampler.MetropolisExchange(\n", "            hilbert=hilbert,\n", "            graph=graph,\n", "            d_max=2,\n", "            n_chains=config['n_chains']\n", "        )\n", "    else:\n", "        # Use local sampler for unconstrained systems\n", "        sampler = nk.sampler.MetropolisLocal(\n", "            hilbert=hilbert,\n", "            n_chains=config['n_chains']\n", "        )\n", "    \n", "    # Create variational state\n", "    vs = nk.vqs.MCState(\n", "        sampler=sampler,\n", "        model=model,\n", "        n_samples=config['n_samples'],\n", "        n_discard_per_chain=config['n_discard_per_chain'],\n", "        seed=42\n", "    )\n", "    \n", "    # Create optimizer\n", "    optimizer = nk.optimizer.Sgd(learning_rate=config['learning_rate'])\n", "    \n", "    # Create stochastic reconfiguration\n", "    # Check if model has complex parameters\n", "    try:\n", "        sample_params = nnx.state(model) if hasattr(model, '__dict__') else model.init(jax.random.PRNGKey(0), jnp.ones(hilbert.size))\n", "        is_complex = any(jnp.iscomplexobj(p) for p in jax.tree.leaves(sample_params))\n", "        sr = nk.optimizer.SR(\n", "            diag_shift=config['sr_diag_shift'],\n", "            holomorphic=is_complex\n", "        )\n", "    except:\n", "        sr = nk.optimizer.SR(diag_shift=config['sr_diag_shift'])\n", "    \n", "    # Create VMC driver\n", "    vmc = nk.VMC(\n", "        hamiltonian=hamiltonian,\n", "        optimizer=optimizer,\n", "        preconditioner=sr,\n", "        variational_state=vs\n", "    )\n", "    \n", "    return vmc, vs\n", "\n", "def run_vmc_optimization(model_name, model, hamiltonian, hilbert, graph, config):\n", "    \"\"\"Run VMC optimization for a single model.\"\"\"\n", "    print(f\"\\n🚀 Optimizing {model_name}...\")\n", "    \n", "    try:\n", "        # Setup VMC\n", "        vmc, vs = setup_vmc(model, hamiltonian, hilbert, graph, config)\n", "        \n", "        # Storage for results\n", "        energies = []\n", "        variances = []\n", "        iterations = []\n", "        \n", "        # Optimization loop with logging\n", "        start_time = time.time()\n", "        \n", "        for i in range(config['n_iterations']):\n", "            vmc.advance(1)\n", "            \n", "            if i % config['log_every'] == 0 or i == config['n_iterations'] - 1:\n", "                energy_stats = vs.expect(hamiltonian)\n", "                energy = energy_stats.mean.real\n", "                variance = energy_stats.variance.real\n", "                \n", "                energies.append(energy)\n", "                variances.append(variance)\n", "                iterations.append(i)\n", "                \n", "                error_str = \"\"\n", "                if exact_energy is not None:\n", "                    error = abs(energy - exact_energy)\n", "                    error_str = f\", Error: {error:.6f}\"\n", "                \n", "                print(f\"   Step {i:3d}: E = {energy:.6f}, Var = {variance:.6f}{error_str}\")\n", "        \n", "        end_time = time.time()\n", "        \n", "        # Final results\n", "        final_energy = energies[-1]\n", "        final_variance = variances[-1]\n", "        optimization_time = end_time - start_time\n", "        \n", "        results = {\n", "            'model_name': model_name,\n", "            'final_energy': final_energy,\n", "            'final_variance': final_variance,\n", "            'energies': energies,\n", "            'variances': variances,\n", "            'iterations': iterations,\n", "            'optimization_time': optimization_time,\n", "            'converged': True,\n", "            'error_message': None\n", "        }\n", "        \n", "        if exact_energy is not None:\n", "            results['final_error'] = abs(final_energy - exact_energy)\n", "        \n", "        print(f\"   ✅ Completed in {optimization_time:.1f}s\")\n", "        return results\n", "        \n", "    except Exception as e:\n", "        print(f\"   ❌ Failed: {e}\")\n", "        return {\n", "            'model_name': model_name,\n", "            'converged': <PERSON><PERSON><PERSON>,\n", "            'error_message': str(e),\n", "            'optimization_time': 0\n", "        }\n", "\n", "print(\"✅ VMC framework ready!\")"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "📊 Model Parameter Analysis:\n", "============================================================\n", "   PolyGCNN_Std        :     7092 parameters\n", "   PolyGCNN_CP         :     4888 parameters\n", "   PolyGCNN_Deg3       :     5442 parameters\n", "   GCNN_Baseline       :     9400 parameters\n", "   CP_Poly             :      151 parameters\n"]}], "source": ["# ============================================================================\n", "# MODEL PARAMETER ANALYSIS\n", "# ============================================================================\n", "\n", "def analyze_model_parameters(models, hilbert):\n", "    \"\"\"Analyze parameter counts for all models.\"\"\"\n", "    print(\"\\n📊 Model Parameter Analysis:\")\n", "    print(\"=\" * 60)\n", "    \n", "    param_counts = {}\n", "    test_input = jnp.ones((2, hilbert.size))  # Batch of 2 configurations\n", "    \n", "    for name, model in models.items():\n", "        try:\n", "            # Initialize parameters\n", "            if hasattr(model, 'init'):\n", "                params = model.init(jax.random.PRNGKey(0), test_input)\n", "                param_count = nk.jax.tree_size(params)\n", "            else:\n", "                # For nnx models\n", "                param_count = nk.jax.tree_size(nnx.state(model))\n", "            \n", "            param_counts[name] = param_count\n", "            print(f\"   {name:20s}: {param_count:8d} parameters\")\n", "            \n", "        except Exception as e:\n", "            print(f\"   {name:20s}: Error - {e}\")\n", "            param_counts[name] = 0\n", "    \n", "    return param_counts\n", "\n", "# Analyze parameters\n", "param_counts = analyze_model_parameters(models, hilbert)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "🚀 Starting benchmark with 5 models...\n", "\n", "🏁 Starting Full Benchmark Suite\n", "============================================================\n", "\n", "==================== PolyGCNN_Std ====================\n", "\n", "🚀 Optimizing PolyGCNN_Std...\n", "   Step   0: E = -4.220998, Var = 82.530889\n", "   Step  10: E = -20.064356, Var = 5.827598\n", "   Step  20: E = -20.873377, Var = 1.031885\n", "   Step  30: E = -21.118934, Var = 0.700089\n", "   Step  40: E = -21.237638, Var = 0.629299\n", "   Step  50: E = -21.383573, Var = 0.519201\n", "   Step  60: E = -21.419080, Var = 0.270782\n", "   Step  70: E = -21.472790, Var = 0.790225\n", "   Step  80: E = -21.510600, Var = 0.576909\n", "   Step  90: E = -21.501716, Var = 0.154909\n", "   Step  99: E = -21.494316, Var = 0.024918\n", "   ✅ Completed in 136.1s\n", "   Final Energy: -21.494316\n", "   Time: 136.1s\n", "\n", "==================== PolyGCNN_CP ====================\n", "\n", "🚀 Optimizing PolyGCNN_CP...\n", "   Step   0: E = -12.689521, Var = 40.597441\n", "   Step  10: E = -20.145891, Var = 3.610055\n", "   Step  20: E = -20.988560, Var = 1.317602\n", "   Step  30: E = -21.189762, Var = 0.509255\n", "   Step  40: E = -21.279084, Var = 0.440751\n", "   Step  50: E = -21.385696, Var = 0.201807\n", "   Step  60: E = -21.464329, Var = 0.168823\n", "   Step  70: E = -21.478965, Var = 0.117608\n", "   Step  80: E = -21.515334, Var = 0.074540\n", "   Step  90: E = -21.484279, Var = 0.090911\n", "   Step  99: E = -21.551206, Var = 0.049854\n", "   ✅ Completed in 37.8s\n", "   Final Energy: -21.551206\n", "   Time: 37.8s\n", "\n", "==================== PolyGCNN_Deg3 ====================\n", "\n", "🚀 Optimizing PolyGCNN_Deg3...\n", "   Step   0: E = -2.281709, Var = 50.822901\n", "   Step  10: E = -4.000202, Var = 0.000000\n", "   Step  20: E = -4.000202, Var = 0.000000\n", "   Step  30: E = -4.000202, Var = 0.000000\n", "   Step  40: E = -4.000202, Var = 0.000000\n", "   Step  50: E = -4.000202, Var = 0.000000\n", "   Step  60: E = -4.000202, Var = 0.000000\n", "   Step  70: E = -4.000202, Var = 0.000000\n", "   Step  80: E = -4.000202, Var = 0.000000\n", "   Step  90: E = -4.000202, Var = 0.000000\n", "   Step  99: E = -4.000202, Var = 0.000000\n", "   ✅ Completed in 29.6s\n", "   Final Energy: -4.000202\n", "   Time: 29.6s\n", "\n", "==================== GCNN_Baseline ====================\n", "\n", "🚀 Optimizing GCNN_Baseline...\n", "   Step   0: E = -13.575982, Var = 41.438275\n", "   Step  10: E = -20.218747, Var = 4.139685\n", "   Step  20: E = -20.887645, Var = 1.045003\n", "   Step  30: E = -21.148498, Var = 0.603423\n", "   Step  40: E = -21.258121, Var = 0.503937\n", "   Step  50: E = -21.380202, Var = 0.283944\n", "   Step  60: E = -21.464172, Var = 0.258556\n", "   Step  70: E = -21.484954, Var = 0.137293\n", "   Step  80: E = -21.514949, Var = 0.057475\n", "   Step  90: E = -21.532372, Var = 0.034931\n", "   Step  99: E = -21.523965, Var = 0.115729\n", "   ✅ Completed in 127.2s\n", "   Final Energy: -21.523965\n", "   Time: 127.2s\n", "\n", "==================== CP_Poly ====================\n", "\n", "🚀 Optimizing CP_Poly...\n", "   ❌ Failed: \n", "\n", "The output of the model NNXWrapper(wrapped_class=<class 'qpolynets.layers.polynomial_layers.CP'>, ...) has `shape=(8, 1)`, but\n", "`shape=(8,)` was expected.\n", "\n", "This might be because of an hilbert space mismatch or because your\n", "model is ill-configured.\n", "\n", "\n", "   ❌ Failed: \n", "\n", "The output of the model NNXWrapper(wrapped_class=<class 'qpolynets.layers.polynomial_layers.CP'>, ...) has `shape=(8, 1)`, but\n", "`shape=(8,)` was expected.\n", "\n", "This might be because of an hilbert space mismatch or because your\n", "model is ill-configured.\n", "\n", "\n"]}], "source": ["# ============================================================================\n", "# RUN BENCHMARKING\n", "# ============================================================================\n", "\n", "def run_full_benchmark(models, hamiltonian, hilbert, graph, config):\n", "    \"\"\"Run complete benchmarking suite.\"\"\"\n", "    print(\"\\n🏁 Starting Full Benchmark Suite\")\n", "    print(\"=\" * 60)\n", "    \n", "    all_results = {}\n", "    \n", "    for model_name, model in models.items():\n", "        print(f\"\\n{'='*20} {model_name} {'='*20}\")\n", "        \n", "        # Run VMC optimization\n", "        result = run_vmc_optimization(\n", "            model_name, model, hamiltonian, hilbert, graph, config\n", "        )\n", "        \n", "        all_results[model_name] = result\n", "        \n", "        # Print summary\n", "        if result['converged']:\n", "            print(f\"   Final Energy: {result['final_energy']:.6f}\")\n", "            if exact_energy is not None:\n", "                print(f\"   Final Error:  {result.get('final_error', 'N/A'):.6f}\")\n", "            print(f\"   Time: {result['optimization_time']:.1f}s\")\n", "        else:\n", "            print(f\"   ❌ Failed: {result['error_message']}\")\n", "    \n", "    return all_results\n", "\n", "# Run the benchmark\n", "print(f\"\\n🚀 Starting benchmark with {len(models)} models...\")\n", "benchmark_results = run_full_benchmark(models, hamiltonian, hilbert, graph, VMC_CONFIG)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAABisAAASgCAYAAACJ0HuLAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjAsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvlHJYcgAAAAlwSFlzAAAPYQAAD2EBqD+naQABAABJREFUeJzs3Xl8TNf/x/H3ZN8TIoslxBrEWtResdXeorRoq5au9mpLFQ1aRatKW6pVFb6KlhZF1U43umpV7WutQYJYkkgy9/eHX6aZZrKIJEPyej4eeTRz7jn3fu6ckd47n3vOMRmGYQgAAAAAAAAAAMBOHOwdAAAAAAAAAAAAKNxIVgAAAAAAAAAAALsiWQEAAAAAAAAAAOyKZAUAAAAAAAAAALArkhUAAAAAAAAAAMCuSFYAAAAAAAAAAAC7IlkBAAAAAAAAAADsimQFAAAAAAAAAACwK5IVAAAAAAAAAADArkhWAABwi0JDQxUaGpqnx4iIiJDJZMrTY9yuuyHGOwnvFwAAQO7Kj+vyVMeOHZPJZFKfPn3y5XgAUBiRrADuAiaTKcufP/74w95h3pE2bNigRx99VGXLlpWHh4fc3d1VoUIFPf7441q7dq29wwNwB9u7d68iIyP14IMPqnTp0pa/t8nJyfYODQAA3GEKyz3b9evX5efnJ5PJpF69etk7HABAAeNk7wAAZF9kZGSG24KDg/MxkjvflStX1Lt3b61YsUJubm5q0aKFunbtKmdnZx07dkzffPONFi5cqBdeeEFTp061d7hAOgsWLND169ftHUahtm7dOk2YMEGOjo6qWLGi3NzclJCQYO+wAADAHayg37N99tlnunz5skwmk7788kvFxMTI39/f3mHli5IlS2rv3r3y9fW1dygAUGCRrADuIuPGjbN3CHcFs9ms7t27a926dWrevLkWLlyoEiVKWNW5ceOGPvroI+3bt89OUQKZK126tL1DKPTatWunhg0bqkaNGnJ3d1doaKiOHz9u77AAAMAdrKDfs3300UdydHTUiy++qClTpmjBggV6/vnn7R1WvnB2dlblypXtHQYAFGhMAwUUQOPGjZPJZNLWrVu1bNky3XvvvfLw8FDRokX1yCOP6OTJkzbbxcbGatSoUapSpYrc3d3l6+urli1bav369enqRkVFyWQyKSoqSmvWrNF9990nHx8fq/nYL1++rGHDhqlUqVJyc3NT5cqVNW3aNB05ciTdXJ89evSQyWTSt99+azO2ZcuWyWQyafDgwVme/6JFi7Ru3TpVqFBBq1atSpeokCQXFxcNGjRIb7/9tlV5QkKCJk2apOrVq8vDw0M+Pj5q2rSplixZkm4faecsPXbsmHr06KFixYrJzc1NderU0VdffWVVf9KkSTKZTHr33Xdtxn3ixAk5OjqqXr16VuXJycmaNWuWGjRoIB8fH3l4eKh27dp6//33ZTabM4xp37596tatmwICAuTg4KCtW7dKkgzD0IwZM1S1alW5ubmpZMmSGjRokC5fvpzpnK+LFy9W8+bNVaRIEbm5ualKlSp6/fXXlZiYmK6uyWRSRESELly4oKefflrFixeXq6urwsPDNXfuXJv7l6T169erU6dOCgwMlKurq0JCQvTggw9q48aN6equW7dO7du3V7FixeTq6qry5cvrpZde0qVLlzLcf267fv26XnrpJZUuXVqurq6qUKGCJk+eLMMwbNb/6aef1K1bNwUHB8vFxUUhISF65plndPr06XR1ba1vYBiGPvnkEzVs2FABAQFyc3NTiRIl1KpVK5uf0ZMnT2rQoEEqV66cXF1d5e/vrwceeEC//PJLuro5/bshSYmJiRozZozKli1r6Yvx48frxo0bNuvv27dPffr0UUhIiFxdXRUUFKRevXpp//796er26dNHJpNJR44c0fTp01W9enW5u7srIiLCUufAgQN66KGHVKRIEXl6eqpRo0Zas2aN1d+pnAgLC1P9+vXl7u5+S+2WLFmiOnXqyN3dXYGBgXr88cdt9jEAACi80l57LViwQPXq1ZOnp6flWjyr7ZJ0+vRpDRgwQKGhoXJxcVFAQIC6dOli81ovO/dvWdm9e7d27Nih+++/Xy+++KKcnZ01Z86cDOvf6j3BjRs39P7776t9+/YqU6aMXF1dVaRIEbVs2VJr1qzJVoyzZ8+WyWTShAkTbG4/e/asnJ2dVb16dUvZ5cuXNX78eIWHh8vb21teXl4KDQ1V9+7d9dtvv1nqZbRmxdmzZzV8+HCFhYXJ09NTPj4+qlChgnr37q3Dhw9nK24AwE2MrAAKsFmzZumrr77SAw88oGbNmumnn37S559/rj/++EO7du2Sq6urpe7x48cVERGhY8eO6b777lO7du109epVrV69Wm3bttXs2bP19NNPpzvG0qVL9c0336h9+/Z69tlndfToUUk3v/Rv0aKFfv/9d9WuXVuPPvqoLl++rIkTJ+q7775Lt58BAwbos88+04cffqj77rsv3fYPP/xQkmzG8F+pF8wvvviiPD09M62b9j24ceOG7r//fn333XeqWrWqBg4cqOvXr2vp0qXq2bOndu7cqSlTpqTbx/Hjx3XvvfeqXLlyevzxxxUbG6vPPvtMnTt31oYNG9SyZUtJUu/evTVmzBjNnz9fQ4YMSbef//3vfzKbzXriiScsZUlJSerUqZPWrVunypUrq1evXnJzc9OWLVs0ePBg7dixQwsXLky3r0OHDqlBgwYKCwvTY489pqtXr8rb21uSNHDgQH3wwQcqUaKEnn76abm4uOirr77Szz//rKSkJDk7O6fbX//+/fXJJ58oJCREDz30kHx9fbVjxw6NHTtWmzZt0vr169O1u3Tpkho3biwXFxd169ZNCQkJWrZsmZ588kk5ODiob9++VvUjIyM1YcIEeXl5qXPnzgoJCdHp06f1ww8/aOHChWrVqpWl7oQJExQZGSl/f3916NBBgYGB2rVrl6ZOnaqvv/5aP/74Y54Pz05KStL999+v06dPq127dnJyctKKFSs0atQoxcfHa/z48Vb1582bp6eeekpubm564IEHVKpUKR08eFAff/yxVq1apR07dmQ5muLll1/Wm2++qbJly+rhhx+Wr6+vzpw5o19++UXLli1Tjx49LHV///133X///YqNjVWbNm3UtWtXXbhwQStWrFCTJk20fPlytW/fPt0xbuXvRqqHH35Yv/zyi7p16yZnZ2etXLlS48aN06+//qqvvvrK6ib4m2++UdeuXZWcnKyOHTuqQoUKOnnypL788kutWbNGW7Zs0T333JPuGEOGDNH333+vDh06qH379nJ0dJR0M/HRuHFjxcbGqkOHDqpRo4aOHDmiLl262Dy/vPbOO+9o+PDh8vPzU+/eveXn56d169apUaNGTBkAAADSmTp1qjZu3KhOnTqpRYsW6R68yWj7kSNH1KRJE505c0YtW7ZUz549deLECS1dulRr1qzR0qVL9eCDD6Y7Xkb3b9nx0UcfSbr5MEmxYsXUsWNHLV++XN9//72aNGlis82t3BPExsZq6NChatSokVq3bq2AgACdOXNGK1euVMeOHfXhhx9meT/42GOPaeTIkfr44481evRoyzVjqrlz5yo5OVnPPPOMpJsPA7Vt21Y7duxQw4YN9dRTT8nJyUknTpzQ1q1btX37dtWpUyfD412/fl2NGjXS0aNH1bp1a3Xq1EmGYej48eNatWqVHn74YZUvXz5b7y8AQJIB4I4nyZBkREZG2vyZNGmSVf3IyEhDkuHt7W3s2rXLalvPnj0NScaSJUusyps1a2aYTCbj888/tyq/ePGiUbNmTcPNzc04c+aMpXzevHmGJMNkMhlr165NF/OECRMMSUaPHj0Ms9lsKf/nn3+MYsWKGZKMJ554wqpNtWrVDFdXV+PChQtW5YcOHTJMJpPRqFGjLN+rpKQkw8XFxZBkHDx4MMv6aU2cONGQZHTs2NFISkqylJ89e9YICQkxJBnfffedpfzo0aOWvhk3bpzVvr755htDktG2bVur8tatWxuSjL/++ivd8StXrmw4OztbnX9qXw4dOtRITk62lCcnJxv9+vUzJBnLly+3GdOoUaPSHePbb781JBmVKlUyLl68aClPTEw0mjZtakgyypQpY9Umta+7detmxMfHW21Lje+dd96xKk+NoX///lZx//3334ajo6NRuXJlq/rr1q0zJBnlypUzTp48abXNbDYbJ06csLzevHmzIclo3LixcenSJZuxDh06NN2556YyZcoYkox27doZ169ft5RHR0cbvr6+ho+Pj3Hjxg1L+f79+w1nZ2ejYsWKxunTp632tWnTJsPBwcF48MEHrcqbNWtm/Pd/00WKFDFKlChhXL16NV1M58+ft/yelJRklC9f3nBzc7P6zBqGYZw6dcooUaKEERQUZNWfOf27IcmoWLGiERsbaymPj483GjRoYEgyFixYYCmPjY01/Pz8jGLFihl79+612tfu3bsNT09Po1atWlblTzzxhCHJKFGihHHkyJF0592iRQtDkjFr1iyr8q+//tryOZw3b166djmR2u9p/z6kdfToUcPFxcUoUqSIcfToUUt5SkqK0bVrV0s8AACg4MnpPZuHh4fx+++/p9tfVttT7ysmT55sVf7dd98ZDg4ORpEiRYy4uDhLeVb3b1mJj483ihQpYvj5+RkJCQmGYRjGypUrDUlG7969M31PsntPkJCQYHXdnyo2NtaoUqWKUaRIEatrb8O4eX323/uXgQMHGpKMVatWWZWnpKQYoaGhhoeHh+U+4s8//zQkpbsWT62f9ho39V4r7X1s6ntg6/4jMTHRqg8AAFnjjhm4C6Re5GX04+vra1U/9cJ2zJgx6faV+kXvCy+8YCn7448/DElG9+7dbR5/xYoVhiTj/ffft5SlXuzauqgzDMMoX7684eDgYPWFXarXX3/dZrJi5syZhiTj7bfftiofMWJEui89MxIdHW15X/77xXpWypcvb5hMJmP//v3ptn300UeGJKNv376WstSL1dDQUKuL71SlS5c2/P39rcoWLlxoSDJefPFFq/IdO3YYkowuXbpYylJSUgx/f3+jePHiNvd/8eJFw2QyGd26dUsXU1BQkOUmIq3+/fsbkoz58+en2/b999/bTFbUqlXLcHZ2tkpupEpOTjb8/f2NunXrWpWn3ljZuji/7777DElW2zp27GhIMr788st09f+rc+fOhiTj77//trm9Vq1aRkBAQJb7uR2pX1ofOnQo3bbevXunS0gNGzbMkGSsWbPG5v46d+5sODg4GJcvX7aU2UpWFC1a1AgNDbXZt2ml/pt96aWXbG6fPn26IclYvXq1pexW/26kjdHWv80tW7YYkoyIiIh0x505c6bNuFLfp927d1vKUpMV/02IGcbN5Kcko0KFCkZKSkq67a1atcrXZEXq37ZXX3013bbDhw8bDg4OJCsAACigcnrPltFDNpltP3HihOW63dZ1Sa9evdJd82d1/5aV+fPnG5KMZ5991lKWlJRkBAUFGe7u7jbvFW71niAzU6dONSQZ27Ztsyq3laz4+++/DUlGp06drMpTH2bp16+fpWzXrl2GJKNnz55ZxmArWfHVV19l+KAYAODWMQ0UcBcxMpgHPyN169ZNVxYSEiJJunjxoqVs+/btkm4O0bW1INz58+clyeZi1PXr109XFhcXp8OHDyskJMTm+gcZDRF+/PHHNXLkSH300UcaPny4pJtTM0VFRalo0aLq3r27zXZp3ep7lOrKlSs6fPiwSpUqpUqVKqXbnjoF0e+//55uW61atdINL5Zuvtep722qrl27ysfHRwsXLtTkyZMt7RYsWCBJVvOfHjhwQDExMapYsaJee+01m3G7u7vb7JeaNWvanK5n586dkmz3QYMGDeTkZP2/hevXr+vPP/9UsWLFNH36dJsxuLq62oyhUqVKlqmn0kr9DF66dMmyfceOHTKZTGrbtq3NY6S1fft2OTs76/PPP7e5/caNGzp//rxiYmLk7++f4X7++OMPrVixwqrMz89Pw4YNyzKG1Lq2hnRn9m9s69at+vnnn9O1OXfunMxmsw4ePJjpMPNHH31U7733nsLDw/Xwww/rvvvuU8OGDdNNL5R6vGPHjtn8N33w4EFJN/9Nd+jQwWpbdv9upNWsWbN0ZU2bNpWTk5PlM5c2rj/++MNmXAcOHLDEFR4ebrXN1t+aP/74Q5LUsGFDOTikX4arSZMmNtc7ySupfx9svR/lypVTSEgIC3QDAFDA3er9iK1rnKy2p15fpV5v/VerVq20aNEi/f777+rdu/ctHS8jqVPtpr1fcXJy0qOPPqpp06Zp4cKFGjRoULp2t3JPIEl///233nrrLX377bc6c+aMEhISrNqdOnUqy1irVq2qZs2a6euvv9bJkydVqlQpSf9OLZw6BVRq3dq1a2vx4sU6ceKEHnjgATVu3Fh169aVi4tLlsdq1qyZSpYsqcmTJ2vnzp1q3769GjVqlOE9IgAgcyQrgALM1vzoqRezKSkplrKYmBhJ0oYNG7Rhw4YM93f16tV0ZcHBwenK4uLiJElBQUE295NRube3tx577DHNnj1b27ZtU7NmzbR8+XKdO3dOzz//vNzc3DKMLZW/v79cXFx048YNnTp1Ktvzg16+fDnD85Gk4sWLW9VLK6N56J2cnNItgO3u7q7u3btr7ty5Wr9+vdq1a6cbN25oyZIlCggIULt27Sx1U/vl4MGD6dY/SCu7/ZI2flt94OjomO7L/YsXL8owDJ0/fz7TGGzJ7H2RrD+Dly5dUpEiRbK1kHFMTIySk5OzjOfq1atZJiv+u48yZcpkO1lxK+eX2pdvvfVWpvu01ZdpvfPOOypfvrw++eQTTZo0SZMmTZKTk5M6dOigadOmqVy5clbHW7p06S0fL7t/N9LK7PN07tw5S1lqXJktxJhRXLY+05l9njMrzytZxRMcHEyyAgAAWMnouj2z7bdz75LV8WzZu3evvv/+e1WuXDldsqNv376aNm2a5syZYzNZcSvXzDt27FCLFi2UnJysli1b6oEHHpCPj48cHBz0xx9/aOXKlUpMTMxWzM8995y2bdumuXPnKjIyUqdPn9bq1atVq1Yt3XvvvZZ6jo6O2rRpkyZMmKBly5ZpxIgRkiQfHx/16dNHb7zxRqbrIPr4+GjHjh2KjIzUV199pW+++UaSFBAQoIEDB2r06NE2E0oAANvSP4YIoNBJvYCcMWOGjJvTw9n8mTdvXrq2aRfOTeXj4yNJio6Otnm8jMqlmwttS/8+9XIrC2tLNy96GzRoIEnatGlTttpI/74HZ8+etbn9zJkzVvVuR+oC2vPnz5ckrV69WrGxserVq5fVItWpx+rSpUum/WJrUTxb/SJl3jcpKSmWL5P/G0Pt2rUzjSGnI1pS+fn56eLFi4qPj8+yrq+vr4oUKZJlPGXKlMl0P3369EnX5tixY7d1HpnFLN28YcwsZltP5Kfl6OiooUOH6s8//1R0dLS++OILdenSRStXrlTbtm1148YNq+OtXLky0+NFRkbmyvll9nlK/cyljevPP//MNK60i8ynyu2/NXkh9fwyOm5Gf18AAEDhldF1e2bbb+feJavj2ZK6sPa+fftkMpmsfqpXry5J2rVrl3766adb3ndar7/+uuLj47V+/XqtXbtW06dP14QJEzRu3LhbHhHStWtXBQcH6+OPP1ZKSorlv88++2y6ukWKFNE777yjEydO6ODBg/r4448VFhamd99913J/mplSpUpp7ty5OnfunHbv3q13331XRYsW1bhx4zIcIQ8AsI1kBQDLl/vfffddruzPx8dH5cqV06lTp2x++fv9999n2LZ69epq0qSJvvzyS/3444/aunWrmjVrpsqVK2f7+KmJjalTp+r69euZ1k19Msfb21vly5fXqVOnLFPkpLVlyxZJ0j333JPtODLStGlTlS9fXitXrtTly5ctSYv/fkFbuXJl+fn5aceOHUpKSrrt40o3kw6S7T7YsWOHkpOTrcq8vLwUHh6uv//+W7GxsbkSgy0NGjSQYRhav359tupevHhRf//9d57Fk9ty+9+YJAUGBqpr1676/PPP1aJFCx08eFC7d+/Os+NlZtu2benKvvvuOyUnJ1s+c3kRV+q+t2/fnm4Uk5T535q8kPr3wdb7ceTIEZ04cSJf4wEAAAVT2mv6/16/S7l775KYmKj//e9/cnBwUL9+/dS/f/90P/fff7+krEfPZuXQoUMqWrSoIiIi0m2zdX2VGWdnZz355JM6efKkVq9erY8//lheXl7q1atXpu0qVKig/v37a9u2bfLy8tLy5cuzfUyTyaTw8HANHjzYMmPBrbQHAJCsAKCbc9Q3bdpUX375pT755BObdf766y+r6Vyy0rt3b5nNZo0aNcrqqfsTJ05kuPZBqgEDBigxMVHdunWTYRg2n37JTM+ePdWmTRsdPHhQDz74oOXJorRu3LihmTNn6oUXXrCU9evXT4Zh6KWXXrIajnzhwgXLEzH9+vW7pVgy0rt3byUkJGjWrFlau3atatSoYfWlrnRzlMjgwYN15swZDRkyxOaogzNnzmjPnj23dFxJmjhxotWw8Bs3buiVV16x2Wb48OG6ceOG+vXrp0uXLqXbfvHiRZtredyKwYMHS5JeeOEFm/2Vdm7a559/XpL01FNP6fTp0+nqXrt2TTt27LiteHLboEGD5OzsrOeff96yLkNaN27cyPIL/MTERG3atCndKJakpCRLIil1qrQHH3xQ5cuX18yZM/X111/b3N/27duzTOZl12uvvWa1nkVCQoJGjRol6ebUAKn69u0rPz8/jR8/3ubaHWazWVu3bs32cUNCQhQREaFDhw5ZRmGl+uabb/J1vQrp5poizs7Oeu+996wStWazWS+99JLNhAoAAMCtKlWqlFq3bq1jx46lu7f66aeftGjRIhUpUkRdunS57WN98cUXiomJUZs2bTR37lx9/PHH6X4+++wzubu7a8mSJbpy5UqOjxUaGqrY2Fjt2rXLqnzu3Llat27dLe/vmWeekaOjo5577jmdOHFCjz76aLr1M44ePWrzIaiLFy8qMTExy6mId+/ebfMBvdSRttmZyhgA8C8mzgPuIrYWpE3VuXNn1apVK8f7XrRokVq0aKH+/fvr3XffVf369eXn56eTJ09q165d2r17t7Zv367AwMBs7W/EiBFasWKFlixZov379+v+++/X5cuX9fnnn+u+++7TihUrbC6IK0kPPfSQAgMDdebMGQUEBKhr1663dC4ODg5aunSpHn/8ca1cuVLlypVTy5YtVaVKFTk6Our48ePatGmTzp8/rxdffNHS7sUXX9TatWu1cuVK1axZU+3bt9f169e1dOlSnTt3TiNGjMhwcfBb1bt3b40bN06RkZFKSkqyOe2NJI0dO1Z//vmnZs+erVWrVqlFixYqWbKkzp07p4MHD+qHH37QxIkTVbVq1Wwdt1mzZnr66af10UcfKTw8XA899JCcnZ21atUq+fr6qkSJEun6pV+/fvrtt980a9YslS9fXm3atFHp0qUVGxuro0eP6ttvv1Xfvn01e/bsHL8f999/v8aOHavXXntNYWFh6ty5s0JCQnT27Fn98MMPatCggaKioiRJLVu21OTJkzVq1ChVrFhR7du3V9myZXX16lUdP35c27ZtU5MmTSzzxd4JKleurE8++UT9+vVTeHi42rZtq0qVKikpKUn//POPvvvuOwUEBNhcqDxVfHy8WrVqpdDQUNWvX19lypRRQkKCNmzYoL1796pjx46Wz4Gzs7O+/PJLtWnTRh06dLAs8ufh4aETJ07ol19+0ZEjR3TmzBl5eHjc9vlVrVpV4eHh6tatm5ydnbVy5UodPnxYHTp00OOPP26p5+/vr2XLlqlLly5q0KCBWrZsqfDwcDk4OOiff/7R9u3bFRMTk24hxczMnDlTjRs31oABA/T111+rRo0aOnLkiL744gs9+OCDWrlyZYZ/a7Jy4cIFq78RFy5ckCT179/fMoXCyy+/bBn5FRoaqsmTJ+uFF15Q7dq19cgjj8jX11fr1q3TpUuXVKNGjXQ33wAAoGDJy3u2tGbPnq3GjRvrpZde0vr161W3bl2dOHFCS5culYODg+bNm2dzYetblToF1JNPPplhHT8/Pz300ENauHChFi1aZLWA9a0YNmyY1q1bpyZNmujhhx+Wr6+vfv31V33//ffq1q2bli1bdkv7K1WqlDp27KiVK1dKks24/vzzT3Xp0kV16tRRtWrVVKJECZ0/f14rV65UUlKSRo4cmekxNm7cqOHDh6tRo0aqXLmyAgMDdfLkSa1cuVImk0kvvfTSLcUMAIWeAeCOJynLn3nz5lnqR0ZGGpKMLVu2pNvX0aNHDUnGE088kW5bXFycMXHiROOee+4xPD09DTc3NyM0NNRo37698eGHHxpXr1611J03b1664/7XxYsXjcGDBxvFixc3XFxcjLCwMGPq1KnGTz/9ZEgyhg0blmHb559/3pBkjBgxIjtvUYbWrVtn9OzZ0wgNDTXc3NwMV1dXo2zZskbPnj2NtWvXpqsfHx9vTJw40QgPDzfc3NwMLy8vo3HjxsaiRYvS1c3svTQMw2jWrJmR2Z/Z1O1OTk7G2bNnM6xnNpuNBQsWGC1atDCKFCliODs7GyVKlDAaN25sTJw40fjnn3+yHZNhGEZKSooxbdo0IywszHBxcTGKFy9uDBgwwLh06ZLh5eVl1KpVy2a7VatWGR06dDACAgIMZ2dnIygoyKhXr54xevRoY+/evVZ1JRnNmjWzuZ8nnnjCkGQcPXo03bY1a9YYbdq0MYoUKWK4uLgYpUqVMjp37mxs2rQpXd3vvvvO6N69u1G8eHHD2dnZKFasmFGzZk3j+eefN3755ZcMzz83lClTxihTpozNbZn9+9u1a5fxxBNPGKVLlzZcXFyMIkWKGOHh4cbTTz+d7hz/+/m5ceOGMWXKFKNt27ZGSEiI4erqahQrVsyoX7++8cEHHxiJiYnpjhcdHW2MHDnSCA8PN9zd3Q1PT0+jQoUKxkMPPWT873//M5KSkrIVd0afq9QYExISjNGjRxuhoaGGi4uLUbZsWWPcuHFGQkKCzffo6NGjxsCBA40KFSoYrq6uhre3txEWFmY89thjxvLly63qZvZ5SbV3716jS5cuhq+vr+Hh4WE0aNDAWL16tfHWW28ZkowVK1Zk2DYzqeed2Y+t92vRokVG7dq1LX306KOPGqdOncrybwIAALh75eY9W3a2G4ZhnDx50nj22WeN0qVLG87Ozoa/v7/x4IMPGj///HO6utm5f/uvAwcOGJKMwMBA48aNG5nW3bZtmyHJuOeeeyxlObknWLVqlVG/fn3Dy8vL8PX1NVq3bm1s27Ytw/gzuy43DMNYuXKlIcm49957bW4/ceKEMWrUKKNRo0ZGUFCQ4eLiYpQsWdJo27at8fXXX1vVtXVNvGfPHuP555836tSpYxQrVsxwcXExypQpYzz00EPGDz/8kGFcAADbTIZxm6uiAsAtmjNnjp5++mnNnj07w6du7rvvPn3//fc6cOCAKlSokM8RFk4HDx5UpUqV1KNHDy1evNje4QC37dFHH9WiRYu0b98+hYWF2TscAAAA5LNXX31Vr732mubOnZtrU/oCAPIOa1YAyDO21hM4ceKEXnvtNTk7O+uBBx6w2W7Hjh367rvv1LZtWxIVeeDs2bPp5s6/fv26hg0bJunmNFzA3cJsNuvs2bPpyjdt2qTPPvtM4eHhJCoAAAAKobi4OM2ePVv+/v7q2bOnvcMBAGQDa1YAyDMPPfSQkpKSVKdOHfn5+enYsWNavXq1rl+/rjfffFPFixe3qv/+++/r5MmTmj9/vhwdHTVhwgQ7RV6wTZ8+XYsXL1ZERISKFy+us2fPatOmTTp58qQ6dOhAsgJ3lRs3bigkJETNmzdX5cqV5eTkpL///lsbNmyQq6urZs2aZe8QAeSSK1eu6LXXXtMff/yhnTt36sKFC4qMjMx0fvi0UtefSr0WqVmzpl5//XW1bNkybwMHAOSrVatW6ddff9Xq1at1/vx5vfPOO3J3d7d3WACAbCBZASDP9O7dW59++qmWL1+uixcvysvLSw0aNNDgwYPVuXPndPWnTp2qkydPqkKFCpoxY4bq1q2b/0EXAq1bt9bu3bu1adMmXbhwQY6OjgoLC9PQoUM1dOhQy8LBwN3A2dlZAwYM0JYtW/TLL7/o6tWrKlasmLp3765XXnlFNWvWtNSdPn26Ll26lOU+IyIiFBERkXdBA8iRmJgYffTRR6pZs6Y6d+6sjz/+ONttExMT1bJlS126dEkzZsxQYGCgZs6cqbZt22rjxo1q1qxZHkYOAMhPX3zxhebPn6/g4GCNGTNGQ4YMsXdIAIBsYs0KAABQKISGhur48eNZ1ruVJ7UB5J/U2xaTyaQLFy4oICAg2/9eZ82apYEDB+rHH39Uw4YNJUnJycmqWbOmvLy89NNPP+Vl6AAAAACygZEVAACgUDh27Ji9QwBwG25n5N/y5csVFhZmSVRIkpOTkx577DG98sorOnXqlEqWLJkbYQIAAADIIRbYBgAAAFCg7d69WzVq1EhXnlr2999/53dIAAAAAP6DkRX5xGw26/Tp0/L29mY+eAAAAGSbYRi6cuWKSpQoIQcHnjXKiZiYGBUtWjRdeWpZTExMhm0TExOVmJhoeW02mxUbGyt/f3+u6wEAAJBtXNdnjWRFPjl9+rRCQkLsHQYAAADuUidOnFCpUqXsHcZdK7PEQmbbJk2apPHjx+dFSAAAACiEuK7PGMmKfOLt7S3p5ofRx8cn345rNpt1/vx5BQQEkLEroOjjgo8+Lvjo44KPPi748rKP4+LiFBISYrmexK3z9/e3OXoiNjZWkmyOukg1atQoDR8+3PL68uXLKl26dL5f1wMAAODuxnV91khW5JPUp7V8fHzyPVmRkJAgHx8fvhwpoOjjgo8+Lvjo44KPPi748qOPmXIo56pXr66//vorXXlqWbVq1TJs6+rqKldX13Tl+X1dDwAAgIKB6/qMcbcMAAAAoEDr0qWL9u3bp59++slSlpycrIULF6p+/foqUaKEHaMDAAAAIDGyAgAAAMBdYu3atbp27ZquXLkiSdqzZ4+WLVsmSWrfvr08PDzUv39/zZ8/X4cPH1aZMmUkSf369dPMmTPVvXt3TZ48WYGBgZo1a5b279+vjRs32u18AAAAAPyLZAUAAACAu8Jzzz2n48ePW14vXbpUS5culSQdPXpUoaGhSklJUUpKigzDsNRzdXXVpk2bNGLECA0ePFjXr19XrVq1tHbtWjVr1izfzwMAAABAeiQrAAAAANwVjh07lmWdqKgoRUVFpSsPCgrS/Pnzcz8oAAAAALmCNSsAAAAAAAAAAIBdkawAAAAAAAAAAAB2RbICAAAAAAAAAADYFckKAAAAAAAAAABgVyQrAAAAAAAAAACAXZGsAAAAAAAAAAAAdkWyAgAAAAAAAAAA2BXJCgAAAAAAAAAAYFckKwAAAAAAAAAAgF2RrAAAAAAAAAAAAHZFsgIAAAAAAAAAANgVyQoAAAAAAAAAAGBXJCsKOHN8sowks73DAAAAAAAAAAAgQwU+WbF582b169dPlStXlqenp0qWLKkHH3xQv/32W7baR0VFyWQy2fw5e/ZsHkd/+y6vPqKU/x1XwoGL9g4FAAAAAAAAAACbnOwdQF774IMPFBMTo6FDh6pq1ao6f/683n77bTVo0EDr1q1TixYtsrWfefPmqXLlylZl/v7+eRFyrkk4dFHxO89LkmKj9ii+ZoD8OpaTo7eLnSMDAAAAAAAAAOBfBT5ZMXPmTAUGBlqVtW3bVhUqVNAbb7yR7WRFtWrVVLdu3bwIMc84+rrKpayPbhyNkyTF/3leCfsvyrd9qDzrBsvkYLJzhAAAAAAAAAAAFIJpoP6bqJAkLy8vVa1aVSdOnLBDRPnHOcBD/k9Wk0PrIJncb+aljIRkXfrykM5/tEtJ567bOUIAAAAAAAAAAApBssKWy5cv6/fff1d4eHi223Ts2FGOjo4qWrSounbtqt27d+dhhLnHZDLJIdxHgc/fI4/a/yZubhyLU/SM33V5/TEW4AYAAAAAAAAA2FWBnwbKloEDB+ratWsaPXp0lnWDg4M1evRoNWjQQD4+Pvrrr780efJkNWjQQD/88INq1qxps11iYqISExMtr+Pibk7FZDabZTbnX3LAbDbLMAyZPBzl172i3GsH6NKKw0qJTZBSDF3ZfELX/zwvv87l5VreL9/iQu5J7eP8/Fwhf9HHBR99XPDRxwVfXvYxnxsAAAAAhYHJMAzD3kFk19atW9W8efNs1d25c6dq1aqVrnzs2LF6/fXX9d5772nQoEE5iuPYsWOqXr26WrRooZUrV9qsM27cOI0fPz5d+YEDB+Tt7Z2j4+aE2WzW5cuX5evrKweHmwNpjGSzzD/Hyvj1opTm3tdUxVsO9wXI5O6Yb/Hh9tnqYxQs9HHBRx8XfPRxwZeXfXzlyhVVqlRJly9flo+PT67uG7cuLi5Ovr6+9AcAAABuCdeRWburRlaEhYVpzpw52apbunTpdGXjx4/X66+/rokTJ+Y4USFJoaGhatKkiXbs2JFhnVGjRmn48OGW13FxcQoJCVFAQEC+fhjNZrNMJpMCAgKsb5w7Byup4XVdWn5ISf9ckSQZe6/IOB4v7/Zl5V47QCYTC3DfDTLsYxQY9HHBRx8XfPRxwZeXfezm5par+wMAAACAO9FdlawoXry4nnzyyRy1HT9+vMaNG6dx48bplVdeue1YDMPI9EbU1dVVrq6u6codHBzy/UsKk8lk87iuxb0U+GxNXfvlrC6vPSojIUXm68m6tOyg4v84L7/OFeRczD1fY0XOZNTHKDjo44KPPi746OOCL6/6mM8MAAAAgMKgUNz5vPbaaxo3bpzGjBmjyMjI297f0aNH9cMPP6hBgwa5EJ19mRxM8qpfXMHD68q9RjFLeeKhS4qe/pviNv0jI5l5kgEAAAAAAAAAeeeuGlmRE2+//bZeffVVtW3bVh06dEg3dVPahEP//v01f/58HT58WGXKlJEktWrVSvfdd59q1KhhWWD7zTfflMlk0muvvZav55KXHH1c5N+riuLvidWlFYeUcilRSjYUt+G4rv95XkW6VpBrqK+9wwQAAAAAAAAAFEAFPlmxatUqSdI333yjb775Jt32tOuLp6SkKCUlxaqsevXq+uyzzzR16lTFx8crMDBQLVq00NixY1WpUqW8P4F85l65qFyH11HcxuO6+v0pySwln7uu87N3yfPeYPm2DZWDh7O9wwQAAAAAAAAAFCAFPlmxdevWbNeNiopSVFSUVdk777yTuwHdBRxcHOXXvpw8agXq4pcHlXTyqiTp2s9nFb8nRn6dysu9RjEW4AYAAAAAAAAA5IpCsWYFcsalhJcCB9SSb6dyMrk4SpLMV5MUu3ifLsz7W8mxCXaOEAAAAAAAAABQEJCsQKZMDiZ5Ny6poBfqyK2qv6U88cBFRb/zm65sOykjhQW4AQAAAAAAAAA5R7IC2eLk66pivavK//EqcvRxkSQZSWZdXntU597/QzdOXLFzhAAAAAAAAACAuxXJCtwS9/BiChpeR16NSkj/v2RF0plrOjfrD1366rDMCcn2DRAAAAAAAAAAcNchWYFb5uDmJL8HyitwQC05F/e8WWhIV388rehpvyl+9wX7BggAAAAAAAAAuKuQrECOuYR4K3BQbfm2LyuT882PUkrcDcUs3KsLC/Yo+VKinSMEAAAAAAAAANwNSFbgtpgcTfK+r5SCnq8jt7AilvKEPTGKnvabrvxwSobZsGOEAAAAAAAAAIA7HckK5Aqnom7y7xOuoj0ry8HLWZJk3EjR5VVHdG7WH7px6qqdIwQAAAAAAAAA3KlIViDXmEwmedQMUPDwOvKsH2wpTzp5Vedm7tSlNUdkvpFixwgBAAAAAAAAAHcikhXIdQ4ezirSpaICnq0hp0CPm4Vm6ep3p24uwL0v1r4BAgAAAAAAAADuKCQrkGdcQ30VNKS2fO4vIzmZJEkplxIVE/W3Yj7dq5S4G3aOEAAAAAAAAABwJyBZgTxlcnKQT4vSChpWR67lfS3l8X9d0Nlpv+rqjjMswA0AAAAAAAAAhRzJCuQL52LuKvZkdRV5uJIcPJ0kSUZCii6tOKTzH+5S0tlrdo4QAAAAAAAAAGAvJCuQb0wmkzzvCVLQ8LryqBNkKb9xPE7R7+7U5XXHZCSxADcAAAAAAAAAFDYkK5DvHD2dVbR7JRV7qrqcirnfLDQburLlhM5O/10JBy/aN0AAAAAAAAAAQL4iWQG7cSvvp6Ch98i7RYjk+P8LcMck6MLc3Yr9bL9SrrIANwAAAAAAAAAUBiQrYFcmZwf53h+qoKH3yCXUx1J+fec5RU/7Tdd+PSvDYAFuAAAAAAAAACjISFbgjuAc6KGAp2uoSNeKMrndXIDbfD1ZF5cd1PmP/lLS+et2jhAAAAAAAAAAkFdIVuCOYXIwyfPeYAW/UEfuNQMs5TeOXlb09N8Vt/G4jGSzHSMEAAAAAAAAAOQFkhW44zh6u8i/Z2UV6xsux6JuNwtTDMVt/EfRM35X4pHL9g0QAAAAAAAAAJCrSFbgjuUWVlRBw+6Rd7NSlk9q8vl4nf9ol2KXHZD5epJ9AwQAAAAAAAAA5AqSFbijObg4yrddWQUOvkcuId6W8uu/Ruvs27/p+s5zLMANAAAAAAAAAHc5khW4K7gU91TAczXl92B5mVwdJUnma0mK/Wy/LnyyW8kx8XaOEAAAAAAAAACQUyQrcNcwOZjk1bCEgofXkXs1f0t54sFLOvvO74rbekJGCgtwAwAAAAAAAMDdhmQF7jqOvq7yf6yq/HtXlaOv683CZLPivjmmc+/tVOI/cfYNEAAAAAAAAABwS5zsHQCQU+5V/eVa3ldx64/r6o+nJUNKOntd5z/4U85BHpLJZO8Q84UhKTkpSeecT6twnHHhQx8XfPRxwUcfFywO3i4K6FfN3mEAAAAAQIFCsgJ3NQdXJ/l1Ki+P2oG6+OVBJZ2+ZklaFDbJumHvEJDH6OOCjz4u+OjjgsExPtneIQAAAABAgUOyAgWCSylvBQ6sras/ntLV704p5Xoh+xLBMArNSJJCiz4u+Ojjgo8+LjBMTsykCgAAAAC5jWQFCgyTo0neTUvJu2kpe4eSr8xms86dO6fAwEA5OPDlSUFEHxd89HHBRx8DAAAAAJA57pYLgeQUQ1cSkuwdBgAAAAAAAAAANpGsKMAORF9Ru3e/V8TMnZqx6ZC9wwEAAAAAAAAAwCamgSrAini4aP/ZK5Kk/dFX7BwNAAAAAAAAAAC2MbKiACvm5aKiHs6SpAPRV+0cDQAAAAAAAAAAtpGsKMBMJpMqBXlLks5fSVTstRt2jggAAAAAAAAAgPRIVhRwqckK6eYaFgAAAAAAAAAA3GlIVhRwlYK8LL+TrAAAAAAAAAAA3IlIVhRwYcH/jqzYd5ZkBQAAAAAAAADgzkOyooCrmHZkBckKAAAAAAAAAMAdiGRFAefj5qwgb2dJ0v7oKzIMw84RAQAAAAAAAABgjWRFIVDO312SdCUhWWfjEuwcDQAAAAAAAAAA1khWFAKpyQpJ2s9UUAAAAAAAAACAOwzJikKgQjGSFQAAAAAAAACAOxfJikLAamRFNMkKAAAAAAAAAMCdpcAnK7Zu3SqTyWTzZ8eOHdnax7lz59SnTx8VK1ZMHh4eatiwoTZt2pTHkeeeMkXd5GC6+fsBkhUAAAAAAAAAgDuMk70DyC9vvPGGmjdvblVWrVq1LNslJiaqZcuWunTpkmbMmKHAwEDNnDlTbdu21caNG9WsWbO8CjnXuDk5qIy/p45euKaD0VeVYjbkmJq9AAAAAAAAAADAzgpNsqJixYpq0KDBLbebO3eudu/erR9//FENGzaUJDVv3lw1a9bUiBEj9NNPP+V2qHmiUpCXjl64psRks/6Jva6yxTztHRIAAAAAAAAAAJIKwTRQt2v58uUKCwuzJCokycnJSY899ph+/vlnnTp1yo7RZV9YkLfl9/1n4+wYCQAAAAAAAAAA1gpNsmLgwIFycnKSj4+P2rRpo++//z5b7Xbv3q0aNWqkK08t+/vvv3M1zrxSKcjL8vv+s1ftGAkAAAAAAAAAANYK/DRQvr6+Gjp0qCIiIuTv769Dhw7prbfeUkREhNasWaM2bdpk2j4mJkZFixZNV55aFhMTY7NdYmKiEhMTLa/j4m6OZjCbzTKbzTk9nVtmNptlGIYqBv477dP+s3H5GgPyVmof06cFF31c8NHHBR99XPDlZR/zuQEAAABQGNxVyYqtW7emWyQ7Izt37lStWrVUu3Zt1a5d21LetGlTdenSRdWrV9eIESOyTFZIksmU8WLUGW2bNGmSxo8fn678/PnzSkhIyMYZ5A6z2azLly/L09ssZ0eTklIM7Tl9SefOncu3GJC3UvvYMAw5OBSawVKFCn1c8NHHBR99XPDlZR9fuXIlV/cHAAAAAHeiuypZERYWpjlz5mSrbunSpTPc5ufnp44dO2r27NmKj4+Xu7t7hnX9/f1tjp6IjY2VJJujLiRp1KhRGj58uOV1XFycQkJCFBAQIB8fn2ydQ24wm80ymUwKCAhQhUAv7T1zRScuJcq3qL9cnRzzLQ7knbR9zBdgBRN9XPDRxwUffVzw5WUfu7m55er+AAAAAOBOdFclK4oXL64nn3wyV/ZlGIakzEdNSFL16tX1119/pStPLatWrZrNdq6urnJ1dU1X7uDgkO9fUphMJjk4OKhysI/2nrmiFLOhoxfiVbVE/iVNkLdS+5gvwAou+rjgo48LPvq44MurPuYzAwAAAKAwKJR3PhcvXtTq1atVq1atLJ9U69Kli/bt26effvrJUpacnKyFCxeqfv36KlGiRF6Hm2sqBXlbfj8QzXQCAAAAAAAAAIA7w101siInevXqpdKlS6tu3boqVqyYDh48qLffflvR0dGKioqyqtu/f3/Nnz9fhw8fVpkyZSRJ/fr108yZM9W9e3dNnjxZgYGBmjVrlvbv36+NGzfa4YxyLizYy/L7fpIVAAAAAAAAAIA7RIFPVtSoUUOfffaZZs+eratXr6po0aJq0qSJ/ve//6levXpWdVNSUpSSkmKZIkq6OZ3Tpk2bNGLECA0ePFjXr19XrVq1tHbtWjVr1iy/T+e2WI2sOEuyAgAAAAAAAABwZyjw00C9/PLL2rlzpy5duqTk5GSdO3dOX375ZbpEhSRFRUXJMAyFhoZalQcFBWn+/PmKiYlRfHy8tm/frlatWuXTGeSekn7u8nK9mZ9iZAUAAADuNlevXtWwYcNUokQJubm5qVatWlqyZEm22m7ZskWtW7dWYGCgvLy8VKNGDb377rtKSUnJ46gBAAAAZEeBT1bgXyaTSZWCbk4FdfJivK4mJts5IgAAACD7unbtqvnz5ysyMlJr165VvXr11LNnTy1atCjTdhs3blSrVq2UnJysOXPmaMWKFYqIiNDQoUM1fPjwfIoeAAAAQGYK/DRQsBYW7K3f/7kk6eYi2/eULmLfgAAAAIBs+Prrr7VhwwYtWrRIPXv2lCQ1b95cx48f10svvaRHHnlEjo6ONttGRUXJ2dlZq1evlqenpySpVatW2r9/v6KiojRjxox8Ow8AAAAAtjGyopBh3QoAAADcjZYvXy4vLy91797dqrxv3746ffq0fvrppwzbOjs7y8XFRe7u7lblfn5+cnNzy5N4AQAAANwakhWFTFiaZAXrVgAAAOBusXv3blWpUkVOTtaDw2vUqGHZnpFnn31WN27c0JAhQ3T69GldunRJ//vf/7R8+XKNGDEiT+MGAAAAkD1MA1XIVApOM7KCZAUAAADuEjExMSpXrly68qJFi1q2Z6R+/fravHmzunfvrpkzZ0qSHB0dNWnSJL3wwguZHjcxMVGJiYmW13FxcTkJHwAAAEAWSFYUMsW8XFXMy0UXrt7QfqaBAgAAwF3EZDLlaNtvv/2mLl26qH79+vrwww/l6empzZs3a8yYMUpISNDYsWMzbDtp0iSNHz/+tuIGAAAAkDWSFYVQpSBvXbgaowtXb+jC1UQV83K1d0gAAABApvz9/W2OnoiNjZX07wgLWwYOHKigoCAtX77csgh38+bN5eDgoHHjxunRRx+1OWpDkkaNGqXhw4dbXsfFxSkkJOR2TgUAAACADaxZUQhZLbLNVFAAAAC4C1SvXl179+5VcnKyVflff/0lSapWrVqGbf/44w/VqVPHkqhIVa9ePZnNZu3duzfDtq6urvLx8bH6AQAAAJD7SFYUQmFp161gKigAAADcBbp06aKrV6/qiy++sCqfP3++SpQoofr162fYtkSJEvr111+VkpJiVb59+3ZJUqlSpXI/YAAAAAC3hGmgCqG0Iyv2R1+1YyQAAABA9rRr106tW7fWc889p7i4OFWoUEGLFy/WN998o4ULF1pGTfTv31/z58/X4cOHVaZMGUnS888/ryFDhqhTp0565pln5OHhoU2bNuntt99Wq1atVLNmTXueGgAAAACRrCiUKgV5WX7ffzbOjpEAAAAA2ffll19q9OjRevXVVxUbG6vKlStr8eLF6tGjh6VOSkqKUlJSZBiGpWzw4MEqWbKk3nnnHT355JOKj49XaGioIiMj9fzzz9vjVAAAAAD8B8mKQsjbzVkl/dx16lK8DkRflWEYMplM9g4LAAAAyJSXl5dmzJihGTNmZFgnKipKUVFR6cq7du2qrl275mF0AAAAAG4Ha1YUUqnrVlxNTNbpywl2jgYAAAAAAAAAUJiRrCik0q5bwSLbAAAAAAAAAAB7IllRSIUFp1m3IppkBQAAAAAAAADAfkhWFFJpR1bsZ2QFAAAAAAAAAMCOSFYUUuUDvOTocHNRbZIVAAAAAAAAAAB7IllRSLk5OyrU30OSdOj8VSWnmO0cEQAAAAAAAACgsCJZUYiFBd+cCupGslnHY6/bORoAAAAAAAAAQGFFsqIQS7tuxQGmggIAAAAAAAAA2AnJikIsLE2yYh/JCgAAAAAAAACAnZCsKMRSp4GSpAPRJCsAAAAAAAAAAPZBsqIQK+PvKRenmx+B/SQrAAAAAAAAAAB2QrKiEHN0MKlioJck6diFa0pISrFzRAAAAAAAAACAwohkRSGXum6F2ZAOn79q52gAAAAAAAAAAIURyYpCrlKadSv2s8g2AAAAAAAAAMAOSFYUcmkX2WbdCgAAAAAAAACAPZCsKORSp4GSpAOMrAAAAAAAAAAA2AHJikKuuK+bvF2dJEkHolmzAgAAAAAAAACQ/0hWFHImk8mybsWpS/G6kpBk54gAAAAAAAAAAIUNyQqoUtqpoFi3AgAAAAAAAACQz0hWQJXTLrJ9lqmgAAAAAAAAAAD5i2QFGFkBAAAAAAAAALArkhVQpSAvy+/7z5KsAAAAAAAAAADkL5IVkL+Xq4p5uUpiZAUAAAAAAAAAIP+RrIAkKSz45uiKmGs3dOFqop2jAQAAAAAAAAAUJiQrIEkKC/Kx/M5UUAAAAAAAAACA/ESyApL+HVkhkawAAAAAAAAAAOQvkhWQJFUK8rb8zroVAAAAAAAAAID8RLICkqSKaZIV+0lWAAAAAAAAAADyEckKSJK8XJ1Uqoi7JOnA2SsyDMPOEQEAAAAAAAAACguSFbCoHHxzdMW1Gyk6eTHeztEAAAAAAAAAAAqLAp+s6NOnj0wmU4Y/O3bsyLR9VFRUhm3Pnj2bT2eRP1i3AgAAAAAAAABgD072DiCvjR07Vs8++2y68k6dOsnV1VX16tXL1n7mzZunypUrW5X5+/vnSox3irBg63UrWlYJsmM0AAAAAAAAAIDCosAnK8qXL6/y5ctblW3btk0XLlzQmDFj5OjomK39VKtWTXXr1s2LEO8YViMrzjKyAgAAAAAAAACQPwr8NFC2zJ07VyaTSf369bN3KHeUcgGecnQwSZL2R1+1czQAAAAAAAAAgMKi0CUrLl++rGXLlqlly5YqW7Zsttt17NhRjo6OKlq0qLp27ardu3fnYZT24erkqHLFPCVJh89dVVKK2c4RAQAAAAAAAAAKgwI/DdR/LV68WPHx8erfv3+26gcHB2v06NFq0KCBfHx89Ndff2ny5Mlq0KCBfvjhB9WsWdNmu8TERCUmJlpex8XFSZLMZrPM5vxLApjNZhmGke1jVgry0sFzV3Ujxayj56+qQqBXHkeI23WrfYy7D31c8NHHBR99XPDlZR/zuQEAAABQGNxVyYqtW7eqefPm2aq7c+dO1apVK1353Llz5e/vry5dumRrP23btlXbtm0tr++77z516NBB1atX16uvvqqVK1fabDdp0iSNHz8+Xfn58+eVkJCQrWPnBrPZrMuXL8swDDk4ZD2QpqTXv3V+OXBKPiqSl+EhF9xqH+PuQx8XfPRxwUcfF3x52cdXrrCWGAAAAICC765KVoSFhWnOnDnZqlu6dOl0Zbt27dKvv/6qoUOHytXVNcdxhIaGqkmTJtqxY0eGdUaNGqXhw4dbXsfFxSkkJEQBAQHy8fHJ8bFvldlslslkUkBAQLZunGuXM0vbT0uSziY4KDAwMK9DxG261T7G3Yc+Lvjo44KPPi748rKP3dzccnV/AAAAAHAnuquSFcWLF9eTTz6Z4/Zz586VpNvaR6qsnppzdXW1mRBxcHDI9y8pTCZTto9bubiv5feD0Vf5QuUucSt9jLsTfVzw0ccFH31c8OVVH/OZAQAAAFAYFJo7n8TERC1cuFD33nuvqlWrdlv7Onr0qH744Qc1aNAgl6K7c5Qu6iE355sfi/3RTDkAAAAAAAAAAMh7hSZZsWLFCsXGxmY6qqJ///5ycnLS8ePHLWWtWrXShAkTtGLFCm3evFkzZsxQkyZNZDKZ9Nprr+VH6PnK0cGkioHekqRjMdeUkJRi54gAAAAAAAAAAAXdXTUN1O2YO3euPD091aNHjwzrpKSkKCUlRYZhWMqqV6+uzz77TFOnTlV8fLwCAwPVokULjR07VpUqVcqP0PNdpSBv/XXqsgxDOnTuqqqV9M26EQAAAAAAAAAAOVRokhXr16/Psk5UVJSioqKsyt555508iujOFRbsZfl9/9krJCsAAAAAAAAAAHmq0EwDheyrFORt+f0A61YAAAAAAAAAAPIYyQqkUznYx/L7vrMkKwAAAAAAAAAAeYtkBdIJ8nGVj9vNGcIYWQEAAAAAAAAAyGskK5COyWRSWPDNqaDOXE7Q5fgkO0cEAAAAAAAAACjISFbAprTrVhxkdAUAAAAAAAAAIA+RrIBNqSMrJGk/yQoAAAAAAAAAQB4iWQGbwtKMrNjPItsAAAAAAAAAgDxEsgI2VSJZAQAAAAAAAADIJyQrYFMRTxcFertKkg5EX5FhGHaOCAAAAAAAAABQUJGsQIZS1624eD1J568m2jkaAAAAAAAAAEBBRbICGUo7FdSBs1ftGAkAAAAAAAAAoCAjWYEMpY6skKR9Z+PsGAkAAAAAAAAAoCAjWYEMhaUdWRHNItsAAAAAAAAAgLxBsgIZqhjkZfl9fzTTQAEAAAAAAAAA8gbJCmTIw8VJpYt6SJIORl+R2WzYOSIAAAAAAAAAQEFEsgKZSl1k+/qNFJ26FG/naAAAAAAAAAAABRHJCmSqstUi26xbAQAAAAAAAADIfSQrkKlKwSyyDQAAAAAAAADIWyQrkKmwoH+TFfsZWQEAAAAAAAAAyAMkK5CpssU85eRgksTICgAAAAAAAABA3iBZgUy5ODmoXICnJOnw+atKSjHbOSIAAAAAAAAAQEFDsgJZCgv2kSQlpRg6euGanaMBAAAAAAAAABQ0JCuQpbAgL8vvrFsBAAAAAAAAAMhtJCuQpUppFtlm3QoAAAAAAAAAQG4jWYEshQX/m6xgZAUAAAAAAAAAILeRrECWQop4yN3ZURIjKwAAAAAAAAAAuY9kBbLk4GBSpf9ft+J47HVdv5Fs54gAAAAAAAAAAAUJyQpkS+q6FYYhHTp31c7RAAAAAAAAAAAKEpIVyBbWrQAAAAAAAAAA5BWSFciW1JEVEutWAAAAAAAAAAByF8kKZIvVyIpopoECAAAAAAAAAOQekhXIlkBvV/l5OEuS9p+Ns3M0AAAAAAAAAICChGQFssVkMlmmgoqOS9Sl6zfsHBEAAAAAAAAAoKAgWYFsC7Nat4KpoAAAAAAAAAAAuYNkBbKtktW6FSyyDQAAAAAAAADIHSQrkG1WIyvOkqwAAAAAAAAAAOQOkhXItrTJiv0kKwAAAAAAAAAAuYRkBbLN18NZwT5ukm5OA2UYhp0jAgAAAAAAAAAUBE72DgB3l0rB3jobl6DL8Uk6dyVRQf+fvAAAIKdSUlKUlJRk7zDylNlsVlJSkhISEuTgwLMiBVFO+9jZ2VmOjo55GFnBcvXqVY0ZM0aff/65YmNjVblyZb388svq0aNHttqvXLlS06ZN086dO5WSkqLQ0FANHTpUTz/9dB5HDgAAACArJCtwS8KCvPTtgfOSbk4FRbICAJBThmHo7NmzunTpkr1DyXOGYchsNuvKlSsymUz2Dgd54Hb62M/PT8HBwXw2sqFr16765ZdfNHnyZFWqVEmLFi1Sz549ZTab1atXr0zbTp48WaNHj9azzz6rUaNGydnZWfv27dONGzfyKXoAAAAAmSFZgVtSKe0i29FXdF+lADtGAwC4m6UmKgIDA+Xh4VGgv6g1DEPJyclycnIq0OdZmOWkjw3D0PXr13Xu3DlJUvHixfMyxLve119/rQ0bNlgSFJLUvHlzHT9+XC+99JIeeeSRDEep/Pbbbxo9erQmTZqkESNGWMpbtmyZL7EDAAAAyBrJCtySysE+lt/3scg2ACCHUlJSLIkKf39/e4eT50hWFHw57WN3d3dJ0rlz5xQYGMiUUJlYvny5vLy81L17d6vyvn37qlevXvrpp5/UqFEjm23ff/99ubq6avDgwfkRKgAAAIAcuCsnTb5y5YpGjBih+++/XwEBATKZTBo3blyG9X///Xe1atVKXl5e8vPzU9euXXXkyJFsH2/jxo1q2LChPDw8VKxYMfXp08fyBFxhUyHQS6n33weiSVYAAHImdY0KDw8PO0cC2F/qv4OCvnbL7dq9e7eqVKkiJyfr561q1Khh2Z6Rb7/9VlWqVNEXX3yhsLAwOTo6qlSpUnr55ZeZBgoAAAC4Q9yVyYqYmBh99NFHSkxMVOfOnTOtu2/fPkVEROjGjRv6/PPP9cknn+jAgQNq2rSpzp8/n+Wxtm3bpnbt2ikoKEgrV67UjBkztHHjRrVs2VKJiYm5dEZ3D3cXR5UpevOG+kD0FZnNhp0jAgDczRhlAPDvILtiYmJUtGjRdOWpZTExMRm2PXXqlA4ePKghQ4ZoyJAh2rhxo/r06aOpU6eqb9++mR43MTFRcXFxVj8AAAAAct9dOQ1UmTJldPHiRZlMJl24cEEff/xxhnVfffVVubq6avXq1fLxuTmFUZ06dVSxYkVNnTpVU6ZMyfRYL730kipVqqRly5ZZnuIqW7asGjdurE8++UTPPfdc7p3YXaJSkLeOxVxXQpJZJy5eVxl/T3uHBAAAgEIgs8ROZttSFz9fvHixevToIenmehfXrl3T9OnTNX78eFWoUMFm20mTJmn8+PG3FzgAAACALN2VIytMJlO2nkBLTk7W6tWr9dBDD1kSFdLNZEfz5s21fPnyTNufOnVKv/zyix5//HGr4eaNGjVSpUqVsmxfUIUF/7vI9n7WrQAAAEA+8Pf3tzl6IjY2VpJsjrpI21aS2rRpY1Xerl07STenjc3IqFGjdPnyZcvPiRMnbjl2AAAAAFm7K5MV2XX48GHFx8db5rFNq0aNGjp06JASEhIybJ86721G7TObF7cgI1kBAEDmoqKiLA9XmEwmOTs7q2zZsurXr59OnTp1S/uKiIhQREREjmOJjo7WK6+8olq1asnHx0cuLi4qVaqUunbtqq+++kopKSnp2hw5ckSDBg1SpUqV5O7uLg8PD4WHh2vMmDFW8ffp00cmk0nh4eE292MymTRo0CDL62PHjlnekyVLlqSrP27cOMvI2Vuxd+9ePf744ypXrpzc3NxUrFgx3XPPPRo0aJDVlD2LFi3S9OnTs73f1HijoqJuKR7kjerVq2vv3r1KTk62Kv/rr78kSdWqVcuwra3reenmwuiS5OCQ8W2Rq6urfHx8rH4AAAAA5L67chqo7Ep98iqjuW0Nw9DFixdVvHjxHLXPbF7cxMREqzUtUm+UzWazzGZz9k/iNpnNZhmGkavHrBjw77RP+89eydfzQXp50ce4s9DHBV9h7OPUc079KWhSz+mTTz5R5cqVFR8fr61bt+rNN9/Utm3btGvXLnl63to0ijl5n3bs2KEHH3xQhmHo2WefVYMGDeTl5aV//vlHq1evVteuXTV79mz179/f0mb16tXq2bOnihUrpoEDB6p27doymUz666+/NG/ePK1ZsybdU+h79uzRvHnzrPaTNu7U2NOew+jRo9W1a1c5OzunO8db+Vzs3LlTTZo0UZUqVTR27FiFhobqwoUL+vPPP/XZZ5/phRdekLf3zQctFi1apN27d2vo0KHZ2vetxmPrPLN7nNS/Abb+DhSmvw2Z6dKli+bMmaMvvvhCjzzyiKV8/vz5KlGihOrXr59h24ceekjr16/X2rVr1atXL0v5119/LQcHB9WrVy9PYwcAAACQNbsnK7Zu3armzZtnq+7OnTtVq1atWz5GTue2zapOZm0zmtv2/PnzmY7myG1ms1mXL1+WYRiZPjF2KzzNhpwcTEo2G9pz+qLOnTuXK/tFzuRFH+POQh8XfIWxj5OSkmQ2m5WcnJzuKemCIPXL5SpVqqhOnToyDEMNGzZUSkqK3njjDX3xxRdWX5hmJvWL71t9ny5duqQuXbrIy8tLW7dutXo4o1GjRurRo4dGjBih2NhYy76PHj2qnj17qmLFitqwYYN8fX0tbe677z4NGDBAK1assNQ3m83y9PRU7dq1NW7cOD388MNyd3dPF39q/dT/tm3bVt98841mzZqlgQMHpnvfbuVz8c4778jBwUEbNmywJCUkqXPnzoqMjLQ6/q2+l2nPM6s2hmFYRpfc6oLZycnJMpvNiomJsUrepLpyhZGs0s0pm1q3bq3nnntOcXFxqlChghYvXqxvvvlGCxculKOjoySpf//+mj9/vg4fPqwyZcpIkvr27asPP/xQAwYM0IULF1S1alVt3LhRM2fO1IABAyz1AAAAANiP3ZMVYWFhmjNnTrbqli5d+pb2nTo3bUZz25pMJvn5+eW4fWbz4o4aNUrDhw+3vI6Li1NISIgCAgLydei42WyWyWRSQEBArn4BVj7QS/vPXtE/FxPlV7SYXJwKx5drd6K86mPcOejjgq8w9nFCQoKuXLkiJycnq3WhCorUfnR0dEy37pUknTx5UsnJyRo/frw+++wznTp1SgEBAXrwwQc1ceJEq+uT1C++nZycZBiGwsLCVK5cOX3zzTdWx7x69apKlSqlRx99VDNnztS8efMUHR2tzz//XCEhITbjvOeee6xev/vuu7p27ZpmzZpluQ76r+7du6c7zylTpqhx48aaOXOmXn75Zav6JpPJ8h6k/rdFixYyDENvvPGG+vXrZ0kypO7vVj4XFy9elI+Pj/z8/DJNEjRv3lzbtm2TJLm4uFjKUxMkp0+f1vPPP6+1a9fKwcFBbdu21bBhwyxxZTceW8mGrDg5OcnBwUH+/v5yc3NLt91WWWH15ZdfavTo0Xr11VcVGxurypUrWy2aLUkpKSlKSUmxGuHi7OysDRs26JVXXtEbb7yh2NhYlS1bVpMnT7a6ZgcAAABgP3b/dqB48eJ68skn82Tf5cuXl7u7u2Ue27T++usvVahQIdObv9R5b//66y+1b98+XfvM5sV1dXWVq6trunIHB4d8/yLKZDLl+nHDgry1/+wVJZsNHY+Nt1rHAvkvL/oYdxb6uOArbH3s4OBgtaZDQZN6TqnnZxiGTCaTDh06JEkKDAxUly5dtGnTJo0aNUpNmzbVrl27FBkZqR07dmj79u3priNS9zV48GANGzZMhw4dUsWKFS3b//e//ykuLk6DBg2SyWTSxo0b5ejoqA4dOmT7Pd6wYYOCgoLUsGHDWzrfRo0aqUuXLnrzzTf1zDPPWD3QkbaP0/53ypQpql27tqZOnaoJEybYfN+yo2HDhlqzZo0ee+wxPfPMM7r33nvTje6QpFmzZunpp5/W4cOHtXz5cqv44uPj1bp1a50+fVqTJk1SpUqVtGbNGssX4NmJJ7WP055HdqXuP6O/AYXl70J2eHl5acaMGZoxY0aGdaKiomyuM1K0aFHNnj1bs2fPzsMIAQAAAOSU3ZMVecnJyUmdOnXSl19+qTfffNPy1N4///yjLVu26Pnnn8+0fcmSJXXvvfdq4cKFevHFFy1Dy3fs2KH9+/dbnrYrjMKCvaU/b/6+72wcyQoAQK7o9N73On8lMeuK+STA21WrBjfJcfuUlBQlJycrPj5emzdv1sSJE+Xt7S0fHx+tW7dOb775pl566SVJUuvWrRUSEqJHHnlECxYs0FNPPWVzn3379tWYMWM0c+ZMq8WiZ86cqebNm6tq1aqSpBMnTiggIEAeHh5W7f+7LkLaL8j/+eefHE25Kd2cAjM8PFxvvPGGpk6dmmX9mjVrqlevXpo2bZoGDBig4ODgHB33xRdf1K+//qrFixdr8eLFcnR0VI0aNdS+fXsNHTpUAQEBkqSqVavKz89Prq6uatCggdU+5s+fr71792rlypV64IEHJEn333+/4uPjsz0CGAAAAABwe+7ax7TWrl2rZcuWadWqVZJuLuy4bNkyLVu2TNevX7fUGz9+vK5fv66OHTtq7dq1Wr58uTp06KBixYrphRdesNqnk5OTWrZsaVU2ZcoU7du3T927d9fGjRu1aNEiPfzww6pWrZr69u2b9yd6hwoL+jc5cSCaeZQBALnj/JVEnY1LuGN+bjdx0qBBAzk7O8vHx0edO3dWcHCw1q5da1mguk+fPlb1u3fvLk9PT23atCnDfXp7e6tv376KiorStWvXJEmbN2/Wnj17NGjQoCxjGj58uJydnS0/qV/O366wsDD1799f77//vv75559stXn99deVlJRkc52v7HJ1ddXy5cu1Z88evfPOO+rRo4fOnz+viRMnqkqVKtq/f3+W+9iyZYu8vb3TvRfZXVcEAAAAAHD77tqRFc8995yOHz9ueb106VItXbpU0s3FIUNDQyVJlStX1tatWzVy5Eh169ZNTk5OatGihaZOnWp50i5V6vy2aUVEROjrr7/Wq6++qk6dOsnDw0MdO3bUW2+9ZXOap8Ii7UiK/Wev2jESAEBBEuB9Z/2/9XbjWbBggapUqSJHR0f5+/srJCREJpNJ8+bNk5OTU7prEZPJpODgYJvrZaU1ePBgvf/++/r000/19NNP6/3331epUqX04IMPWuqULl1aBw8e1PXr161GV7zwwgt67LHHJCndl/OlS5fW0aNHc3y+48aN08KFCzV27FjNnz8/y/qhoaEaMGCA3n///dteN6BKlSqqUqWKpJtTMk2fPl3Dhw/X2LFj9fnnn2faNiYmRkFBQenKczraAwAAAABw6+7aZMWxY8eyXbdOnTrauHFjlvXSLsKXVuvWrdW6detsH68wKOnnLg8XR12/kcLICgBArrmdKZfuRFWqVFHdunVlGIaSk5Mt5f7+/kpOTtb58+etEhaGYejs2bOqV69epvutUKGC2rVrp5kzZ6pdu3b66quvNH78eMuUldLN65f169fr66+/Vrdu3SzlISEhlgW30y40LUlt2rTRe++9px07dqSbKik7ihcvrmHDhmny5MnpRrBmZMyYMfrkk0/0yiuvKDw8/JaPaYvJZNLzzz+vCRMmaPfu3VnW9/f3188//5yu/OzZs7kSDwAAAAAga3ftNFCwLwcHkyr+/1RQ/8Re1/UbyVm0AAAAqVKnnVy4cKFV+RdffKFr166lm5bSlqFDh2rXrl164okn5OjomG6NiyeffFJBQUEaMWKEzpw5k624nn/+eXl6emrAgAG6fPlyuu2GYVgtTm3LyJEjVbRoUb388svZOqa/v79GjhypZcuW2UwYZCWjczt9+rTi4uJUokQJS5mrq6vi4+PT1W3evLmuXLmir776yqp80aJFtxwPAAAAACBn7tqRFbC/ykHe+vPEJUnSgeirqhXiZ9d4AAC4W7Ru3Vpt2rTRyJEjFRcXp8aNG2vXrl2KjIxU7dq19fjjj2drH1WrVtWWLVv02GOPKTAw0Gq7n5+fVqxYoU6dOqlmzZp67rnn1KBBA3l5eSkmJkbffvutzp49q0aNGlnalC1bVkuWLNEjjzyiWrVqadCgQapdu7akm+uDffLJJzIMQ126dMkwLh8fH40ePVrPP/98tt+PYcOGaebMmVq7dm2226R6+umndenSJT300EOqVq2aHB0dtW/fPr3zzjtycHDQyJEjLXWrV6+uL7/8Uh988IHq1KkjBwcH1a1bV71799Y777yj3r17a+LEiapYsaK+/vprrVu37pbjAQAAAADkDMkK5FilNOtWHDh7hWQFAADZZDKZtGLFCo0bN07z5s3TxIkTVaxYMT3++ON64403sr0u1sMPP6xx48ZluLB2gwYNtHv3bs2YMUMrVqzQ22+/rRs3biggIEB16tTRnDlz1LNnT6s2HTt21F9//aW3335bs2fP1okTJ+Tg4KCyZcuqbdu2Gjx4cJZxDRgwQO+++26217/w8PDQuHHj9PTTT2erflqDBw/WZ599pjlz5ujUqVO6du2aAgIC1LBhQy1YsMBqOquhQ4fq77//1iuvvKLLly/LMAwZhiEPDw9t3rxZQ4cO1csvvyyTyaT7779fS5YssUrmAAAAAADyjsnIaKEG5Kq4uDj5+vrq8uXL8vHxybfjms1mnTt3ToGBgXJwyN1Zv74/eEGPzf1JktS/SVmN7Vg1V/eP7MnLPsadgT4u+ApjHyckJOjo0aMqW7as3Nzc7B1Onktds8LJyUkmkynX9lu3bl2ZTCb98ssvubZP5Mzt9HFW/x7sdR0J2+gPAAAA5ATXkVljZAVyrFKwl+V3FtkGACB/xMXFaffu3Vq9erV+++23LNeQAAAAAADgbkCyAjkW4OWqIh7Oung9SfvPkqwAACA//P7772revLn8/f0VGRmpzp072zukPGEYhlJSUjKt4+jomKsjVQAAAAAA9lM45ppAnjCZTAr7/3Urzl1J1MVrN+wcEQAABV9ERIQMw9CFCxc0btw4e4eTZ+bPny9nZ+dMf7Zt22bvMAEAAAAAuYSRFbgtYUHe2nEkVpK0P/qKGpTzt3NEAACgIOjUqVOWa3GEhYXlUzQAAAAAgLxGsgK3pdL/j6yQbq5bQbICAADkBn9/f/n7c10BAAAAAIUF00DhtoQF/ZusYN0KAAAAAAAAAEBO5DhZMXjwYO3fvz83Y8FdqGKQ9cgKAAAAAAAAAABuVY6TFQsWLFDVqlXVunVrrVy5UoZh5GZcuEv4ujurhK+bJGnf2St8DgAAAAAAAAAAtyzHyYrTp0/rvffe05kzZ9SlSxeFhoZq8uTJunDhQm7Gh7tA6roVVxKSdTYuwc7RAAAAAAAAAADuNjlOVnh6emrAgAHavXu3Nm7cqDp16mjs2LEKCQlRnz599Ouvv+ZmnLiDsW4FAAAAAAAAAOB25MoC2y1atNCXX36po0ePqlGjRvrf//6n+vXrq379+lq1alVuHAJ3sEqsWwEAAAAAAAAAuA25kqyIj4/Xxx9/rE6dOmnLli2qUqWKIiMjlZKSos6dO+u1117LjcPgDhUWnHZkxVU7RgIAwJ0hKipKJpPJ8uPs7KyyZcuqX79+OnXq1C3tKyIiQhERETmOJTo6Wq+88opq1aolHx8fubi4qFSpUuratau++uorpaSkpGtz5MgRDRo0SJUqVZK7u7s8PDwUHh6uMWPGWMXfp08fmUwmhYeH29yPyWTSoEGDLK+PHTtmeU+WLFmSrv64ceNkMplyNK3orcac+uPq6qqwsDBFRkYqIYHpLAEAAADAXm4rWXH48GENHz5cJUuW1LPPPqtSpUpp/fr12r17t1599VX9+uuvGjlypN57773cihd3oAqBXnIw3fx9f3ScfYMBAOAOMm/ePG3fvl3r169Xv379tHjxYjVt2lTXrl3Ll+Pv2LFD1atX15w5c/TAAw9oyZIl2rhxoyZPnixnZ2d17dpVUVFRVm1Wr16tGjVqaPXq1Xr66ae1evVqy++rVq1Sx44d0x1nz5496faTldGjRyspKek2zi7nMbu7u2v79u3avn27VqxYofr162vChAl64oknciUeAAAAAMCtc8ppw3bt2mnDhg3y9PRU3759NXjwYJUvXz5dvU6dOmny5Mm3FSTubG7Ojgr199SRC9d0MPqqUsyGHFOzFwAAFGLVqlVT3bp1ZRiGmjZtKsMw9Prrr2vFihV69NFH8/TYly5dUufOneXl5aUffvhBxYsXt9r+2GOPadeuXYqJibGUHT16VD169FClSpW0ZcsW+fr6Wra1aNFCQ4YM0fLly6324+npqXvuuUeRkZHq1auX3N3ds4ytXbt2Wrt2rWbPnq3Bgwff1nnmJGYHBwc1aNDAKp5jx47p888/17Rp01SyZMnbigkAAAAAcOtyPLLi8OHDeuedd3Tq1ClNnz7dZqJCunmTvmXLlhwHiLtD6roViclm/RN73c7RAABwZ0r9gvz48eNKSEjQqFGjVLZsWbm4uKhkyZIaOHCgLl26lGF7wzBUsWJFtWnTJt22q1evytfXVwMHDpQkzZkzR9HR0XrzzTfTJSpS1ahRQ82bN7e8njZtmq5du6ZZs2ZZfemfymQyqWvXrunKp0yZolOnTmnGjBmZnn+qFi1aqE2bNnrttdd05crtrXeV05j/K23fAAAAAADyX46TFQcOHNDgwYPl5eWVaT1vb281a9Ysp4fBXaKS1boVLLINAIAthw4dkiQFBASoc+fOmjp1qh5//HGtWbNGw4cP1/z589WiRQslJibabG8ymTR48GBt2LBBBw8etNq2YMECxcXFWZIVGzZskKOjo9q3b5/t+NavX6+goCCrUQfZ0bBhQ3Xp0kVTpkxRbGxsttpMmTJFFy5c0FtvvXVLx/qvnMb8X2n7BgCAW5G6VpWbm5vNpHdERISqVatmh8jy3549ezRu3DgdO3bM3qEAAO5COZ4GCkgrLOjfZMWB6CtqWy3YjtEAAO5WRx/qpuQcLK6cV5yKFVPZL5bluH1KSoqSk5MVHx+vzZs3a+LEifL29paPj4/WrVunN998Uy+99JIkqXXr1goJCdEjjzyiBQsW6KmnnrK5z759+2rMmDGaOXOmpk+fbimfOXOmmjdvrqpVq0qSTpw4oYCAAHl4eFi1N5vNMpvNltcODg5ycLj5/Mo///yjWrVq5ehcJ02apPDwcL3xxhuaOnVqlvVr1qypXr16adq0aRowYICCg3N27ZDTmJOTkyXdnC5r0aJFWrFiherVq6eKFSvmKA4AABITEzVmzBj973//s3codrNnzx6NHz9eERERCg0NtXc4AIC7TI6TFWXLlpXJZHtdAgcHB/n5+alevXoaMmSIqlSpkuMAcXcISzuyIpqRFQCAnEm+cEHJ0dH2DiPX/Pdp/+rVq+uDDz7QV199JUnq06eP1fbu3burX79+2rRpU4bJCm9vb/Xt21dRUVGaOHGiPD09tXnzZu3Zs0evvfZaljENHz7carqmDh06aPXq1bd4ZumFhYWpf//+ev/99zVkyBCVLl06yzavv/66li5dqvHjx+uDDz647Riy69q1a3J2dra8NplMateunT766KN8iwEAUPC0bdtWixYt0osvvqiaNWvaOxzkwPXr19M96AEAyD85ngaqWbNmMgxDp06dUmhoqOrXr68yZcro1KlTSklJUUhIiL788kvVrVtXv/76a27GjDtQqL+HXBxvfpyYBgoAkFNOxYrJKSjozvkpVuy2zmfBggX65Zdf9Pvvv+v48eP6888/1bhxY8XExMjJySndlEMmk0nBwcFWi17bMnjwYF25ckWffvqpJOn9999XqVKl9OCDD1rqlC5dWufPn9f169ZrSb3wwgv65Zdf9Msvv6Rby6J06dI6evRojs933LhxcnR01NixY7NVPzQ0VAMGDNDHH3+cblqr7MpJzO7u7pb3YNeuXbp06ZLWrFnDwtoAgNsyYsQI+fv7a+TIkVnWzcnaVWn9+uuveuCBB1S0aFG5ubmpdu3a+vzzzy3bL1y4oJCQEDVq1EhJSUmW8j179sjT01OPP/64pWzDhg168MEHVapUKbm5ualChQp65plndMHGaNd9+/apZ8+eCgoKkqurq0qXLq3evXsrMTFRUVFR6t69uySpefPmMplMMplMioqKyvRcDh48qF69eikwMFCurq6qUqWKZs6caVVn69atMplMWrx4sUaPHq0SJUrIx8dHrVq10v79+9Ptc+PGjWrZsqV8fHzk4eGhxo0ba9OmTVZ1xo0bJ5PJpN9//13dunVTkSJFLOuxJiYm6oUXXlBwcLA8PDx033336bffflNoaKjlYZNjx47JyclJkyZNSnf8b7/9ViaTSUuXLs303AEA1nI8sqJNmzbasWOHDh06pJCQEEv5P//8o/vvv1+dO3dWVFSUIiIiFBkZqTVr1uRKwLgzOTk6qHygl/aeidPRC9eUmJwiVydHe4cFALjL3M6US3eiKlWqqG7dujIMwzLtkCT5+/srOTlZ58+ft0pYGIahs2fPql69epnut0KFCmrXrp1mzpypdu3a6auvvtL48ePl6Pjv/3tbt26t9evX6+uvv1a3bt0s5SEhIZZrNxcXF6v9tmnTRu+995527NiRozUgihcvrmHDhmny5Ml64YUXstVmzJgx+uSTT/TKK68oPDz8lo+Zk5gdHBxUt27dWz4WAACZ8fb21pgxYzR06FBt3rxZLVq0sFnPMAx17txZmzZt0qhRo9S0aVPt2rVLkZGR2r59u7Zv3y5XV9cMj7Nlyxa1bdtW9evX1+zZs+Xr66slS5bokUce0fXr19WnTx8VK1ZMS5YsUUREhEaOHKlp06bp+vXr6t69u0qXLq3Zs2db9nf48GE1bNhQTz75pHx9fXXs2DFNmzZNTZo00V9//WUZjfjnn3+qSZMmKlasmCZMmKCKFSvqzJkz+uqrr3Tjxg116NBBb7zxhl555RXNnDlT99xzjyRZEgC27NmzR40aNVLp0qX19ttvKzg4WOvWrdOQIUN04cIFRUZGWtV/5ZVX1LhxY3388ceKi4vTyJEj1alTJ+3du9dyHbRw4UL17t1bDz74oObPny9nZ2d9+OGHatOmjdatW6eWLVta7bNr167q0aOHnn32WV27dk3SzWk3P/vsM40YMUItWrTQnj171KVLF8XFxVnahYaG6oEHHtDs2bM1YsQIq+uw999/XyVKlFCXLl0yPHcAgA1GDoWHhxuLFy+2ue3TTz81KleubBiGYcyfP98oUqRITg9TYFy+fNmQZFy+fDlfj5uSkmKcOXPGSElJyfNjDV38u1Fm5GqjzMjVxp7T+XuehVl+9jHsgz4u+ApjH8fHxxt79uwx4uPj7R1Knpg3b54hyfjll18MwzAMs9ls3LhxwzCbzYZhGMa6desMSca0adOs2i1dutSQZMyZM8dS1qxZM6NZs2bpjrF+/XpDktG8eXPDxcXFiI6Ottp+8eJFIygoyChbtqxx+vRpm3GWKVPG6NChg+X1kSNHDE9PT6N27drGpUuX0tU3m83Gl19+aXn9xBNPGJ6enlZ1Ll++bBQrVsxo166dIckYOHCgZdvRo0cNScZbb71l1WbixImGJEub8+fP24zXltyIOTf8t49vRVb/Hux1HQnb6A8A/5X2//uJiYlGuXLljLp161r+n9CsWTMjPDzcUv+bb74xJBlvvvmm1X4+++wzQ5Lx0UcfZXq8ypUrG7Vr1zaSkpKsyjt27GgUL17c6ppyypQphiRj+fLlxhNPPGG4u7sbu3btynDfZrPZSEpKMo4fP25IMlauXGnZ1qJFC8PPz884d+5chu1Tr2W2bNmS6TmkatOmjVGqVKl0f1MHDRpkuLm5GbGxsYZhGMaWLVsMSUb79u2t6n3++eeGJGP79u2GYRjGtWvXjKJFixqdOnWyqpeSkmLUrFnTuPfeey1lkZGRhiTj1Vdftar7999/G5KMkSNHWpUvXrzYkGQ88cQTlrLUuJYvX24pO3XqlOHk5GSMHz8+W+8BgMKD68is5XgaqEOHDsnX19fmtiJFiujYsWOSbmaa/zv9AAqmSsHWi2wDAADbWrdurTZt2mjkyJEaP368Nm7cqGnTpqlv376qXbu21dQMme2jatWq2rJlix5++GEFBgZabffz89OKFSt05coV1axZU5GRkVq7dq2+++47rVixQsOHD9fZs2fl4+NjaVO2bFktWbJE+/fvV61atfT2229r8+bN2rx5s95//33VqVNHEyZMyDQuHx8fjR49WmvXrs32+zFs2DCVKFHiltrkZswAAOQWFxcXvf766/r111+tpmVKa/PmzZJsr13l6emZbrqitA4dOqR9+/bp0UcflSQlJydbftq3b68zZ85YTYv00ksvqUOHDurZs6fmz5+v9957T9WrV7fa57lz5/Tss88qJCRETk5OcnZ2VpkyZSRJe/fulXRzLYdt27bp4YcfTjeNZU4lJCRo06ZN6tKlizw8PNKdS0JCgnbs2GHV5oEHHrB6XaNGDUnS8ePHJUk//vijYmNj9cQTT1jtz2w2q23btvrll18soydSPfTQQ1avt23bJkl6+OGHrcq7desmJyfrCUoiIiJUs2ZNq2mrZs+eLZPJpKeffvpW3xIAKPRynKwoU6ZMhvMOfvLJJ5ZFFWNiYlS0aNGcHgZ3kcppF9lm3QoAADJkMpksCYN58+apffv2mjp1qh5//HFt3rw506kf0kq9iR40aJDN7Q0aNNDu3bv15JNPasWKFerevbtatmypgQMH6tChQ5ozZ44WLFhg1aZjx47666+/1L59e82ePVvt27dXx44d9cEHH6h58+Zavnx5lnENGDBAZcuWzdY5SJKHh4fGjRuX7fr/lRsxAwCQW3r06KF77rlHo0ePtlovItXtrF0VHR0tSXrxxRfl7Oxs9TNgwABJslprwmQyqU+fPkpISFBwcHC6ByLMZrPuv/9+ffnllxoxYoQ2bdqkn3/+2ZIkiI+PlyRdvHhRKSkpKlWqVA7eEdtiYmKUnJys9957L925tG/fPt25SDen0kwr9ZopNc7U96dbt27p9jllyhQZhqHY2Firffx3Da/U9z8oKMiq3MnJKd3xJWnIkCHatGmT9u/fr6SkJM2ZM0fdunVTcHDwLb0fAIDbWLPixRdf1DPPPKOTJ0+qe/fuCgoKUnR0tD7//HP99NNP+uijjyTdnEuROYELh0pBJCsAAJBuPin536cl/8vNzU2TJ0/W5MmTM623devWDLetWrVKdevWVf369TOsExQUpDfeeENvvPFGpsdJq1y5cukWtrQlKirK5sMrLi4uOnLkSLry0NBQGYZhc19PPfWUnnrqqWzH+F+3GzMAALnFZDJpypQpat26teW7kbRuZ+2qYsWKSZJGjRqlrl272qwTFhZm+f3MmTMaOHCgatWqpb///lsvvvii3n33Xcv23bt3688//1RUVJSeeOIJS/mhQ4es9lm0aFE5Ojrq5MmTWZx99hUpUkSOjo56/PHHNXDgQJt1buXhB+nf9+e9997LcC2r/yYhTCaT1evUhER0dLRKlixpKU9OTraZSOrVq5dGjhypmTNnqkGDBjp79myG5wMAyFyOkxVPPfWUDMPQuHHjNHz4cEt5cHCwZs+erf79+0uSRo8ene2nA3F3K+nnLk8XR127kaL9TAMFAECeiIuL0+7du7V69Wr99ttvjBoAAOAO1KpVK7Vu3VoTJkxQSEiI1baWLVvqzTff1MKFC/X8889byr/44gtdu3Yt3QLQaYWFhalixYr6888/s3wQISUlRT179pTJZNLatWv16aef6sUXX1RERIQl0ZH6Rf1/v7f58MMPrV67u7urWbNmWrp0qSZOnGhJCvzXf0c6ZMbDw0PNmzfXzp07VaNGDbm4uGTZJiuNGzeWn5+f9uzZk+HI06zcd999kqTPPvvMski4JC1btkzJycnp6ru5uenpp5/W+++/rx9//FG1atVS48aNc3YCAFDI5ShZkZKSosOHD+vhhx/WU089pf379ysmJkb+/v4KCwuzykr/N2ONgstkMqlSsLd2/nNJJy/G62pisrxcc5wPAwAANvz+++9q3ry5/P39FRkZqc6dO9s7pDxhGIZSUlIyrePo6JjuaUgAAO4UU6ZMUZ06dXTu3DmFh4dbytOuXRUXF6fGjRtr165dioyMzNbaVR9++KHatWunNm3aqE+fPipZsqRiY2O1d+9e/f7771q6dKkkKTIyUt99953Wr1+v4OBgvfDCC9q2bZv69++v2rVrq2zZsqpcubLKly+vl19+WYZhqGjRolq1apU2bNiQ7rjTpk1TkyZNVL9+fb388suqUKGCoqOj9dVXX+nDDz+Ut7e3qlWrJkn66KOP5O3tLTc3N5UtW9bm9EmSNGPGDDVp0kRNmzbVc889p9DQUF25ckWHDh3SqlWrLOt7ZJeXl5fee+89PfHEE4qNjVW3bt0UGBio8+fP688//9T58+f1wQcfZLqP8PBw9ezZU2+//bYcHR3VokUL/f3333r77bfl6+srB4f0M6oPGDBAb775pn777Td9/PHHtxQzAOBfOVqzwjAMVa1aVdu3b5fJZFLlypXVuHFjVa5cmRvGQi4szVRQBxldAQBArouIiJBhGLpw4cJtrfNwp5s/f366uab/+5O6ACYAAHei2rVrq2fPnunKb3ftqubNm+vnn3+Wn5+fhg0bplatWum5557Txo0b1apVK0nShg0bNGnSJI0dO9ZqpEZUVJR8fHz0yCOP6MaNG3J2dtaqVatUqVIlPfPMM+rZs6fOnTunjRs3pjtuzZo19fPPP6tOnToaNWqU2rZtq5EjR8rV1dUyKqJs2bKaPn26/vzzT0VERKhevXpatWpVhudStWpV/f7776pWrZrGjBmj+++/X/3799eyZcsyHWGSmccee0xbtmzR1atX9cwzz6hVq1YaOnSofv/992zvc968eRo6dKjmzp2rTp06acmSJZYF0/38/NLVL1mypJo0aaKiRYuqV69eOYobACCZjIwmDs5CqVKl9OGHH6pDhw65HVOBFBcXJ19fX12+fFk+Pj75dlyz2axz584pMDDQZvY/t8374ajGr9ojSZryUHU9Uq90nh+zsMvvPkb+o48LvsLYxwkJCTp69KjKli0rNzc3e4eT5wzDUHJyspycnHiwI5tiYmJ09OjRTOuEhYXJ29s70zr55Xb6OKt/D/a6joRt9AcAFE4//vijGjdurE8//TRdQuLcuXMqU6aMBg8erDfffNNOEQK403EdmbUcz9HTo0cPLViwgGQFrKQdWbGPRbYBAEAO+fv7ZzhlBAAAQF7asGGDtm/frjp16sjd3V1//vmnJk+erIoVK1otbH7y5EkdOXJEb731lhwcHDR06FA7Rg0Ad78cJytq1aqlzz77TC1atFDXrl1VvHjxdE+Rpf0DjsKhUvC/yYoDTAMFAAAAAADuMj4+Plq/fr2mT5+uK1euqFixYmrXrp0mTZpkNQry448/1oQJExQaGqpPP/1UJUuWtGPUAHD3y3Gyonfv3pKkU6dOaevWrem2m0ymLBdFRMFTzMtV/p4uirl2Q/vPXrV3OAAAAAAAALekfv36+v7777OsN27cuAK9hhgA5LccJyu2bNmSm3GgAKkU5K3tR2J04WqiYq4myt8r88XBAAAAAAAAAACFW46TFc2aNcvNOFCAhAXfTFZI0oHoq2pIsgIAAAAAAAAAkAmH293B5cuXtW7dOn366ae6ePFibsSEu1xYmnUr9p+Ns2MkAAAAAAAgL5hMJqspkLZu3SqTyWRzqvCsREVFyWQy6ddff82ybp8+fRQaGnrLxwAA3PlyPLJCkl577TVNnjxZ8fHxMplM+uWXX1SkSBG1bNlSrVu31ssvv5xbceIuUikoTbIimnUrAAAAAAAoaLZv365SpUrZOwwAQAGS45EVs2bN0vjx49W/f3+tWbNGhmFYtnXs2FFr1qzJlQBx96kU5GX5/UD0FTtGAgAAAAAA8kKDBg1IVgAAclWOkxXvv/++hg8frnfffVf333+/1baKFSvq4MGDtx0c7k7ebs4q6ecuSTpw9opVIgsAgMIgdSqD1B9nZ2eVLVtW/fr106lTp25pXxEREYqIiMhxLNHR0XrllVdUq1Yt+fj4yMXFRaVKlVLXrl311VdfKSUlJV2bI0eOaNCgQapUqZLc3d3l4eGh8PBwjRkzxir+Pn36yGQyKTw83OZ+TCaTBg0aZHl97Ngxy3uyZMmSdPXHjRsnk8mkCxcuZPv8UqecSP1xcXFRQECAGjdurNGjR+v48ePZ3tftWLx4sVq0aKHg4GC5urqqRIkS6tSpk3788cd8OT4AoHA6dOiQ+vbtq4oVK8rDw0MlS5ZUp06d9Ndff1nqnD9/Xi4uLho7dmy69vv27ZPJZNK7775rqTtgwABVrVpVXl5eCgwMVIsWLfTdd9+la/vfaaBs+fXXX9WjRw+FhobK3d1doaGh6tmzZ4b/f7548aL69u2rokWLytPTU506ddKRI0eyfB8Mw9CsWbNUq1Ytubu7q0iRIurWrVu22gIA7hw5TlYcOXJEbdq0sbnN29tbly5dyumuUQCkrltxJTFZZy4n2DkaAADsY968edq+fbvWr1+vfv36afHixWratKmuXbuWL8ffsWOHqlevrjlz5uiBBx7QkiVLtHHjRk2ePFnOzs7q2rWroqKirNqsXr1aNWrU0OrVq/X0009r9erVlt9XrVqljh07pjvOnj170u0nK6NHj1ZSUtJtnJ21N954Q9u3b9eWLVs0d+5cRURE6JNPPlGVKlX06aef5tpxMhITE6OGDRtq5syZWr9+vaZNm6bo6Gjdd9992rZtW54fHwBQOJ0+fVr+/v6aPHmyvvnmG82cOVNOTk6qX7++9u/fL0kKCAhQx44dNX/+fJnNZqv28+bNk4uLix599FFJUmxsrCQpMjJSa9as0bx581SuXDlFRETkaC2KY8eOKSwsTNOnT9e6des0ZcoUnTlzRvXq1bP5cEL//v3l4OCgRYsWafr06fr5558VERGR5XdMzzzzjIYNG6ZWrVppxYoVmjVrlv7++281atRI0dHRtxw3AMA+crxmha+vb4Z/8I8dO6bAwMAcB5WVK1eu6LXXXtMff/yhnTt36sKFC4qMjEyX0U9JSdGMGTO0fv167d69W7GxsSpTpowefPBBvfzyy/Lz88vyWBERETZvMNu0aaNvvvkml86o4KkU5K3N+85JkvafvaIS/z/SAgCAwqRatWqqW7euDMNQ06ZNZRiGXn/9da1YscLypUBeuXTpkjp37iwvLy/98MMPKl68uNX2xx57TLt27VJMTIyl7OjRo+rRo4cqVaqkLVu2yNfX17KtRYsWGjJkiJYvX261H09PT91zzz2KjIxUr1695O6e9f/z27Vrp7Vr12r27NkaPHjwbZ7pTRUrVlSDBg0srx944AG98MILatWqlfr06aMaNWqoevXquXIsWwYNGqTk5GQ5OTnJZDJJunmeAQEBmjt3rpo1a5ZnxwYAFF733Xef7rvvPsvrlJQUdejQQeHh4frwww81bdo0SVLfvn21fPlybdq0Sa1bt7bUXbhwoTp16iR/f39JUlhYmGbNmmW1vzZt2ujYsWN69913b3m0Z7du3dStWzer/XXs2FFBQUFatGiRhgwZYlW/bt26mjt3ruV1eHi4GjdurJkzZ2r06NE2j7Fjxw7NmTNHb7/9toYPH24pb9q0qSpVqqRp06ZpypQptxQ3AMA+cjyyomXLlnrzzTetngw0mUxKTk7WBx98kOGoi9wQExOjjz76SImJiercuXOG9eLj4zVu3DiVKVNG06dP19dff62nnnpKH330kRo3bqz4+PhsHa9cuXLavn271c/06dNz52QKqLDgf9et2M+6FQAASJLly/Tjx48rISFBo0aNUtmyZeXi4qKSJUtq4MCBmT45aBiGKlasaPM66+rVq/L19dXAgQMlSXPmzFF0dLTefPPNdImKVDVq1FDz5s0tr6dNm6Zr165p1qxZVomKVCaTSV27dk1XPmXKFJ06dUozZszI9PxTtWjRQm3atNFrr72mK1fy7jqhaNGi+vDDD5WcnKx33nnHatvBgwfVq1cvBQYGytXVVVWqVNHMmTPT7ePvv//W/fffLw8PDwUEBGjgwIFas2aNTCZTlk+Yent7y83NTU5OOX4+CACATCUnJ+uNN95Q1apV5eLiIicnJ7m4uOjgwYPau3evpV67du0UHBysefPmWcrWrVun06dPq1+/flb7nD17tu655x7L/8OcnZ21adMmq/1l19WrVzVy5EhVqFBBTk5OcnJykpeXl65du2Zzf/99mKNRo0YqU6aMtmzZkuExVq9eLZPJpMcee0zJycmWn+DgYNWsWTNHI0IAAPaR4zunCRMmqF69eqpataq6dOkik8mk999/Xzt37tQ///yjzz//PDfjtFKmTBldvHjRMqfxxx9/bLOeu7u7jh49anlCQLo5UqJ06dLq3r27vvjiCz322GNZHs/d3d3qST1krVKQt+X3A2dJVgAAIN2cV1q6OR1D586dtWnTJo0aNUpNmzbVrl27FBkZaXkwwtXVNV17k8mkwYMHa9iwYTp48KAqVqxo2bZgwQLFxcVZkhUbNmyQo6Oj2rdvn+341q9fr6CgoFu+7mnYsKG6dOmiKVOm6Omnn1bRokWzbDNlyhTVrl1bb731liZMmHBLx7sV9erVU/HixfXtt99ayvbs2aNGjRqpdOnSevvttxUcHKx169ZpyJAhlhG7knTmzBk1a9ZMnp6e+uCDDxQYGKjFixdbrcPxXykpKTIMQ6dOndKkSZNkGIalTwAAyG3Dhw/XzJkzNXLkSDVr1kxFihSRg4ODnnzySasHNJ2cnPT444/rvffe06VLl+Tn56eoqCgVL17c6iGIadOm6YUXXtCzzz6r1157TcWKFZOjo6PGjh2bo2RFr169tGnTJo0dO1b16tWTj4+PTCaT2rdvb/MB0uDgYJtlaUeC/ld0dLQMw1BQUJDN7eXKlbvluAEA9pHjZEWFChX0ww8/aPjw4Zo1a5YMw9CCBQvUvHlzffrppypdunRuxmkldWh9VhwdHa0SFanuvfdeSdKJEydyNS78q3yAlxwdTEoxG4ysAABkW/R7O2W+csPeYVg4eLsoaHDtHLdPSUlRcnKy4uPjtXnzZk2cOFHe3t7y8fHRunXr9Oabb+qll16SJLVu3VohISF65JFHtGDBAj311FM299m3b1+NGTNGM2fOtBrpOXPmTDVv3lxVq1aVdPM6JyAgQB4eHlbtzWaz1XzVDg4OcnC4Odj2n3/+Ua1atXJ0rpMmTVJ4eLjeeOMNTZ06Ncv6NWvWVK9evTRt2jQNGDDA5pcTuaV06dLatWuX5fXw4cPl7e2t77//Xj4+PpJuvv+JiYmaPHmyhgwZoiJFiuidd95RbGysvv32W8v72q5dO7Vt21bHjh2zeaxq1apZ5ggvXry4vvnmG9WpUyfPzg0AULgtXLhQvXv31htvvGFVfuHChXRTX/ft21dvvfWWlixZokceeURfffWVhg0bJkdHR6v9RURE6IMPPrBqm5ORkJcvX9bq1asVGfl/7N13eFRV/sfxz52SSSOdBEILPRQRUaSpdEUEEVYsP1kpVtS1rasgKtVV17Kyru6iouBaVkGpAooU1wIogl16lRZIIL3N3Pv7Y5IhIQHSQ5L363nuk3vPPfecc3MuYWa+c86ZrAkTJvjSs7OzfWtjnOrw4cPFprVq1eq09URFRckwDH3xxRfFftmjuDQAwLmpzNNASVL79u21YsUKpaam6vfff1dKSoo+/fRTtWvXrqLaVylWr14tyTv3YUns3LlTERERcjgcatmypSZNmlTiKaTqKn+nXXGR3g9HtiekyWNa1dwiAEBNYKbmyJNy7mzlDZx0795dTqdTISEhuuaaa9SgQQMtX75cmzZtkiSNGTOmUP6RI0cqKChIq1atOm2Z9erV09ixYzVnzhzfdJyrV6/Wr7/+esZv/Od78MEH5XQ6fdvVV19d9hssoG3btrrlllv0z3/+U/v27SvRNTNmzFBubq6mTp1aIW04Hcs6+TokKytLq1at0vDhwxUYGFhouojBgwcrKytL69evlyR9/vnn6tixoy9Qke/GG288bV3z58/Xhg0bNG/ePLVv315XXnkl008AACqNYRhFPoz/+OOPdeDAgSJ527Vrp27duunNN9/Uu+++q+zsbI0dO/as5f34449at25dmdpmWVaR8l5//XV5PJ5ir3nnnXcKHX/99dfau3fvGdfKGDJkiG9U40UXXVRkq8w1qwAAFatCJtB1uVyKjY2tiKIq3YEDBzRhwgRddNFFGjJkyFnzX3LJJbr++usVHx+vzMxMLV++XH/729/05Zdfas2aNb5vIp4qOztb2dnZvuOUlBRJRb/NWNlM05RlWVVaZ742MfW082i6ctymdh9NVYv6wWe/CKVWnX2MqkEf1351sY/z7zl/y2er56dzKbxtq+dXqH0llX/N3Llz1a5dOzkcDkVERPhGnr7xxhtyOByKiooqUn7+VAenphc8vueee/TPf/5Tb7/9tm6//Xb985//VOPGjXX11Vf78jVt2lTbt29Xenp6odEVDz74oG8+6GHDhhUqu2nTptq9e3ep7zk//+TJk/X222/r8ccf15w5c3zn8s8X/GlZlpo1a6bx48fr5Zdf1gMPPFDkfGnqPtM1+/btU2xsrCzL0rFjx+R2u/XSSy/ppZdeKjb/0aNHZVmWEhMT1bx58yLlRkdHF1unZVm+L8N07dpVw4YNU5cuXXTffffp+++/P+M95P8NKO7vQF362wAAKJ0hQ4Zozpw5io+PV6dOnfTdd9/p2WefVePGjYvNP27cON1xxx06ePCgevbsqbZt2xYpb/r06Zo8ebJ69+6trVu3atq0aWrevLncbnep2hYSEqLLLrtMzz77rKKiohQXF6fPP/9cs2fPLjLqI9/GjRt16623auTIkdq/f78mTZqkRo0a6a677jptPb169dLtt9+usWPHauPGjbrssssUFBSkQ4cO6csvv9R5552n8ePHl6rtAIDqUa5gxZ49e/TBBx9o7969RUYaGIah2bNnn7WMtWvXFlrY8Uw2b95c5qkJJCkpKUmDBw+WZVl6//33TxtoKGjGjBmFjgcPHqy4uDg99NBDWrRokYYPH17sdU899VSx3xI8evSosrKyynYDZWCappKTk2VZVonutyI1Cj45Xdc32w4o2Aqv0vrriursY1QN+rj2q4t9nJubK9M0fd9ozxdxZ8dqbFXxSvvGXDr54XKbNm3UuXNnWZYlj8ej3NxcGYah8PBwud1uHTp0SPXr1/ddZ1mWDh8+rAsvvNBXb/6H4QXbERcXp0GDBunll1/WwIEDtXjxYj3xxBOyLMuXr1+/fvr000+1ZMkS/eEPf/Bd27BhQ9+C235+foWuGThwoF5++WV99dVX6tatW4nvM//6+vXr609/+pOeffZZ3Xvvvb7255/P/5nf95I0YcIEvfnmm5o4caJvBMOpz8WZ5H8zM3/KrVN9++23Onz4sMaOHSu326169erJbrfrpptuOu0HF3FxcXK73YqIiNDhw4eLlHvw4MEideb3sVR4ytLOnTtr/vz5Z7wft9st0zSVmJgop9NZ5HxlLkIOAKjZZs6cKafTqaeeekppaWnq0qWLPvroIz322GPF5r/hhht0//336/fff/et0VTQpEmTlJGRodmzZ+tvf/ub2rdvr3//+99asGBBmUYKvvvuu7rvvvv08MMPy+12q1evXlq5cqWuuuqqYvPPnj1b//nPf3TDDTcoOztbffv21cyZM8+6HtasWbPUvXt3zZo1S6+88opM01RsbKx69erlmwocAHDuK3Ow4uOPP9aIESPk8XgUHR1dZFhfSdeVaNu2rV577bUS5S3POhjHjx/XwIEDdeDAAa1evbpcCyyNGjVKDz30kNavX3/aYMXEiRP14IMP+o5TUlLUpEkT1a9f3zc3clUwTVOGYah+/fpV/gHYBS1Maf0hSdKRLJvvW4ioWNXZx6ga9HHtVxf7OCsrS6mpqXI4HHI4KmSg5zklvx/tdnuh+8v/IHrgwIF6/vnn9d///lcPPPCA7/z8+fOVnp6uAQMG+K7Lf0116u/pvvvu0xVXXKFbb71Vdrtdd9xxR6E8t99+u/7+97/r0Ucf1WWXXeYLUJzKMAzfdQ8++KDmzJmje++9V6tXr1ZoaGihvJZlaeHChb7XP/n3WbDeiRMnavbs2Xr88ceLlJ//02az+fZjYmL08MMP67HHHvN9+aU0z0X+PNun/q4l7xdV7rnnHjmdTj344INyOBwKCQlR37599cMPP+iCCy6Qn5/facvu3bu3nn/+eW3btq3QVFDz5s07bZ0Fgw1ZWVn65ptv1KpVqzPej8PhkM1mU2RkpPz9/YucLy4NAABJCgsL0+uvv14k/XSBhZCQEGVkZJy2PD8/Pz377LN69tlnC6Xnj8Ys6NSRh3369CmS1qhRI82fP7/Itaeu/TRmzBjf9JgDBw48bfsk+UZvnmrs2LFFprUCANQsZf50YNKkSerVq5f++9//lutD6IYNG+rWW28t8/Ulcfz4cQ0YMEC7d+/WqlWr1KlTpwop90wfKLlcrmIXcSq4iGVVsdLSpeBg2erVq9J64xueDMpsT0irMx/AVQfDMKrl2ULVoY9rv7rWxzabTYZh+LbaJv+e8u/PsqxCaZdffrmuuOIKTZgwQampqerVq5d+/PFHTZ48WRdccIFuvvnmIr+XU48vv/xytW/fXmvWrNGoUaMUExNT6Hx4eLgWLlyooUOHqnPnzho/fry6d++u4OBgJSYm6n//+58OHz6snj17+spu0aKFb9HNCy64QPfcc48uuMC7wPivv/6qN954Q5ZlacSIEadtW2hoqCZNmuQLwhTs41N/L/keeOABvfLKK1q+fHmx50vyu96xY4c2bNjgG6GwYcMGzZ49WykpKXrrrbfUsePJUTszZ87UJZdcossuu0zjx49XXFycUlNTtWPHDi1ZssS3vtkDDzygN998U4MHD9a0adMUExOjd999V1u2bJHkDVbk19+zZ09dddVV6tChg8LCwrRnzx7961//0s6dO7VgwYIz3k/+/Z7ub0Bd+bsAAAAAoG4rc7Bi+/bt+uijj875b8vnByp27dqllStX+t5wl8fcuXMleRfNPJdlb9+uo/98WamrV8s14RFF5s1PXVXiIoPk57Apx21q62GmLwAAIJ9hGFq4cKGmTJmiN998U08++aSioqL0xz/+UX/961+L/cJDca677jpNmTLltAtrd+/eXT///LNmzpyphQsX6vnnn1dOTo7q16+vCy+8UK+99lqRxaKHDBmin376Sc8//7z+/e9/a//+/bLZbGrevLkGDRqkP/3pT2dt11133aV//OMf2r17d4nuIzAwUFOmTNHtt99eovzFefTRRyV5RymEhoaqTZs2GjdunG6//XY1a9asUN727dtr06ZNmj59uh577DElJCQoLCxMrVu31uDBg335YmNj9fnnn+v+++/XnXfeqcDAQA0fPlzTpk3T6NGjC8233aNHD9/0qOnp6YqKilKPHj3097//XT179izzfQEAAABAXWFYZVk1Ut43edOnTy80B3JVWr58udLT05Wamqpx48Zp5MiRuu666yR515UIDAxUZmamevfurY0bN+rFF18sMk9h/fr11bJlS9+xw+FQ7969tWrVKknSF198oSeffFLDhw9XixYtlJWVpeXLl+vVV19V7969tXLlyhJ/0y0lJUWhoaFKTk6usmmgsrZu0+68oZr+nTqp+QfvV0m9BV31jy/0y8EU2W2Gfpl6hfyd9ipvQ21nmqYSEhIUHR3NNy9rKfq49quLfZyVlaXdu3erefPmdWKKm/x1GxwOR4WOJLnoootkGIa+/fbbCisTZ3f77bfrvffeU2Jiom8aqfL08dn+PVTH60icHv0BAACAsuB15NmVeWTFo48+queee05XXnmlAgMDK7JNJTJ+/Hjt3bvXdzxv3jzf/MG7d+9WXFycjhw54nvzft999xUpY/To0YXmOvR4PL6FESXvFFV2u13Tp0/XsWPHZBiGWrdurWnTpunPf/7zOf+Bkn/bNnLFxyt7yxZl/fijsnftlqtF8yptQ9uYevrlYIo8pqWdR9PUITb07BcBAIDTSklJ0c8//6ylS5fqu+++04IFC6q7SbXatGnTFBsbqxYtWigtLU1Lly7V66+/rscee+yM610AAAAAAEqnzMGKb775RgkJCWrVqpX69u2ryMjIQucNw9DMmTPL3cDTOXUxpuLExcUVWdzpTE7N26pVK3388celbdo5JWTY1TqaN69y8uJFir7//iqtv02Dk+tkbDuSSrACAIBy2rRpk++11+TJk3XNNddUd5MqhWVZhb5EUpyCa0ZUFqfTqWeffVa///673G63WrdurRdeeKHYL8IAAAAAAMquzMGKf/7zn7799957r8j5yg5WoGRCBg/W0Wefk0xTyYsXq/6998qowhEhbWNOBiu2Hk6rsnoBAKit+vTpU6ovY9RUc+fO1dixY8+YZ82aNerTp0+ltmPixImaOHFipdYBAAAAAChHsMI0zYpsByqJo359OS7uKvf6DXIfPKSMbzcqqNvFZ7+wgrQ9ZWQFAABASQwdOvSsa3G0bdu2iloDAAAAAKhspQpW/O9//1OXLl0UHBx8xnyJiYlatGiRxo0bV67GoWK4Lr9c7vUbJEnJixZVabCiYai/6rkcSs12a+thghUAAKBkIiMji0wzCgAAAACovUo1H1Dfvn3166+/+o5N05Sfn582b95cKN+OHTt02223VUwLUW7OXr1kywswpX7yiczMzCqr2zAM37oVB05kKjUrt8rqBgAAAAAAAADUDKUKVpw6P7JlWXK73XVi3uSazHC5VO+KKyRJZnq6UletrtL628QUnAqKdSsAAAAAAAAAAIVV3UrLqFYhw6727ScvWlSldbeNOTltGOtWAAAAAAAAAABORbCijgjo0kXORo0kSelffaXchIQqq7ttgxDfPutWAAAAAAAAAABORbCijjBsNoXmj64wTaUs/bjK6m5TYGQFwQoAAAAAAAAAwKkcpb1g69atcji8l3k8HknSli1bCuU59RjnhtCrr9axV/4lyTsVVOS4sVVSb2SwS1HBLh1Ly2YaKAAAAAAAAABAEaUeWTFmzBh17dpVXbt2Vffu3SVJf/zjH31pXbt21dixVfMhOErHLy5OAZ07S5Kyt25VVhUGldo28I6uSEzP0bG07CqrFwCA6vTjjz/qlltuUatWrRQSEqLAwEC1bt1ad9xxhzZu3Fgk/xdffKHrrrtOjRo1kp+fn0JDQ9WzZ0/961//Unp6ui9fXFycDMPQnXfeWaSMtWvXyjAMzZ8/35c2Z84cGYYhf39/7d27t8g1ffr0UceOHUt1b1OmTJFhGL7NZrOpYcOGGjx4sL766qtSlVVZ4uLiNGbMGN/xnj17ZBiG5syZU21tAgAAAAAUr1QjK958883KageqSOg1w5T5/feSpOSFi+Q/Ib5K6m0TU09f7UiUJG07nKqoVq4qqRcAgOoya9Ys3XPPPWrbtq3uvfdexcfHy+FwaMuWLXrvvffUtWtX7dixQy1btpQkTZ48WdOmTVPPnj01ffp0tWzZUhkZGfr66681ZcoUbdu2TX//+98L1TF79mw98MADatu2bYnalJ2drccee0z/+c9/Kuw+V6xYodDQUJmmqX379ulvf/ub+vTpow0bNqhLly4VVk9FaNiwodatW+f7nQMAAAAAzh2lClaMHj26stqBKhIyaJCOPPlXWbm5Sl66VNEP/VmGo9SzgZVafIN6vv2tR1LVs1VUpdcJAEB1+eqrr3TXXXfpqquu0vz58+V0OuV2u+VwONS/f3/dfffdmjdvngICAiRJ8+bN07Rp03TLLbfotddek2EYvrKuvPJKPfzww1q3bl2hOnr06KFff/1Vjz76qD788MMStWvQoEF699139dBDD+n888+vkHu98MILFRXl/X+9Z8+euvjii9WyZUvNnz//nAtWuFwu38hgAAAAAMC5hQW26xh7WJiC+/aVJHmOHVP6119XSb1tYgoEK1hkGwBQy/31r3+V3W7XrFmz5OfnV2yekSNHKjY2VpI0bdo0hYeH6x//+EehQEW+evXq6fLLLy+UFhERoQkTJuijjz7S+vXrS9Suhx9+WJGRkXrkkUdKeUclFxoaKklyOp2+tKysLP35z39W586dFRoaqoiICPXo0UOLFi0qcv28efPUrVs3hYaGKjAwUC1atNC4ceMK5UlJSdFDDz2k5s2by8/PT40aNdL9999faKqs4hQ3DVT+dFa//PKLbrzxRoWGhiomJkbjxo1TcnJyoesty9Irr7yizp07KyAgQOHh4br22mu1a9eu0v6aAAAAAACnIFhRB4VeM8y3n7yw6IcElaF1TOGRFQAA1FYej0dr1qzRRRddpIYNG541/6FDh/Tzzz/r8ssvV2BgYKnquu+++9SoUSM9/PDDJcpfr149PfbYY/rkk0+0evXqUtV1Oh6PR263Wzk5OdqxY4fuvvtuuVwuXXvttb482dnZSkpK0kMPPaSFCxfqvffe0yWXXKIRI0borbfe8uVbt26drr/+erVo0UL//e9/9fHHH+uJJ56Q2+325cnIyFDv3r01d+5c3XvvvVq+fLkeeeQRzZkzR1dffbUsyyrTffzhD39QmzZt9OGHH2rChAl699139cADDxTKc8cdd+j+++/XgAEDtHDhQr3yyiv65Zdf1LNnTx05cqRM9QIAAAAAvCp//h+cc4IvuUT2sDB5TpxQ6qpV8qSlyR4cXLl1uhxqHB6g349natvhVFmWVew3RwEAddvlG7fqaI777BmrSH0/hz69qGTrQeQ7duyYMjMz1axZsyLnPB5PoWO73a59+/ZJkpo3b17q9gUEBGjKlCm67bbbtHTpUg0ZMuSs19x5552aOXOmHnnkEX3zzTfl/v+4QYMGhY5DQkL03nvv6bzzzvOlhYaGFlr7zOPxqH///jp+/LhefPFF3XzzzZKkr7/+WpZl6d///rdvhIakQotk/+Mf/9CPP/6oDRs26KKLLpIk9e/fX40aNdK1116rFStW6Morryz1fdxyyy36y1/+IkkaMGCAduzYoTfeeEOzZ8+WYRhav369XnvtNT3//PN68MEHfdddeumlatOmjV544QU9+eSTpa4XAAAAAODFyIpaLiEnV68dS9OOjCxfmuHnp5CrrpIkWdnZSv3kkyppS9u80RXpOR4dOJFZJXUCAGqWozluHcrOPWe2ig6cXHTRRXI6nb7t+eefL3eZY8eOVfv27TVhwgSZpnnW/H5+fpoxY4Y2btyoDz74oNz1f/bZZ/r222/1zTffaOnSpRowYIBuuOEGLViwoFC+efPmqVevXgoODpbD4ZDT6dTs2bP122+/+fJ07dpVknTdddfpgw8+0IEDB4rUt3TpUnXs2FGdO3eW2+32bVdccYUMw9DatWvLdB9XX311oeNOnTopKytLCQkJvnoNw9CoUaMK1dugQQOdf/75+vzzz8tULwAAAADAi2BFLbbyWLK6rt+il4+m6a2DSYXOVcdUUG0LLLK9jamgAADFqO/nUEOX85zZ6vuVfhBqVFSUAgICtHfv3iLn3nnnHX377bdavHixL61p06aSpN27d5fpd2a32/XXv/5Vv/zyi+bOnVuia2644QZ16dJFkyZNUm5ubpnqzXf++efroosuUteuXXXVVVdp3rx5atWqle6++25fno8++kjXXXedGjVqpLffflvr1q3Tt99+q3Hjxikr6+QXKi677DItXLhQbrdbN998sxo3bqyOHTvqvffe8+U5cuSIfvzxx0JBH6fTqXr16smyLB07dqxM9xEZGVno2OVySZIyMzN99VqWpZiYmCJ1r1+/vsz1AgAAAAC8mAaqFusaGiS7IeVa0geHkzSxZUMF2e2SJP+OHeXXooVydu1SxrffKuf3A/Jr3KhS21MwWLHlcKr6xcdUan0AgJqntFMunYvsdrv69eunTz/9VIcOHSq0bkX79u1lGIb27NnjS2vYsKHOO+88ffrpp8rIyCj1uhWSNGzYMPXq1UuTJ0/Wq6++etb8hmHomWee0cCBA0uUvzRsNps6dOigefPmKSEhQdHR0Xr77bfVvHlzvf/++4WmncrOzi72XoYNG6bs7GytX79eTz31lP7v//5PcXFx6tGjhy8Y9MYbbxRbf1RUVIXeT8FyDcPQF1984QtkFHS6hdQBAAAAACXDyIpaLMzp0LDoMElSisfUoiMnfOcMw1DosJOjK1KWLFZla1Ngke1thxlZAQCovSZOnCiPx6M777yzRCMXHn/8cR0/flz33ntvsQtEp6Wl6dNPPz1jGc8884z279+vf/zjHyVq44ABAzRw4EBNmzZNaWlpJbqmJDwej3766Se5XC6FhIRI8r7u8PPzKxSoOHz4sBYtOv3oTpfLpd69e+uZZ56RJG3evFmSNGTIEO3cuVORkZG66KKLimxxcXEVdi8FDRkyRJZl6cCBA8XWW3CNDgAAAABA6TGyopYbExup9w8flyTNOXBMNzaM8H1QEDp0iI6++KJkWUpeuEiRd95ZqYtet6gfJLvNkMe0tPVIxX0oAgDAuaZXr156+eWX9ac//UldunTRbbfdpvj4eDmdTh0+fFgffvihJPk+zB85cqQef/xxTZ8+XVu2bNEtt9yili1bKiMjQxs2bNCsWbN0/fXX6/LLLz9jncOGDTtjAOBUzzzzjC688EIlJCSoQ4cOZbrX7777zrcY9pEjR/TGG29oy5YteuCBB+Tv7y/J+0H/Rx99pLvuukvXXnut9u/fr+nTp6thw4bavn27r6wnnnhCv//+u/r376/GjRvrxIkTmjlzppxOp3r37i1Juv/++/Xhhx/qsssu0wMPPKBOnTrJNE3t27dPn376qf785z+rW7duZbqXM+nVq5duv/12jR07Vhs3btRll12moKAgHTp0SF9++aU6duyo2267rcLrBQAAAIC6gmBFLXd+vUC193fo1yy3fkzL1ObUDHUJCZIkOWNjFditmzLWr1fO3r3K+uEHBXTuXGltcTnsah4VpB0JadqZkCa3x5TDzuAeAEDtdOedd6pHjx6aOXOmXnzxRR08eFCGYahx48bq2bOnVq1apX79+vnyT5s2TQMGDNBLL72kSZMm6dixYwoICFCHDh304IMP6o477jhrnU899ZSWLl0qj8dTojZecMEFuvHGG/Xuu++W+T4HDRrk24+IiFDr1q31xhtvaPTo0b70sWPHKiEhQf/+97/1xhtvqEWLFpowYYJ+//13TZ061ZevW7du2rhxox555BEdPXpUYWFhuuiii7R69WpfMCUoKEhffPGFnn76ab366qvavXu3AgIC1LRpUw0YMKDSRlZI0qxZs9S9e3fNmjVLr7zyikzTVGxsrHr16qWLL7640uoFAAAAgLrAsIqbawAVLiUlRaGhoUpOTvZ9i7IqmKapV7ft0ZRDKZKk6xqE6x/tmvnOn1iwUIcmTpQkhd14gxpOnlyp7bn73U36+MdDkqTPHuytVtHBlVpfXWCapm9OcJuN4E9tRB/XfnWxj7OysrR79241b97c9+372syyLLndbjkcjkodxYjqU54+Ptu/h+p6HXkuSktL02OPPaYPPvhASUlJio+P14QJE3TDDTeUqpzHHntMTz75pDp06KCff/65VNfSHwAAACgLXkeeXd34RKSOuyIkQKEO78LaixNO6Hiu23eu3sCBMgICJEkpy5bLzMmp1La0LbBuxVbWrQAAAEApjBgxQnPnztXkyZO1fPlyde3atdSjg77//ns999xziomJqcSWAgAAACgtghV1QIDN0HUx4ZKkLNPS+4eSfOfswUGqN3CAJMlMTlba2rWV2paCi2xvPUKwAgCAc41pmnK73WfcgOqwbNkyrVy5Uq+88oruuOMO9e3bV6+99poGDhyov/zlLyWa/sztdmvs2LG64447FB8fXwWtBgAAAFBSBCvqiD/GRvj25x48JrPA7F+hw4b59pMXLa7UdrRtcDJYsY2RFQAAnHPGjRsnp9N5xg2oDgsWLFBwcLBGjhxZKH3s2LE6ePCgNmzYcNYynn76aSUlJenJJ5+srGYCAAAAKCMW2K4jWgX669LwYH1xPE27M3P0xfE09Y7wBg6CuneXIzpa7oQEpX3+udzHj8sRHl4p7WgaESiXw6Zst6ltjKwAAOCcM2XKFN1zzz3V3QygiJ9//lnt2rWTw1H4LUynTp1853v27Hna63/99VfNmDFDH330kYKDWTcNAAAAONcQrKhDRsdG6YvjaZKkOQeO+YIVht2ukKFDlDT7DcntVsqyZYq46aZKaYPdZqh1TLB+PpCiPYnpysr1yN9pr5S6AABA6cXFxSkuLq66mwEUkZiYqBYtWhRJj4iI8J0/HdM0NW7cOI0YMUKDBw8uVb3Z2dnKzs72HaekpJTqegAAAAAlwzRQdcgVUaFq4OeduuGTY8k6mHVyMe0qnQoqxrvavWlJOxLSKrUuAAAA1B6GYZTp3AsvvKDt27frxRdfLHWdTz31lEJDQ31bkyZNSl0GAAAAgLMjWFGHOG2Gbspbu8KU9Pahk98+82/TRq727SRJWT/+qOxduyqtHW0bnBx2v5V1KwAAAFACkZGRxY6eSEpKknRyhMWp9u3bpyeeeEKTJ0+Wn5+fTpw4oRMnTsjtdss0TZ04cUKZmZmnrXfixIlKTk72bfv376+YGwIAAABQCMGKOmZUbKTseV86e+dgonLNkwtth1XR6Io2MQUW2WbdCgCo0yzLOnsmoJbj30HJnHfeefrtt9/kdrsLpf/000+SpI4dOxZ73a5du5SZman77rtP4eHhvu2rr77Sb7/9pvDwcE2cOPG09bpcLoWEhBTaAAAAAFQ8ghV1TEOXnwZFhUqSjuS4teJYsu9cyFVXSXbv+hHJixfLMs1KaUPbBieDFVsJVgBAneR0eqclzMjIqOaWANUv/99B/r8LFG/48OFKS0vThx9+WCh97ty5io2NVbdu3Yq9rnPnzlqzZk2R7fzzz1dcXJzWrFnDovIAAADAOYAFtuug0bFR+vioN0gx58AxDY0OkyQ5oqIUfMklSvv8c7kPHVLGN98qqHvxb/rKo0GIv+r5O5Sa5dY2poECgDrJbrcrLCxMCQkJkqTAwMAzzjdf01mWJbfbLYfDUavvsy4rSx9blqWMjAwlJCQoLCxM9rwvjaB4V155pQYOHKjx48crJSVFrVq10nvvvacVK1bo7bff9v3+brnlFs2dO1c7d+5Us2bNFBYWpj59+hQpLywsTG63u9hzAAAAAKoewYo66JLwYLUMcGlnZra+OpGmbelZahPkL0kKvWaY0j7/XJKUvGhRpQQrDMNQfIN6+nbPcR1MzlJyZq5CA/gmIQDUNQ0aNJAkX8CiNrMsS6ZpymazEayopcrTx2FhYb5/Dzizjz76SJMmTdITTzyhpKQkxcfH67333tMNN9zgy+PxeOTxeJheCwAAAKhhDItX8VUiJSVFoaGhSk5OrtJ5bk3TVEJCgqKjo2WznZz1a9b+BE3ecVCSdGvjKM1o3dibPytL2y+9TGZqqmyBgWr95ReyBQZWeLsmLfhJ72zYJ0maf2cPXRRX/IKIOLvT9TFqD/q49qvrfezxeJSbm1vdzahUpmkqMTFRkZGRdbKP64Ky9rHT6TzriIrqeh2J4tEfAAAAKAteR54dIyvqqOsbROjpXYeUaVr64HCSJrZoqCC7XTZ/f4UMGqQT8+bJzMhQ6qpVCh06tMLrP3XdCoIVAFB32e32Wj/9jWmacjqd8vf3J1hRS9HHAAAAAFA+vJOqo8KcDg2LDpckpbhNLTpywncu9Jphvv3khYsqpf42MSeDFaxbAQAAAAAAAAB1G8GKOmxMoyjf/pwDx3zz+gZ06SJnY++0UOnr1in3SMXPJd42pvDICgAAAAAAAABA3UWwog7rHBKo8+sFSJJ+TMvU5tQMSd4FsEOvvtqbyTSVsnRphdcdHuSn6HouSdLWw6ksgAgAAAAAAAAAdRjBijpu9CmjK/KFDrvat5+8cGGlBBPy1604npGro2nZFV4+AAAAAAAAAKBmIFhRx10THa5Qh3dR08UJJ3Q81y1J8mvWTAEXXCBJyt6+XdlbtlR43YXXrUir8PIBAAAAAAAAADUDwYo6LtBu0/UNIiRJWaal9w8l+c6FDqvchbZZtwIAAAAAAAAAIBGsgKSbG0X69ucePCYzb8qnkCsHyXA6JUnJS5fKcrsrtN42DQqOrCBYAQAAAAAAAAB1FcEKqFWgvy4ND5Yk7c7M0RfHvVMy2UNDFdyvnyTJk5io9K++qtB628QE+/a3MLICAAAAAAAAAOqsGhmsSE1N1cMPP6zLL79c9evXl2EYmjJlSrF5x4wZI8Mwimzx8fElru+zzz5Tjx49FBgYqKioKI0ZM0YJCQkVdDfnhtGxp1tou8BUUIsqdiqoQD+HmkYESpK2H0mVaVb8It4AAAAAAAAAgHNfjQxWJCYm6tVXX1V2drauueaas+YPCAjQunXrCm3vv/9+ier6/PPPdeWVVyomJkaLFi3SzJkz9dlnn6l///7Kzs4u552cO66IClUDP++UT58cS9bBrBxJUvCll8geHi5JSv1slTwpKRVab/4i2xk5Hh04kVmhZQMAAAAAAAAAagZHdTegLJo1a6bjx4/LMAwdO3ZMr7/++hnz22w2de/evUx1/eUvf1GbNm00f/58ORzeX1fz5s3Vq1cvvfHGGxo/fnyZyj3XOG2GboqN0PN7jsiU9J+DiXqkRUMZTqdChgzR8f/8R1ZOjlI++UThI0dWWL1tGwTrs9+OSJK2Hk5Vk7yRFgAAAAAAAACAuqNGjqzIn8qpsh04cEDffvut/vjHP/oCFZLUs2dPtWnTRgsWLKj0NlSlUbGRsuf9Wt89lKjcvGmZKnMqqPyRFZK0lXUrAAAAAAAAAKBOqpHBitLKzMxUgwYNZLfb1bhxY91zzz1KSko663U///yzJKlTp05FznXq1Ml3vrZo6PLToKhQSdKRHLdWHEuWJPl3aC+/li0lSZkbv1PO/v0VVmd8gxDf/tbDBCsAAAAAAAAAoC6qkdNAlcb555+v888/Xx07dpTkXYPi73//u1atWqVvv/1WwcHBp702MTFRkhQREVHkXEREhO98cbKzswutaZGSt9aDaZoyTbNM91IWpmnKsqwS1/nHhhH6+Kg3SDHnwFFdFeUNJoRcPVTH/v6iJOnEokWKuuuuCmlfs4gAOWyG3KalrUdSq/R3U1uUto9R89DHtR99XPvRx7VfZfYxzw0AAACAuqDagxVr165V3759S5R38+bN6ty5c6nKf+CBBwodDxw4UBdccIGuvfZavfbaa0XOF+d0U06daSqqp556SlOnTi2SfvToUWVlZZ21zopimqaSk5NlWZZstrMPpGljWWrmZ9feHI++OpGu9fsPqoXLIbNHD+nFmZJl6fiChfL84Q8VNhVX03CXdiVmaWdCmg4eOiKHvfKn+KpNStvHqHno49qPPq796OParzL7ODWV0acAAAAAar9qD1a0bdtWr732WonyNm3atELqHD58uIKCgrR+/foz5ouMjJSkYkdQJCUlFTviIt/EiRP14IMP+o5TUlLUpEkT1a9fXyEhIae9rqKZpinDMFS/fv0Sv3Eem2vTlJ2HJEkfZ1ua3iRaio5Wbrduyli/XuaBAwo5dEgBpQwcnU772IPalXhIbtNSui1QraPrnf0i+JSlj1Gz0Me1H31c+9HHtV9l9rG/v3+FlgcAAAAA56JqD1Y0bNhQt956a5XXW5JvveVPHfXTTz9p8ODBhc799NNPvvPFcblccrlcRdJtNluVf0hhGEap6r2hYaSe2X1YmaaleUeO69GWsQqy2xV6zTBl5AV4UhYvVlCXLhXSvrYN6mnpT97gyPaj6WrbMLRCyq1LStvHqHno49qPPq796OPar7L6mGcGAAAAQF1QJ9/5zJ8/XxkZGerevfsZ8zVq1EgXX3yx3n77bXk8Hl/6+vXrtXXrVo0YMaKym1otwpwODYsOlySluE0tOnJCkhQycKCMgABv+rLlMnNyKqS+tg1OjqRgkW0AAAAAAAAAqHtqbLBi+fLlmj9/vpYsWSJJ+vXXXzV//nxfIEKS9u7dq169eumll17S8uXLtWLFCk2cOFGjR49Whw4diozocDgc6t+/f6G0Z555Rlu2bNHIkSP12Wef6d1339V1112njh07auzYsVVzs9VgTKMo3/6cA8e8I1GCghRy+UBJkpmSorQ1ayukLoIVAAAAAAAAAFC3Vfs0UGU1fvx47d2713c8b948zZs3T5K0e/duxcXFKSQkRDExMXrhhRd05MgReTweNWvWTPfee68effRRBQUFFSrT4/EUGkEhSX369NGyZcv0xBNPaOjQoQoMDNSQIUP07LPPFjvNU23ROSRQ59cL0A+pmfoxLVObUzPUJSRIocOGKXnRYklS8qJFCrni8nLX1SQ8UP5Om7JyTW07QrACAAAAAAAAAOqaGhus2LNnz1nzhIeH66OPPipxmZZlFZs+cOBADRw4sMTl1BajG0XpwS37JXlHV3QJCVJgt25yxMTIfeSI0v73P7mTkuQ4w0LjJWGzGWoTU08//p6svUkZyszxKMDPXhG3AAAAAAAAAACoAWrsNFCofNdEhyvU4Q0aLE44oaRctwy7XaFXD/VmcLuV8vGyCqmrTYx3KijLknYkpFVImQAAAAAAAACAmoFgBU4r0G7T9Q28oyayTEvvH0qSJIVefbUvT/KiRRVSV3zBdSuYCgoAAAAAAAAA6hSCFTijmxtF+vbfOnhMpmXJ1bq1/Nu3lyRl/fyzsnfsKHc9+SMrJGnr4ZRylwcAAAAAAAAAqDkIVuCMWgX669LwYEnS7swcfXHcO0VT6DXDfHnyF9wuj7aFRlYwDRQAAAAAAAAA1CUEK3BWo2OjfPtzDhyTJIVcdZVk965nkbxkiSzTLFcd0fVcCg1wSpK2HWYaKAAAAAAAAACoSwhW4KyuiApVAz9vIOGTY8k6mJUjR2Skgi+9VJLkPnxYGd98U646DMNQ27ypoA6nZCk5I7d8jQYAAAAAAAAA1BgEK3BWTpuhm2K9C22bkv5zMFHSKVNBLSz/QtsFp4LalsDoCgAAAAAAAACoKwhWoERGxUbKbnj33z2UqFzTUnDfvrLV8wYYUj79VGZGRrnqaFMgWLGFqaAAAAAAAAAAoM4gWIESaejy06CoUEnSkRy3VhxLls3lUsiVV0qSrIwMpX72WbnqyJ8GSmLdCgAAAAAAAACoSwhWoMSKW2i7IqeCahMT7NvfeoRgBQAAAAAAAADUFQQrUGKXhAerZYBLkvTViTRtS89SwAUXyNmkiSQpfd065R45UubywwL9FBPiLX/bkVRZllX+RgMAAAAAAAAAznkEK1BiNsPQzY0ifcdvHTwmwzAUOixvdIVlKWXJknLV0bZBiCTpREaujqZml6ssAAAAAAAAAEDNQLACpXJ9gwgF2LwrbX9wOEnpHo9Ch13tO39i4cJyjYhoW2AqKBbZBgAAAAAAAIC6gWAFSiXM6dCw6HBJUorb1MIjJ+TXpIkCunSRJOXs2KmsX38tc/ltCi6yzboVAAAAAAAAAFAnEKxAqY1pdHKh7bkHjsmyrJNTQUlKXlT2hbbbNjgZrNjKyAoAAAAAAAAAqBMIVqDUOocE6vx6AZKkH9MytTk1QyGDrpDh5ydJSvl4mazc3DKV3Tq6ngzvLFOMrAAAAAAAAACAOoJgBcpkdIHRFXMOHJM9NFTB/fpJkjyJiUr76qsylRvgZ1eziEBJ0rYjaTLNsq9/AQAAAAAAAACoGQhWoEyuiQ5XqMMuSVqccEJJue5CC22XZyqo/HUrMnM92n88o3wNBQAAAAAAAACc8whWoEwC7TZd3yBCkpRlWnr/UJKCL7lE9ghvWtqq1fKkpJSpbNatAAAAAAAAAIC6hWAFyuzmRpG+/bcOHpPlcChkyFWSJCsnRykrVpSp3PyRFRLrVgAAAAAAAABAXUCwAmXWKtBfl4YHS5J2Z+boi+NpCh02zHc+edHiMpVbaGTFkbTyNRIAAAAAAAAAcM4jWIFyGR1beKFt//bt5WrdSpKU+d13ytm3r9RlNo8KktNuSJK2MQ0UAAAAAAAAANR6BCtQLldEhaqBn1OS9MmxZB3Kzi08umLxklKX6bTb1LK+d8TGzqNpynGbFdNYAAAAAAAAAMA5iWAFysVpM3RTrHdRbVPSfw4mKmToUMnwjoxIXrRIlmWVutz8dSvcpqXdx9IrrL0AAAAAAAAAgHMPwQqU26jYSOXN2qR3DiVK9aMV1KO7JCl3/35lbt5c6jILr1vBVFAAAAAAAAAAUJsRrEC5NXT5aVBUqCQpIcet5ceSC08FtXBRqcvMH1khsW4FAAAAAAAAANR2BCtQIQoutD33wDHVGzBARmCgJCll+XKZ2dmlKi+ekRUAAAAAAAAAUGcQrECFuCQ8WC0DXJKkr06kaYfsChk4UJJkpqYqbc3aUpXXKCxAgX52SdJWRlYAAAAAAAAAQK1GsAIVwmYYurlRpO/4rYPHFHpNgamgFpVuKiibzVDrvKmg9iVlKCPHXTENBQAAAAAAAACccwhWoMJc3yBCATbvStsfHE6SLrxIjgYNJElpX3whd2JiqcprGxPs299+JK3iGgoAAAAAAAAAOKcQrECFCXM6NCw6XJKU4ja18FiKQocO9Z50u5Xy8bJSlVdwkW3WrQAAAAAAAACA2otgBSrUmEaFF9oOuXqo77i0U0HFNwjx7W9j3QoAAAAAAAAAqLUIVqBCdQ4J1Pn1AiRJP6Zl6teYWPl37ChJyvrlF2Vv317isto0ODkNFCMrAAAAAAAAAKD2IliBCje6wOiKOQeOKXRYgYW2Fy8ucTn1g10KD3RKkrYysgIAAAAAAAAAai2CFahw10SHK9RhlyQtTjghzxVXSA6HJCl58RJZHk+JyjEMw7duRUJqto6n51ROgwEAAAAAAAAA1YpgBSpcoN2m6xtESJKyTEvzsywFX3qpJMl95IgyNmwocVltG5xcZHsbU0EBAAAAAAAAQK1EsAKV4uZGkb79tw4eU72CU0GVYqFtghUAAAAAAAAAUPsRrEClaBXor0vDvQtk787M0aYLLpItJESSlPLpSpnp6SUqp23MyWDFFtatAAAAAAAAAIBaiWAFKs3o2JMLbf8nIUUhV14pSbIyM5X62WclKqN1DCMrAAAAAAAAAKC2I1iBSnNFVKga+DklSZ8cS1bG0NJPBRUa4FTDUH9J0tbDqbIsq+IbCgAAAAAAAACoVgQrUGmcNkM3xXoX2jYlzY9oIGfTppKk9HXrlXv4cInKaZM3uiIly60jKdmV0lYAAAAAAAAAQPUhWIFKNSo2UnbDu//OoUQF5i+0bVlKXrKkRGXEF1hkeytTQQEAAAAAAABArVMjgxWpqal6+OGHdfnll6t+/foyDENTpkwpNq9hGKfd4uPjz1pXnz59ir120KBBFXxXtVNDl58GRYVKkhJy3Frf73LfueRFi0o0rVObAutWbD2cUvGNBAAAAAAAAABUK0d1N6AsEhMT9eqrr+r888/XNddco9dff/20edetW1ckbcOGDbr//vs1fPjwEtXXokULvfPOO4XSwsLCStXmumx0bJQ+PposSXonS3r+oguVufE75ezYqaxfflVAxw5nvL5twZEVh9Mqta0AAAAAAAAAgKpXI4MVzZo10/Hjx2UYho4dO3bGYEX37t2LpM2aNUuGYeiWW24pUX0BAQHFloOSuSQ8WC0DXNqZma2vTqTp2IhrFbTxO0ne0RVnC1a0ig6WYUiWJW1jGigAAAAAAAAAqHVq5DRQ+VMxlUVqaqrmzZun3r17q1WrVhXcMhTHZhi6uVGk73hB2/Nl+PlJklKWLpWVm3vG6/2ddsVFBkmStiekymOefeooAAAAAAAAAEDNUSODFeXx3//+V+np6br11ltLfM3OnTsVEREhh8Ohli1batKkScrMzKzEVtY+1zeIUIDNG2Cal5Qq++XetSs8x48r7Ysvz3p927x1K7JyTe1Pyqi8hgIAAAAAAAAAqlyNnAaqPGbPnq2wsDD94Q9/KFH+Sy65RNdff73i4+OVmZmp5cuX629/+5u+/PJLrVmzRjZb8fGe7OxsZWdn+45TUrwLQ5umKdM0y38jJWSapizLqtI6ixNit+nq6DC9f/i4UtymPr/qGvVaulSSdGLhQgX16X3G61vHBGvFL9793w4lq2lEQGU3ucY4V/oYlYc+rv3o49qPPq79KrOPeW4AAAAA1AXVHqxYu3at+vbtW6K8mzdvVufOnctc1y+//KINGzbo7rvvlr+/f4mumTFjRqHjwYMHKy4uTg899JAWLVp02kW6n3rqKU2dOrVI+tGjR5WVlVX6xpeRaZpKTk6WZVmnDaxUlav9bXo/b/9tV4h6hYdLx48rbc0aHd65U7Z69U57bQP/k2/SN+86ogvq17lBQad1LvUxKgd9XPvRx7UffVz7VWYfp6ayZhcAAACA2q/agxVt27bVa6+9VqK8TZs2LVdds2fPlqRSTQFVnFGjRumhhx7S+vXrTxusmDhxoh588EHfcUpKipo0aaL69esrJCSkXPWXhmmaMgxD9evXr/YPR6IlnZ+YoR9SM7Ul26Pfb7xJjV/5p5SbK/+N3yns+utOe21XBUrLdkmSDqSZio6OrqJWn/vOpT5G5aCPaz/6uPajj2u/yuzjkn7JBgAAAABqsmoPVjRs2LDcwYOSyMnJ0X/+8x9deOGF5RqdUdCZ3oi6XC65XK5ir6nqDykMw6iWeoszulGUHtyyX5K0sOulukf/lCSlLFmiiBtvOO11zesHy89uU47H1LYjaefEvZxLzqU+RuWgj2s/+rj2o49rv8rqY54ZAAAAAHVBnXnns3jxYh07dky33HJLucuaO3euJKl79+7lLquuuSY6XKEOuyTp4xxLWed1kiRlbtqknH37Tnud025Ty+hgSdLuY+nKdnsqv7EAAAAAAAAAgCpRY4MVy5cv1/z587VkyRJJ0q+//qr58+dr/vz5ysjIKJJ/9uzZCggI0P/93/+dtkyHw6H+/fv7jr/44gsNGjRIs2bN0sqVK7VkyRLdddddevTRR9WvXz8NHTq04m+slgu023R9gwhJUpZpafX1o3znkhctPuO1bWO8wQq3aWnX0fTKayQAAAAAAAAAoEpV+zRQZTV+/Hjt3bvXdzxv3jzNmzdPkrR7927FxcX5zu3fv1+ffvqpRo0apdDQ0NOW6fF45PGc/MZ+w4YNZbfbNX36dB07dkyGYah169aaNm2a/vznPzMkv4xubhSpV38/Kkma1zBOg+x22TweJS9apKh77pZhGMVe16bByQW4tx1JVbuGVbf2BwAAAAAAAACg8tTYYMWePXtKnLdJkyaFghCnY1lWoeNWrVrp448/Lm3TcBatAv11aXiwvjiepj05Hv0yfKTOm/9f5f7+uzI3bVLghRcWe13bmJPBiq2HU6uquQAAAAAAAACASsbQAFSL0bFRvv3FvQf69pMXLjrtNW1iCo+sAAAAAAAAAADUDgQrUC2uiApVAz+nJGm1I1DHGjaSJKWsWCEzK6vYaxqHByjIz7s491aCFQAAAAAAAABQaxCsQLVw2gzdFOtdaNuU9OlNY737qalKW7Om2GsMw/CtW7E/KVNp2e4qaSsAAAAAAAAAoHIRrEC1GRUbKXveWtoLW7aX2+YdNXGmqaAKrluxndEVAAAAAAAAAFArEKxAtWno8tOgqFBJ0lEZWtfHu3ZF2pdfyn3sWLHXsG4FAABA3ZWWlqb7779fsbGx8vf3V+fOnfXf//73rNd99NFHuvHGG9WqVSsFBAQoLi5ON910k7Zv314FrQYAAABQEgQrUK0KLrS9ZNDV3h2PRykff1xs/rYNTgYrth5Oq9S2AQAA4NwyYsQIzZ07V5MnT9by5cvVtWtX3XjjjXr33XfPeN0zzzyjjIwMTZo0SStWrNCMGTO0efNmdenSRb/88ksVtR4AAADAmTiquwGo2y4JD1bLAJd2Zmbr23rh2tsgVs0OH1TyosWKGD26SP6CwQpGVgAAANQdy5Yt08qVK/Xuu+/qxhtvlCT17dtXe/fu1V/+8hddf/31stvtxV67ZMkSRUdHF0rr16+f4uLi9Pe//12vv/56pbcfAAAAwJkxsgLVymYYurlRpO942fAbJElZv/6qrG3biuSPCnYpMshPkrTlMMEKAACAumLBggUKDg7WyJEjC6WPHTtWBw8e1IYNG0577amBCkmKjY1V48aNtX///gpvKwAAAIDSI1iBand9gwgF2LwrbS/v2EWZfi5JUsrixcXmz1+34lhathLTsqumkQAAAKhWP//8s9q1ayeHo/Dg8E6dOvnOl8auXbu0d+9edejQocLaCAAAAKDsCFag2oU5HRoWHS5JSrXZtbrbJZKk5MVLZHk8RfIXngqKdSsAAADqgsTEREVERBRJz09LTEwscVlut1u33HKLgoOD9cADD5wxb3Z2tlJSUgptAAAAACoewQqcE8Y0KrDQ9pXXyJLkTkhQ+vr1RfLmj6yQWLcCAACgLjEMo0znCrIsS7fccou++OILvfXWW2rSpMkZ8z/11FMKDQ31bWfLDwAAAKBsCFbgnNA5JFDn1wuQJG0Nj9JvcS0lScmLFhXJW3BkxVaCFQAAAHVCZGRksaMnkpKSJKnYURensixLt956q95++23NmTNHw4YNO+s1EydOVHJysm9jjQsAAACgchCswDljdMHRFQOukiSlrvxMnrT0QvnaxAT79reyyDYAAECdcN555+m3336T2+0ulP7TTz9Jkjp27HjG6/MDFW+++aZef/11jRo1qkT1ulwuhYSEFNoAAAAAVDyCFThnXBMdrlCHXZK0pvPFSg4KlpWZqdSVKwvlq+fvVKMw7yiMbYdTZVlWlbcVAAAAVWv48OFKS0vThx9+WCh97ty5io2NVbdu3U57rWVZuu222/Tmm29q1qxZGjt2bGU3FwAAAEApEazAOSPQbtP1DbzD97Ptdn3Svbek4qeCyh9dkZrt1qHkrKprJAAAAKrFlVdeqYEDB2r8+PF67bXXtGbNGt1+++1asWKF/va3v8lu937p5ZZbbpHD4dDevXt91957772aPXu2xo4dq/POO0/r16/3bZs3b66uWwIAAABQAMEKnFNubhTp21/Sb5BMw1DGhg3KPXiwUL42rFsBAABQ53z00Uf64x//qCeeeEKDBg3Shg0b9N577+mmm27y5fF4PPJ4PIVG3y5ZskSS9MYbb6hHjx6FtuHDh1f5fQAAAAAoimAFzimtAv11abh31MTvEVH6Lr6jZFlKXrK0UL74AsGKbaxbAQAAUCcEBwdr5syZOnTokLKzs/XDDz/ohhtuKJRnzpw5sixLcXFxvrQ9e/bIsqxitz179lTtTQAAAAAoFsEKnHNGx55caHvxZQMlScmLFxf6dlybmAIjKwhWAAAAAAAAAECNRrAC55wrokLVwM8pSfq604VKCI9Qzs6dyvr5F1+elvWDZTO8+0wDBQAAAAAAAAA1G8EKnHOcNkM3xXoX2jZtNi3t1U9S4YW2/Z12xUUFSZK2J6TJY1pFCwIAAAAAAAAA1AgEK3BOGhUbKXveyImPL+kvt82ulI8/lpWT48vTNm8qqBy3qb2J6dXRTAAAAAAAAABABSBYgXNSQ5efBkWFSpKSQsP0ZeeL5Dl+XGlffunL07bgIttMBQUAAAAAAAAANRbBCpyzil1oe+HJqaDaFlhkewuLbAMAAAAAAABAjUWwAuesS8KD1TLAJUna3LaD9jaIVdqaNfKcOCFJasPICgAAAAAAAACoFQhW4JxlMwzd3CjSd7z40gGycnOVsmKFJKlZRKD8HN5HeCsjKwAAAAAAAACgxiJYgXPa9Q0iFGDzrrT9SffLlOnn8k0F5bDb1Kp+sCRpT2KGsnI91dZOAAAAAAAAAEDZEazAOS3M6dCw6HBJUnpgkFZ37anM779Xzp49kqT4vKmgPKalXUfTq6uZAAAAAAAAAIByIFiBc96YRicX2l502UBZkpIXL5ZUeN2KrUdSqrppAAAAAAAAAIAKQLAC57zOIYE6v16AJGl70+b6La6lkhctlmWaahtTIFhxOK26mggAAAAAAAAAKAeCFagRRhcYXbH4soHKPXBAmZs2FRpZse0Ii2wDAAAAAAAAQE1EsAI1wjXR4Qp12CVJqy/qoeSgYCUvWqTYUH/VczkkSVsPE6wAAAAAAAAAgJqIYAVqhEC7Tdc3iJAk5Tr99En33kpZvkJWdrZvdMWBE5lKzcqtzmYCAAAAAAAAAMqAYAVqjJsbRfr2F1/WX+70dKWtXq02MQWngmLdCgAAAAAAAACoaQhWoMZoFeivS8ODJUkHohvqu/iOOrFokdrGBPvysG4FAAAAAAAAANQ8BCtQo4yOLbzQdvqXX6mtX44vjXUrAAAAAAAAAKDmIViBGuWKqFA18HNKkr7udKESQkLVeNMXvvOMrAAAAAAAAACAmodgBWoUp83QTbHehbZNm01Le/VT7opligp2SSJYAQAAAAAAAAA1EcEK1DijYiNlN7z7H1/ST+lbt+lS+3FJ0rG0HB1Ly67G1gEAAAAAAAAASotgBWqchi4/DYoKlSQlhYbry84X6bI9G33nt7FuBQAAAAAAAADUKAQrUCOdutB2yx++lM0yJUlbmQoKAAAAAAAAAGoUghWokS4JD1bLAO86FZvbdtBBf391PrpdEutWAAAAAAAAAEBNQ7ACNZLNMHRzo0jf8eJLB6j/vu8kSVuZBgoAAAAAAAAAapQaGaxYvXq1xo0bp/j4eAUFBalRo0YaNmyYvvvuu2Lzb9q0SQMGDFBwcLDCwsI0YsQI7dq1q8T1ffbZZ+rRo4cCAwMVFRWlMWPGKCEhoaJuB2V0fYMI+du8K21/0qO3LkzcqoDcLG07kibLsqq5dQAAAAAAAACAkqqRwYp//etf2rNnj+677z4tW7ZMM2fOVEJCgrp3767Vq1cXyrtlyxb16dNHOTk5+uCDD/TGG29o27ZtuvTSS3X06NGz1vX555/ryiuvVExMjBYtWqSZM2fqs88+U//+/ZWdnV1Zt4gSCHM6dE10uCQpPSBQX3a5WL0O/qS0bLcOnMis5tYBAAAAAAAAAErKUd0NKIuXX35Z0dHRhdIGDRqkVq1a6a9//av69evnS3/iiSfkcrm0dOlShYSESJIuvPBCtW7dWs8995yeeeaZM9b1l7/8RW3atNH8+fPlcHh/Xc2bN1evXr30xhtvaPz48RV8dyiNMY2i9N/DSZKkRZcN1PjXZuuzZl217UiqGocHVnPrAAAAAAAAAAAlUSNHVpwaqJCk4OBgtW/fXvv37/elud1uLV26VH/4wx98gQpJatasmfr27asFCxacsZ4DBw7o22+/1R//+EdfoEKSevbsqTZt2pz1elS+ziGBOr9egCRpe9PmcgVL0RlJ2no4rZpbBgAAAAAAAAAoqRoZrChOcnKyNm3apA4dOvjSdu7cqczMTHXq1KlI/k6dOmnHjh3Kyso6bZk///yzL29x1+efR/Ua3SjKt7/4soHqu3+zth1hkW0AAAAAAAAAqClq5DRQxbn77ruVnp6uSZMm+dISExMlSREREUXyR0REyLIsHT9+XA0bNiy2zLNdn3++ONnZ2YXWtEhJSZEkmaYp0zRLcEcVwzRNWZZVpXVWtaujQjXF9rtSTEurL+qhF1cs1+xDybX6nguqC31c19HHtR99XPvRx7VfZfYxzw0AAACAuqDagxVr165V3759S5R38+bN6ty5c5H0xx9/XO+8845eeuklXXjhhUXOG4Zx2jLPdO5sec507VNPPaWpU6cWST969OgZR3NUNNM0lZycLMuyZLPVmoE0RQwNC9A7SRnKdfrp5/M6ytrym55cVE+9mocqPiZQthL0c01VV/q4LqOPaz/6uPajj2u/yuzj1FRGjAIAAACo/ao9WNG2bVu99tprJcrbtGnTImlTp07VjBkz9OSTT+qee+4pdC4yMlKSih0BkZSUJMMwFBYWdtr6znZ9cSMu8k2cOFEPPvig7zglJUVNmjRR/fr1C62fUdlM05RhGKpfv36t/nDkjuAQvZO0TZK0+LL+Gjlnvv69oYlmbzikqGA/9W0brX7x9dWrVZSCXdX+2FeoutLHdRl9XPvRx7UffVz7VWYf+/v7V2h5AAAAAHAuqvZPbRs2bKhbb721TNdOnTpVU6ZM0ZQpU/Too48WOd+yZUsFBATop59+KnLup59+UqtWrc745q9jx46+vIMHDy5yff754rhcLrlcriLpNputyj+kMAyjWuqtSm2CA3VJSIC+TMnUgeiGig5I1+Mb5mhdgw76pkF7zfsuR/O++11Ou6HuLSLVLz5a/eKj1SwyqLqbXiHqQh/XdfRx7Ucf1370ce1XWX3MMwMAAACgLqix73ymT5+uKVOm6LHHHtPkyZOLzeNwODR06FB99NFHhYbP79u3T2vWrNGIESPOWEejRo108cUX6+2335bH4/Glr1+/Xlu3bj3r9ahaY5rE+PbnXT5UsdZxPbj5fb27fIr+9sXLGrF9raKSj+qL7cc0dcmv6v3sWvV/fq3+uuw3rd+VqFwP80EDAAAAAAAAQHUwLMuyqrsRpfX888/roYce0qBBg4oNVHTv3t23v2XLFnXt2lVdunTRhAkTlJWVpSeeeEJJSUn6/vvvVb9+fV9eh8Oh3r17a9WqVb60tWvXauDAgRo6dKjuuusuJSQkaMKECQoNDdXGjRuLHT1RnJSUFIWGhio5ObnKp4FKSEhQdHR0rf9WXq5p6aKvftIR98mgQ1hqsjpt36JOO37T+dt+U4uD+7U/OFrrG3TQ+oYdtDW8iSzD+3up5+9Q7zb11b9dtHq3iVZEkF913Uqp1KU+rqvo49qPPq796OParzL7uLpeR6J49AcAAADKgteRZ1ft00CVxZIlSyRJK1as0IoVK4qcLxh/iY+P19q1a/XII4/o2muvlcPhUL9+/fTcc88VClRIksfjKTSCQpL69OmjZcuW6YknntDQoUMVGBioIUOG6Nlnny1xoAJVw2kz9KfmDfXY9gO+tBP1QvW/Lt30vy7dJEnBGWnqtGOrzt/2m+7Y8bEiv03Uxuh4rWvYQd/Xb62lPx7S0h8PyWZIFzQNV7/4aPVvF622MfVKtBg7AAAAAAAAAKD0auTIipqIkRVV56fUDP3veJrWn0jThuQ0pbhPP71TQFamOu7cpvO3/6Z2u7YrJcOp72LitaFBe6X6nVzPIjbUX/3aRat/fIx6tIyUv9NeFbdSInWxj+sa+rj2o49rP/q49mNkRd1BfwAAAKAseB15djVyZAVwJufVC9R59QJ1d9NoeSxLv6Vlat2JdK07kab1yWlKyj05eibTP0Dfdjhf33Y4X5LkyslW+13bNXj7Dwr5PVmHc+tpY3S8DipKb6/fp7fX75O/06ZLWkWpb94i3Q1DA6rrVgEAAAAAAACgViBYgVrNbhjqWC9QHesF6rYm9WValrZlZGndiXStP5GmdcdTlVAgeJHt59Lm+I7aHN9RkuTMzVX83p0auPcXeU54tM8M1856jfTZbwn67LcESVL7hiHqFx+tfu2idX7jMNltTBcFAAAAAAAAAKVBsAJ1is0wFB8UoPigAI1tFCXLsrQ7M0frTqTp6+Op+vrocR2yTgYbcp1O/dQqXj+1ipck2T1utfh9n8IOJykj1a69ZoR+PZSiXw+l6J9rdigyyE992npHXFzaJkoh/s7qulUAAAAAAAAAqDEIVqBOMwxDLQJdahHo0k2xkZLitC8zW+tOpOmr/Ye17niq9vv5+/J77A5tb9ZCatZCkmQzTcUcSZDrWLoSswJ0LNWtDzf9rg83/S6HzdDFzSO8oy7io9WifnA13SUAAAAAAAAAnNsIVgCnaBrgUtMAl65vGClJOpSdo6/2HdIX23frmxxLu0PDfXlNm02HGjaQGp68PjTxhMwTbmWl2vTV/uP6emeiZnz8m5pHBfkCF13jIuTnYIFVAAAAAAAAAJAIVgBn1dDlp2tbN9O1rZtJkhKSU/T5t5v11e9H9K1foHbFxMqynQw8JEeGSZEnr/dLyZQn2aMdx3O0a+M+zf5yt4JdDl3WJkr94mPUp219RQW7qviuAAAAAAAAAODcQbACKKXo0BCNHNBbIyVZHo8ObdqsLzb9oHXHU7U5ppG2N20us0DwIickQAqRPE28x0aGWznHs7U4MUVLlx6Vbb5HnRuHqX98tPrGR6tDbIgMg0W6AQAAAAAAANQdBCuAcjDsdsV2vUjXd71I11mWcnbu1KE1a7Vu6059a/fXD63jtbVZS7kdJ/+pWYEOWYEOmY2CvAlZHn2TlK2Nv/2uZ7/epYZ2h/rFR6t/fLR6tYpSgJ+9mu4OAAAAAAAAAKoGwQqgghiGIVerVopr1UpxkkYeParUNWt0bMEcfZOQqB/iWumH1u30a/NWynX6nbzQ3y4zNlBmbKAkaW+2R3OPp+k/axPlWpyrS2JCNSBv1EXj8MBquTcAAAAAAAAAqEwEK4BK4qhfX+HXXafw665Ty/R0Df3qK6WtXqOkt/6lX8Ij9UPr9vqxVbx+btlGWS7/kxe67DIbBMhsECC3pE9yTa3cd0i27/equWHX4LhIDYyP0QVNw2W3MV0UAAAAAAAAgJqPYAVQBWxBQQq5/HKFXH65GrrdarF5s/qsWq3URe8o8/cD2tasuX5o1U4/tG6nn1u1VXpAgREUTpvM6ACZ0QHaLmmmO1MvfbNNgavd6h4apBHNo9QuTKpvWdV1ewAAAAAAAABQLgQrgCpmOBwK7NpVgV27KvqRh5WzY4carlqtLmtWK2vlEnkMQzsbN9MPrdvpx9bt9GOreKUE1ztZgMMms76/0upLn0n6LPGojD25sm/4XYGWFGLYFGG3q76fQ7EBfmoS6FKTYJdi6vkrqp5LUcEuhQU4ZWNUBgAAAAAAAIBzBMEKoBoZhiFX69ZytW6tqDvvUG5CgtLWrFXI6lVq++UqjVy9XKZhaG/DRvq+dTv92KqdfmjTTsdDwk4W4rDJCnfJLSklb/vddzJHcudISSnSYVNGtkdGtke2HFOBlqFQm00RDruinU7FBvqpaaCfGtbzV1SwN6gRVc9PkUEuppsCAAAAAAAAUKkIVgDnEGd0tMKvv07h118nMz1daV9+pbTVq+Rc+7maf75Swz9fKUvS/piG3sBF63baHN9RiaFhZy7YZkj+dln+dlmSTJ0MbOz3ZcqRzBzpWIqMAx4ZOaaU7ZGRbSrIkkJtdkU67YpxOdXI30+Ng1yqX8+lqHou1c8LbkQG+8lpt1Xa7wcAAAAAAABA7USwAjhH2YKCFHLF5Qq54nJZbrcyNm1S2qrVSl29Wk3371fTI4c05KvVkqRMP5eSQkKVFBKmpNAw38/EEO9+YniEkkLCdDy4nizbWYIJTpssp00FV8AoGtjI9QY2Ek7I+D1/xIYp5XgUZBkKs9kU6bSrgctPjQLyR2v4FRix4VJkkJ/8nfYK/70BAAAAAAAAqHkIVgA1gOFwKOjiixV08cWKnvCIsrdvV9rq1UpdtVpZP/2kgJxsNTqWoEbHEs5YjscwlBwc4g1shIYrKSRUiXk/CwY6EkPDlOkfcOZG2QwpwCErQMUGNvb5UtxSboqMwycDGvnBjQBTCrfbFeW0q4G/n2ID/VQ/2F9R9U4GNurnTUcV6MefKwAAAAAAAKC24tM/oIYxDEP+bdrIv00bRd15p3KOHNGRjRtVT4aslGR5TpzI2wruezelpCgiNVkRqcnSgX1nrCfT5VJSvbMHNU7UC5VZ0tEawYWTU/M2X0tMt5SdLONQgdEa2d4pqfw9lsLtdtV3OtTQ30+hLocC/OwK9LMr0M+R99OugLz9AD+7Ap3ecwF+dgW57Ap0evf9HExVBQAAAAAAAJxLCFYANZyjfn05L7xQIdHRsp0laGB5PDJTU33BC3fBYEZy4eCG60Sy6iWfUOND+2Xt3HraMj2GoeR6Id4gRl4Ao+DP4yGhvv2yjtaQpLS8zTcVlWlJnhzJIxnZlpRpSR5LhseUPPn7Vl6+vP28zW5a8rMZ8jcMBdjtCjAMBTpsCnLYFWy3q57TrmCnTcF+Dl/wI9DPrgCnXUH5QZICgZCTgRK7/Ow2GQYLkgMAAAAAAAClQbACqEMMu132sDDZw8JKdZ2ZnV14pEZy4dEbESdOqGl+wCPhd3m2/SxPcrLkdhcqJ9PlKhTIKLTvSwst2WgNmyHZ7JKzcGDj1CBHcdySsuUd1XGGu5bhyZLhMWVLM08GQUxLlkeyTO/mC4KY3oCIYVpyGYZchiF/my0vIGJToN2mILtNQU6Hgh02hTrtqufnUHD+SBDnyREip44Y8XcYyvWYJbgzAAAAAAAAoGYiWAHgrGwul2wx0XLGRJf4GsuyZKalFZmSKu7UkRyJB+TZ+YvvvJmWJo/NpuTgekoMCVdSaGiRAMfxkFBlulzKdrqU5XIpy8+lbD8/Zfu5KvS+LbtNlt0m06901+XKOwrk9My8LVe2XI/smR7Z3B7Z3d6giM1tSh7TNypEHu+uYW2Vw2aX02ZXgN3uC4AEO7yjQUKddoX6ORTm51CEy6lwf4eC/Z0KdjlUz9+hIJdDwXmb3cboDwAAAAAAAJw7CFYAqBSGYcher57s9epJTZqU+DorN1eelJQi622cDHgclGffbzKzs2Rl58jKypKZky0rO0eerGxlWqayLCnTtJRp2JTt51JWXiDDG+Dw8wU3slz+vnNZBfJl+eXncZ1y3qUcv1JGLs7CtNtl2u1SueMsJwMgcsu7pVly5ubKmeOW3e2W3e2R3e2R4TZl95hyWJJThpyGTX42m/wcDvk7nArwcyrQ36V6Lj+FOr3BjzA/hyIDnIr0925hAd4gSKCfnWmvAAAAAAAAUG4EKwCcUwynU47ISDkiI8tdlmWasnJyZGVny8zOlpW3FdnPypaVc8p+Vpqs1Pxr8wIj2dlyZ2cr02Mq0+PxBkRMS5mWlCEpS1KWDGXYbMq2O4sNeuQHTHxBEZe/N3Di9AZQMl0u5TorKCBiGMr181NumQIspveOcuXd0iUdP3nW4XbLLydHjly3/HJz5fR45HSbcpqm/CxLLkvyMwz52/LWBXE4FOTnVLDLTyH+LoUE+Ss8KFAR9QIVFeSv+gFOhfk55M+aHwAAAAAAAHUSwQoAtZZhs8nw95f8/WWv4rott9sb6MgLllhZWTKzc2TlnGY/O1VWmnffnZWlLLdb6W6PMnO9P5OzspXrcCjTtJRhWcqwpAwZyjBsyrDZlGl3KMvppyyXS5kuf2X55f3MmyYr0+VfoYEQt8Mht6Mc/4WkZ3m3hKRCyTaPR67cHPnl5Mrpccvh8eRtpvenacphWrKbpuyWJYcl2S1LdhmyW5LDMLz7hiG7zSa7YZfDZpPDbpfD5vD+dDjlcDhkd/jJ6XTK6eeUy2aTy2bIYbPJbjNObsbJfZvNkMNmyJaX5shLK5jHblNevYZsNuWVJ9kMb9k2m4ot25ClHLcp07R0tuVaAAAAAAAAaiOCFQBQCQyHQ4bDIVtQULnLMk1TCQkJio6Olu0Mn2Rbubkys7JkZmZ6gyCZWbKyMmVmZsnMypSVlaScE1nKyM5WWnau0nNzlJHjVrrbrdRcj1LdHqWbltJNSxmWoUzDUKbNpiybXVkOh7IdDmU5ncry8wY+slz+yvQ7uW6IVQGfspt2uzLtAcr0Dyh3WSWTN3TEkuSRHNm5crrdcrrdchTaPLJ7vNNo2TymbB7vT8NjymZa3gXYTStvNi7vT8u0ZFmGTFMyTUOmZciUIdOyyWNKHtmUK7s8ll1uGfLILkuWdwo1W16QxWGTw54XbLEbctptctq9gQ+n3fCes3nTHaekO22GN823783jtNl8ZTny0vPL9KYXKKeYvH72wvlOV5bTbjBKBgAAAAAAlBjBCgCoJQynU3an07tOSCWxLMs7tVZmZqHAiCcjS5lZGUrJzNLx9Ewdz8hWSlaOUnNylZqbq/RcUxmm6Z02S1KWYSjbsCnbblO23aFspzcYkuP0802Zlet0KMfhVI7Tr3yjOErB7XDK7XAqs0pqKxnDNGVYlmyWJcMyvT/Nk8feNLcMy8rL581vmHnHbst3zrAsGSp8bDMtKb985V1X8KdV3CbfNfKVq5P7lrxtk04ey5It76dhWbJJ3nIk776U1wbJkHe0SX66IW/gw+bL6x3hYjMMGYZkN7znDXlH1OSfs9sM2Qxb3ggYm+x2777NZpPdZveOgskLCtnyR+DYveecDrtsdpscdoccDu+x026X3eGQn8Muh8Mhp9Mhh8MhPz+7nHnHTqfj5MgZ+8kRNIYsuT2Wcj2mDFOyZMnKu3/LkkzLlExTlseU6fHI8m2mLI9H8piyTLcstynL8ubxpnm8aaY779iU5fZIlnnyevNkWTLzrstPN828Y4/kO/bIys+XdyzTKpDmycvjvVamx9v2U48lyekny+mUnE5ZDmeBfW+6b3P4SU6nTKdTystn+tIcshx+3nNOpyyb3Vt2/u8v73dpWb4k79+qvN+tL1eh8wWuK3CNClxzpnKD/By6qlPDCvgXDgAAAADIR7ACAFBihmHIcLkkl6vI1FpBkqIqqB7feiM5OTKzspSTla2MzGxlZmcrPTtXmdnZysrJVWZOrrJyc5WV61aW26Mst1s5Ho+yPaayTVPZpqVs01KOLOVYUo7yNsNQjmFTrmFTjs1Qjs2uXLtduTa7cux25TgcynXkB0ucynE4K2TkSJl+FzabLHkHbaCaeOT9wF4eeZ+g07OZpmxmXhDJtLzHlpn305KsbbL5gkp5QZ28wE9+8OdkIEoFAlAFA0B5I3pUIOAkyxfIMnRqGd6fvkCWL7BVNBBls/LKMi1ffba8gFl+EMsX2LIkwzJkWHbZZJNhWDLslmw2U8pruyVDcpuy3DmyjFzvh/55I24sw/AGFAxvSMoy8tPyRuQYkpWfLkMyvPumDJmGTR6bTaZhK7TvMWwyfemG9zhv3yyQ3zSMvK3g8cl8lrznrQJ58/ctw1CAZRCsAAAAAIAKRrACAHDOKbTeSEiInPIGQ6qD5XbLzM5Wbk6OMrOylZWdo6ycHGXlBUyyct3KzslVltutbLc776dHWR5TWR5TOabpDZ5YlrItS1mWlGNJ2TKUIyk7b5SJ27K8gQnDkCnl/Tz54awl+T4o9Z335TFk5ufzfbBqFDr2peX9zP9QuEg6i2aUi2nzfliO2q1eUmJ1NwEAAAAAah2CFQAAnIHhcMjucMgeFCT/SqqjpOuSVIX8aW68S29YvqU4LMvKW5LDKrA0h3eaHFOSxzRlWpb3etO7WLjHsmRapvdajylP/jWmKU/eZno8cntMeTweufP23W6Pct0e5XpMb5rpTXObpve8xyOPZcnjyTu2LF95HtPybpYlj+Vth2lZclt592KZ3vbm3YfH8t6bRwWWHZF3CiDvN/gLpp0MCp0MDqlwMKhggMhWMBhky8trk04JGuV/a7/gyALLKLyZhuEbaeDdP3kO1YCAFAAAAABUOIIVAADAx7v2g2QzJIkPwitKZQak8gNJVoEAk3fqsJNrMpj5QSgp7/zJQJN1SlDKOxFU0YCUmbeYg1mwvLzyfW3QyYCWZXlnfPJO8pS35T1f+U9W/mPmXXGkcH6d7pq8/CpU7sn1TWR6pNxcKTdXltst5eRKuTmycnOlXHfeuRzv+dxcKSdvPyfHm57jzvuZIysnP693Wjrl5Eg5uXKEhUrD+1ZoPwIAAABAXUewAgAAoAYzDMO7hgwBJgAAAABADcYYdgAAAAAAAAAAUK0IVgAAAAAAAAAAgGpFsAIAAAAAAAAAAFQrghUAAAAAAAAAAKBaEawAAAAAAAAAAADVimAFAAAAAAAAAACoVgQrAAAAAAAAAABAtSJYAQAAAAAAAAAAqhXBCgAAAAAAAAAAUK0IVgAAAAAAAAAAgGpFsAIAAAAAAAAAAFQrghUAAAAAAAAAAKBa1chgxerVqzVu3DjFx8crKChIjRo10rBhw/Tdd98VyufxePTCCy9o0KBBaty4sQIDA9WuXTtNmDBBJ06cKFFdffr0kWEYRbZBgwZVwp0BAAAAAAAAAFD31Mhgxb/+9S/t2bNH9913n5YtW6aZM2cqISFB3bt31+rVq335MjMzNWXKFDVr1kwvvviili1bpttuu02vvvqqevXqpczMzBLV16JFC61bt67Q9uKLL1bS3QEAAAAoTlpamu6//37FxsbK399fnTt31n//+98SXZuQkKAxY8YoKipKgYGB6tGjh1atWlXJLQYAAABQUo7qbkBZvPzyy4qOji6UNmjQILVq1Up//etf1a9fP0lSQECAdu/ercjISF++Pn36qGnTpho5cqQ+/PBDjRo16qz1BQQEqHv37hV7EwAAAABKZcSIEfr222/19NNPq02bNnr33Xd14403yjRN/d///d9pr8vOzlb//v114sQJzZw5U9HR0Xr55Zc1aNAgffbZZ+rdu3cV3gUAAACA4tTIYMWpgQpJCg4OVvv27bV//35fmt1uLxSoyHfxxRdLUqG8AAAAAM5dy5Yt08qVK30BCknq27ev9u7dq7/85S+6/vrrZbfbi7129uzZ+vnnn/X111+rR48evmvPP/98Pfzww9qwYUOV3QcAAACA4tXIaaCKk5ycrE2bNqlDhw5nzZs/VVRJ8krSzp07FRERIYfDoZYtW2rSpEklnkIKAAAAQPktWLBAwcHBGjlyZKH0sWPH6uDBg2cMOCxYsEBt27b1BSokyeFwaNSoUfrmm2904MCBSms3AAAAgJKpkSMrinP33XcrPT1dkyZNOmO+AwcOaMKECbrooos0ZMiQs5Z7ySWX6Prrr1d8fLwyMzO1fPly/e1vf9OXX36pNWvWyGYrPt6TnZ2t7Oxs33FKSookyTRNmaZZijsrH9M0ZVlWldaJqkUf1370ce1HH9d+9HHtV5l9zHPj9fPPP6tdu3ZyOAq/henUqZPvfM+ePU977aWXXlokPf/aX375RY0aNargFgMAAAAojWoPVqxdu1Z9+/YtUd7Nmzerc+fORdIff/xxvfPOO3rppZd04YUXnvb6pKQkDR48WJZl6f333z9toKGgGTNmFDoePHiw4uLi9NBDD2nRokUaPnx4sdc99dRTmjp1apH03bt3Kzg4+Kz1VhTTNJWSkqKUlJQS3S9qHvq49qOPaz/6uPajj2u/yuzjtLQ0SZJlWRVabk2TmJioFi1aFEmPiIjwnT/Ttfn5SnvtqV9CSk5OlnTyy0gAAABASeS/fqzrr+vPpNqDFW3bttVrr71WorxNmzYtkjZ16lTNmDFDTz75pO65557TXnv8+HENHDhQBw4c0OrVq4t9o1NSo0aN0kMPPaT169efNlgxceJEPfjgg77jAwcOqH379urSpUuZ6wUAAEDdlZqaqtDQ0OpuRrUyDKNM58pz7em+hNSkSZMz1gcAAAAUJzExsc6/rj+dag9WNGzYULfeemuZrp06daqmTJmiKVOm6NFHHz1tvuPHj2vAgAHavXu3Vq1a5RvuXV5n+tacy+WSy+XyHQcHB2v//v2qV6/eWd9IVaSUlBQ1adJE+/fvV0hISJXVi6pDH9d+9HHtRx/XfvRx7VeZfWxZllJTUxUbG1uh5dY0kZGRxY6ASEpKkqRiR05UxLWnfgnpxIkTatasmfbt28ebzDqOv+0oiOcBBfE8IB/PAgpKTk5W06ZNz/jas66r9mBFWU2fPl1TpkzRY489psmTJ582X36gYteuXVq5cqUuuOCCctc9d+5cSVL37t1LfI3NZlPjxo3LXXdZhYSE8EexlqOPaz/6uPajj2s/+rj2q6w+5kNx6bzzztN7770nt9tdaN2Kn376SZLUsWPHM16bn6+gklx76peQ8oWGhvLvGZL4247CeB5QEM8D8vEsoCCmBj69Gvmbef755/XEE09o0KBBuuqqq7R+/fpCW77MzExdccUV2rx5s6ZOnSq3210o386dOwuV63A41L9/f9/xF198oUGDBmnWrFlauXKllixZorvuukuPPvqo+vXrp6FDh1bZPQMAAAB12fDhw5WWlqYPP/ywUPrcuXMVGxurbt26nfHaLVu2aMOGDb40t9utt99+W926davzo1YAAACAc0GNHFmxZMkSSdKKFSu0YsWKIufzFyk5cuSIvv32W0nSfffdVyTf6NGjNWfOHN+xx+ORx+PxHTds2FB2u13Tp0/XsWPHZBiGWrdurWnTpunPf/4zUTAAAACgilx55ZUaOHCgxo8fr5SUFLVq1UrvvfeeVqxYobffflt2u12SdMstt2ju3LnauXOnmjVrJkkaN26cXn75ZY0cOVJPP/20oqOj9corr2jr1q367LPPqvO2AAAAAOSpkcGKtWvXlihfXFxcqVZXPzVvq1at9PHHH5emaeccl8ulyZMnFzt0HbUDfVz70ce1H31c+9HHtR99XDU++ugjTZo0SU888YSSkpIUHx+v9957TzfccIMvT/4XkAq+tne5XFq1apUefvhh/elPf1JGRoY6d+6s5cuXq3fv3qVqA32NfDwLKIjnAQXxPCAfzwIK4nk4O8Mqzaf5AAAAAAAAAAAAFYx5jAAAAAAAAAAAQLUiWAEAAAAAAAAAAKoVwYpaKi0tTffff79iY2Pl7++vzp0767///W91NwtlsHr1ao0bN07x8fEKCgpSo0aNNGzYMH333XdF8m7atEkDBgxQcHCwwsLCNGLECO3atasaWo3yeP3112UYhoKDg4uco49rri+//FKDBw9WeHi4AgIC1Lp1a02fPr1QHvq35tq8ebOuueYaxcbGKjAwUPHx8Zo2bZoyMjIK5aOPz32pqal6+OGHdfnll6t+/foyDENTpkwpNm9p+vOll15SfHy8XC6XmjdvrqlTpyo3N7cS7wSlVZ7XzwkJCRozZoyioqIUGBioHj16aNWqVZXcYlSWsj4LH330kW688Ua1atVKAQEBiouL00033aTt27dXQatRWSrqvfVjjz0mwzDUsWPHSmglqkJ5n4VFixapd+/eCgkJUVBQkDp06KBXX321EluMylSe52HNmjUaOHCgoqOjFRwcrE6dOukf//iHPB5PJbcalaE07x+Kw+vIwghW1FIjRozQ3LlzNXnyZC1fvlxdu3bVjTfeqHfffbe6m4ZS+te//qU9e/bovvvu07JlyzRz5kwlJCSoe/fuWr16tS/fli1b1KdPH+Xk5OiDDz7QG2+8oW3btunSSy/V0aNHq/EOUBoHDhzQQw89pNjY2CLn6OOa691331Xv3r0VGhqqt956S8uWLdMjjzxSaPFX+rfm+vXXX9WzZ0/t2bNHL774opYuXaobbrhB06ZN04033ujLRx/XDImJiXr11VeVnZ2ta6655rT5StOfTz75pO677z6NGDFCn3zyie666y799a9/1d13313Jd4PSKOvr5+zsbPXv31+rVq3SzJkztWjRIsXExGjQoEH6/PPPq6j1qEhlfRaeeeYZZWRkaNKkSVqxYoVmzJihzZs3q0uXLvrll1+qqPWoaBXx3vr777/Xc889p5iYmEpsKSpbeZ6Fp59+WiNGjFDHjh31wQcfaPHixbrrrruUk5NTBS1HZSjr8/DZZ59pwIABcrvdeu2117Rw4UL16dNH9913nx588MEqaj0qUknfPxSH15HFsFDrfPzxx5Yk69133y2UPnDgQCs2NtZyu93V1DKUxZEjR4qkpaamWjExMVb//v19aSNHjrSioqKs5ORkX9qePXssp9NpPfzww1XSVpTfkCFDrKFDh1qjR4+2goKCCp2jj2um33//3QoKCrLGjx9/xnz0b801adIkS5K1Y8eOQum33367JclKSkqyLIs+rilM07RM07Qsy7KOHj1qSbImT55cJF9J+/PYsWOWv7+/dfvttxe6/sknn7QMw7B++eWXyrkRlEp5Xj+//PLLliTr66+/9qXl5uZa7du3ty6++OJKazMqR3meheJetx84cMByOp3WLbfcUuFtReWriPfWubm5VufOna17773X6t27t9WhQ4fKai4qUXmehY0bN1o2m8165plnKruZqCLleR5uuukmy+VyWWlpaYXSL7/8ciskJKRS2ovKVdL3D8XhdWRRjKyohRYsWKDg4GCNHDmyUPrYsWN18OBBbdiwoZpahrKIjo4ukhYcHKz27dtr//79kiS3262lS5fqD3/4g0JCQnz5mjVrpr59+2rBggVV1l6U3dtvv63PP/9cr7zySpFz9HHN9frrrys9PV2PPPLIafPQvzWb0+mUJIWGhhZKDwsLk81mk5+fH31cgxiGIcMwzpinNP25YsUKZWVlaezYsYXKGDt2rCzL0sKFCyu0/Sib8rx+XrBggdq2basePXr40hwOh0aNGqVvvvlGBw4cqLR2o+KV51ko7nV7bGysGjdu7HvdjpqlIt5bP/3000pKStKTTz5ZWc1EFSjPs/DPf/5TLpdLf/rTnyq7magi5XkenE6n/Pz8FBAQUCg9LCxM/v7+ldJeVK6SvH84HV5HFkWwohb6+eef1a5dOzkcjkLpnTp18p1HzZacnKxNmzapQ4cOkqSdO3cqMzPT18cFderUSTt27FBWVlZVNxOlkJCQoPvvv19PP/20GjduXOQ8fVxz/e9//1NERIS2bNmizp07y+FwKDo6WnfeeadSUlIk0b813ejRoxUWFqbx48dr165dSk1N1dKlSzVr1izdfffdCgoKoo9rmdL0Z/7rrvPOO69QvoYNGyoqKorXZeeI8rx+/vnnn0/7LEhi+p8apqLfS+3atUt79+71vW5HzVLe5+HXX3/VjBkz9K9//avY9ehQc5TnWfjf//6ndu3a6cMPP1Tbtm1lt9vVuHFjTZgwgWmgaqjyPA933nmncnJydO+99+rgwYM6ceKE/vOf/2jBggV6+OGHK7XdOPfwOrIoghW1UGJioiIiIoqk56clJiZWdZNQwe6++26lp6dr0qRJkk726en63bIsHT9+vErbiNK566671LZtW40fP77Y8/RxzXXgwAFlZGRo5MiRuv766/XZZ5/pL3/5i9566y0NHjxYlmXRvzVcXFyc1q1bp59//lktW7ZUSEiIhg4dqtGjR2vmzJmS+Ddc25SmPxMTE+VyuRQUFFRsXl6XnRvK8/qZ1961S0X2p9vt1i233KLg4GA98MADFdZGVJ3yPA+maWrcuHEaMWKEBg8eXGltRNUoz7Nw4MABbd++Xffee6/uvfdeffbZZxozZoyee+65IiMvUTOU53no1q2bVq9erQULFqhRo0YKDw/X2LFj9eSTT+rPf/5zpbUZ5yZeRxblOHsW1ERnGn5U1qFJODc8/vjjeuedd/TSSy/pwgsvLHSOfq+ZPvzwQy1ZskSbN28+az/RxzWPaZrKysrS5MmTNWHCBElSnz595Ofnp/vvv1+rVq1SYGCgJPq3ptqzZ4+GDh2qmJgYzZ8/X/Xr19eGDRs0Y8YMpaWlafbs2b689HHtUtL+pN9rhvL0E31cu1REf1qWpVtuuUVffPGFPvzwQzVp0qSimocqVtbn4YUXXtD27du1ePHiymgWqkFZnwXTNJWamqr33ntPN9xwgySpb9++Sk9P14svvqipU6eqVatWFd5eVK6yPg/fffedhg8frm7dumnWrFkKCgrS6tWr9dhjjykrK0uPP/54ZTQX5zBeRxZGsKIWioyMLDbylpSUJKn4bwGiZpg6dapmzJihJ598Uvfcc48vPTIyUlLxEdekpCQZhqGwsLCqaiZKIS0tTXfffbf+9Kc/KTY2VidOnJAk33DgEydOyOl00sc1WGRkpLZv364rrriiUPqVV16p+++/X5s2bdKwYcMk0b811YQJE5SSkqLvv//e9+35yy67TFFRURo3bpxuvvlmNWjQQBJ9XFuU5m9yZGSksrKylJGR4QtMFsx76hcPUD3K8/qZ1961S0X0p2VZuvXWW/X2229r7ty5vv/nUfOU9XnYt2+fnnjiCT399NPy8/PzvcZ3u90yTVMnTpyQy+UqMmc9zl3l/X/i8OHDxb4fePHFF7Vp0yaCFTVMeZ6Hu+++WzExMVqwYIHsdrskb/DKZrNpypQpuummm9SiRYvKaTjOObyOLIppoGqh8847T7/99pvcbneh9J9++kmS1LFjx+poFspp6tSpmjJliqZMmaJHH3200LmWLVsqICDA18cF/fTTT2rVqhULNZ2jjh07piNHjuj5559XeHi4b3vvvfeUnp6u8PBw3XTTTfRxDVbc/JOS94MMSbLZbPRvDff999+rffv2Rab56dq1qyT5poeij2uP0vRn/loVp+Y9fPiwjh07xuuyc0R5Xj+fd955p30WznYtzj3lfS+VH6h488039frrr2vUqFGV1lZUvrI+D7t27VJmZqbuu+++Qq/xv/rqK/32228KDw/XxIkTK739qDjl+dtQkvcDqFnK8zx8//33uvDCC32Binxdu3aVaZr67bffKr7BOGfxOrIo/iLWQsOHD1daWpo+/PDDQulz585VbGysunXrVk0tQ1lNnz5dU6ZM0WOPPabJkycXOe9wODR06FB99NFHSk1N9aXv27dPa9as0YgRI6qyuSiFBg0aaM2aNUW2K664Qv7+/lqzZo1mzJhBH9dgf/jDHyRJy5cvL5S+bNkySVL37t3p3xouNjZWv/zyi9LS0gqlr1u3TpLUuHFj+riWKU1/Dho0SP7+/pozZ06hMubMmSPDMHTNNddUUatxJuV5/Tx8+HBt2bJFGzZs8KW53W69/fbb6tatm2JjYyut3ah45XkWLMvSbbfdpjfffFOzZs1iLvpaoKzPQ+fOnYt9jX/++ecrLi5Oa9asKTRSHue+8vxtONP7AZvN5vuCC2qO8jwPsbGx2rhxozweT6H0gu8dUHfwOrIYFmqlgQMHWuHh4darr75qrV692rrtttssSdbbb79d3U1DKT333HOWJGvQoEHWunXrimz5fvvtNys4ONi67LLLrGXLllkfffSR1bFjRys2NtZKSEioxjtAWYwePdoKCgoqlEYf11xDhw61XC6XNX36dGvlypXWU089Zfn7+1tDhgzx5aF/a65FixZZhmFY3bt3t95//31r1apV1pNPPmkFBwdb7du3t7Kzsy3Loo9rkmXLllnz5s2z3njjDUuSNXLkSGvevHnWvHnzrPT0dMuyStefM2bMsAzDsB599FFr7dq11rPPPmu5XC7rtttuq47bw2mU5PXzuHHjLLvdbu3Zs8eXlpWVZXXo0MFq0qSJ9c4771grV660hg8fbjkcDmvt2rXVcSsop7I+C/fcc48lyRo3blyR1+ybNm2qjltBBSjr81Cc3r17Wx06dKjsJqOSlPVZyMnJsbp06WKFhoZaM2fOtFauXGk98sgjlt1ut+65557quBVUgLI+D//4xz8sSdaVV15pLVy40Pr000+tRx55xHI4HNaAAQOq41ZQAUry/oHXkSVDsKKWSk1Nte69916rQYMGlp+fn9WpUyfrvffeq+5moQx69+5tSTrtVtDGjRut/v37W4GBgVZISIh1zTXXWDt27KimlqM8igtWWBZ9XFNlZGRYjzzyiNWkSRPL4XBYTZs2tSZOnGhlZWUVykf/1lyrV6+2Lr/8cqtBgwZWQECA1aZNG+vPf/6zdezYsUL56OOaoVmzZqf9f3f37t2+fKXpz5kzZ1pt2rSx/Pz8rKZNm1qTJ0+2cnJyquiOUBIlef08evToIs+BZVnW4cOHrZtvvtmKiIiw/P39re7du1srV66swtajIpX1WTjT345mzZpV7U2gwpTnb8OpCFbUbOV5FhITE6077rjDiomJsZxOp9WmTRvr2WeftTweTxXeASpSeZ6HDz/80LrkkkusqKgoKygoyOrQoYM1ffp0Ky0trQrvABWpJO8feB1ZMoZl5U2SBwAAAAAAAAAAUA1YswIAAAAAAAAAAFQrghUAAAAAAAAAAKBaEawAAAAAAAAAAADVimAFAAAAAAAAAACoVgQrAAAAAAAAAABAtSJYAQAAAAAAAAAAqhXBCgAAAAAAAAAAUK0IVgAAAAAAAAAAgGpFsAIAoDlz5sgwDG3cuFGStGzZMk2ZMqV6G3WWdsTFxWnMmDFV2h4AAAAAAABUDoIVAIAili1bpqlTp1Z3M87YjgULFujxxx+v4hYBAAAAAACgMjiquwEAgLojIyNDgYGBFVLWBRdcUCHlAAAAAAAAoPoxsgIAUMiYMWP08ssvS5IMw/Bte/bskSRZlqVXXnlFnTt3VkBAgMLDw3Xttddq165dhcrp06ePOnbsqP/973/q2bOnAgMDNW7cOEnS+++/r8svv1wNGzZUQECA2rVrpwkTJig9Pb3E7ShuGqh9+/Zp1KhRio6OlsvlUrt27fT888/LNE1fnj179sgwDD333HN64YUX1Lx5cwUHB6tHjx5av359Rf4qAQAAAAAAUEKMrAAAFPL4448rPT1d8+fP17p163zpDRs2lCTdcccdmjNnju69914988wzSkpK0rRp09SzZ0/98MMPiomJ8V1z6NAhjRo1Sg8//LD++te/ymbzxsi3b9+uwYMH6/7771dQUJC2bNmiZ555Rt98841Wr15donac6ujRo+rZs6dycnI0ffp0xcXFaenSpXrooYe0c+dOvfLKK4Xyv/zyy4qPj9eLL77oq2/w4MHavXu3QkNDy/+LBAAAAAAAQIkRrAAAFNKyZUtfwKF79+6Fzq1fv16vvfaann/+eT344IO+9EsvvVRt2rTRCy+8oGeeecaXnpSUpHnz5qlfv36Fynnsscd8+5ZlqVevXmrXrp169+6tH3/8UZ06dTpjO4rzwgsv6MCBA9qwYYMuvvhiSdIVV1whj8ejf//737r//vvVpk0bX/569epp6dKlstvtkqTY2FhdfPHFWr58uW644YYS/a4AAAAAAABQMZgGCgBQYkuXLpVhGBo1apTcbrdva9Cggc4//3ytXbu2UP7w8PAigQpJ2rVrl/7v//5PDRo0kN1ul9PpVO/evSVJv/32W5natnr1arVv394XqMg3ZswYWZblG7GR76qrrvIFKiSpU6dOkqS9e/eWqX4AAAAAAACUHSMrAAAlduTIEVmWVWiqp4JatGhR6Li4KZvS0tJ06aWXyt/fXzNmzFCbNm0UGBio/fv3a8SIEcrMzCxT2xITExUXF1ckPTY21ne+oMjIyELHLpdLkspcPwAAAAAAAMqOYAUAoMSioqJkGIa++OIL34f7BZ2aZhhGkTyrV6/WwYMHtXbtWt9oCkk6ceJEudoWGRmpQ4cOFUk/ePCgr+0AAAAAAAA4NzENFACgiNONMhgyZIgsy9KBAwd00UUXFdnOO++8s5adH8A4NbAxa9asErejOP3799evv/6qTZs2FUp/6623ZBiG+vbte9YyAAAAAAAAUD0YWQEAKCI/6PDMM8/oyiuvlN1uV6dOndSrVy/dfvvtGjt2rDZu3KjLLrtMQUFBOnTokL788kudd955Gj9+/BnL7tmzp8LDw3XnnXdq8uTJcjqdeuedd/TDDz+UuB1+fn5F8j7wwAN66623dNVVV2natGlq1qyZPv74Y73yyisaP358ocW1AQAAAAAAcG5hZAUAoIj/+7//06233qpXXnlFPXr0UNeuXX3TKc2aNUv//Oc/9b///U833HCDrrrqKj3xxBNKT08vsrh1cSIjI/Xxxx8rMDBQo0aN0rhx4xQcHKz333+/VO04Vf369fX111+rX79+mjhxooYMGaJPPvlEf/vb3/TSSy+V7xcCAAAAAACASmVYlmVVdyMAAAAAAAAAAEDdxcgKAAAAAAAAAABQrQhWAAAAAAAAAACAakWwAgAAAAAAAAAAVCuCFQAAAAAAAAAAoFoRrAAAAAAAAAAAANWKYAUAAAAAAAAAAKhWBCsAAAAAAAAAAEC1IlgBAAAAAAAAAACqFcEKAAAAAAAAAABQrQhWAAAAAAAAAACAakWwAgAAAAAAAAAAVCuCFQAAAAAAAAAAoFoRrAAAAAAAAAAAANWKYAUAAAAAAAAAAKhWBCsAAAAAAAAAAEC1IlgBAAAAAAAAAACqFcEKAAAAAAAAAABQrQhWAAAAAAAAAACAakWwAgAAAAAAAAAAVCuCFQAAAAAAAAAAoFoRrABwztuzZ48Mw9CYMWOqvO4pU6bIMAytXbu2yus+l1Vnn6Di0I8AAACoKXJzczVt2jS1adNGLpdLhmFo4cKFvvMvv/yyOnTooMDAQBmGoRdffFGSZBiG+vTpU+Z6eU8IAFWHYAWAamUYxhm3OXPmVHcTSyX/heyZtmuuuaa6m1krmaap+fPn6w9/+IP+n737Dq+iyv84/pmbQAiBEAIBAUMChB6SIEVAepeigFgWUVGwgKwIq0hRuoKCBdsqCIurwgoRZEERpQRBQUES6T0UFaUkJAGSALnz+4PN/aWSfifhvl/Pw6P3zJk535kcwpn53jPH399fZcqUkZeXlxo2bKgnnnhCP/zwg9UhAgAAoBBlHGe7ubmpcuXK6tKli/7zn/9YHV6xExgYqMDAQKvDkCR17Ngxx/um1GRDqjfffFOTJ09WtWrV9Nxzz2ny5Mlq0KCBJGnp0qUaOXKkPDw8NGrUKE2ePFmtWrWy4MwAAAXhbnUAACBJkydPzrI8LCxMNWrU0P79+1WhQgUnR5V/HTp0yPbbO6kD6pKsuP1M/vzzTw0cOFA//PCDypcvr27duqlOnToyTVNHjhzR559/rvnz5+vtt9/W3//+d6vDLTaK288RAAAgP1LvJa5evaqDBw/qyy+/1IYNG/TLL79o9uzZFkeHG3nkkUeyTaBkTDb897//Vbly5fTdd9+pdOnSmbZJ0urVq1W9evV02/bv36+yZcvmO8aRI0fqgQceUM2aNfN9DABA7pCsAFAsTJky5YbbS9oD/o4dO+Z4TiVZqVKlis3P5PLly+rZs6d+/fVXPfDAA3r//fdVsWLFdHUuXryo119/XfHx8RZFWTwVp58jAABAfmUcd69fv17dunXTG2+8oZEjRyogIMCawJCjIUOG5PoVTX/88YcqVaqUKVGRuk1SpkSFVPB7ycqVK6bhLJYAANYeSURBVKty5coFOgYAIHd4DRSAYi+79+oPGTJEhmHo+PHj+vDDD9WkSROVKVNGVatW1eOPP64LFy5kOtbGjRv1xBNPqFGjRvL29panp6caN26syZMnKzEx0TknlEHqO1TPnTunJ554QtWqVZOHh4caN26sBQsWZLvf2rVr1atXL1WuXFkeHh6qU6eOnn/++SzPO3XKd1xcnEaNGqWAgACVKlUq3Y3d2rVrdccdd8jLy0u+vr7q16+fDhw4kO46p7rRWgeXL1/WzJkzFRYWJi8vL5UrV06tW7fWkiVLMtU1TVMLFy5U69at5efnpzJlyqh69erq2rVrrqfuv/HGG/r11191xx136LPPPsuUqJCkcuXKafLkyXruuefSlV+4cEHjxo1TvXr1VKZMGVWsWFHdu3fXd999l+kYERERMgxDU6ZM0Y4dO9SzZ0/5+PjIx8dH99xzj06dOiVJOnz4sO677z75+fnJ09NTnTp10q5duzIdL/W6Hjt2TG+88YYaNGigMmXK6NZbb9Xo0aOzTKzktf+mfb/uv//9b7Vo0UJeXl6Ob69l93P8888/NWbMGNWvX19eXl7y9vZWUFCQHn74YR09ejRdXbvdrvfff18tWrRQuXLl5OXlpebNm+v999+X3W7PFFN++zsAAEBudenSRQ0aNJDdbtf27dslSV9++aUGDx6sevXqOcaot912m9566y2lpKRkOkbasdpbb72lJk2ayNPT0/Fg/cqVK3r33XfVq1cvBQQEyMPDQxUrVlSXLl301VdfZRlX6pj84sWLGj16tPz9/eXp6amwsDDH2gup6zLUrVtXZcqUUZ06dfTee+9le665uSdIHceeOHFCJ06cSPeqpYzjwNTxv7+/vzw8PFS1alUNGjRIBw8ezPM1Kiyp7URHR6eLPzAw0DHe3bhxo6T0rwZLld2aFSkpKfrggw90xx13qEKFCvL09FRQUJCGDRumw4cPO+rdaM2K/FyvvNy/StJvv/2mZ555xtEnfH191bJlS02fPt1xHv7+/vL29tbFixezPMbIkSNlGIa++OKL7C4zABQLzKwAUOKNHTtWa9euVd++fdW9e3dt3LhRH330kQ4dOqRNmzalq/vqq6/qwIEDatOmjXr37q3ExET98MMPmjZtmjZu3KgNGzbI3d35vxovXLigO+64Q6VLl9bAgQOVlJSk8PBwDRs2TDabTY8++mi6+tOmTdPkyZNVqVIl9e7dW1WqVNGuXbs0Z84cff311/rxxx8zvdonOTlZnTt3VmxsrHr06KFy5co5Hlp//vnnGjRokDw8PHTfffepWrVq+vHHH9W6dWuFhobm6Tw6d+6syMhINWvWTI899pjsdrvWrl2rQYMGae/evZoxY4aj/rhx4/Taa6+pVq1auu+++1ShQgWdPn1a27dvV3h4uB544IEc25w/f74k6aWXXpLNduMcvIeHh+P/Y2Nj1aZNGx04cEAtW7bUgAEDdO7cOS1dulQ9evTQu+++qxEjRmQ6xvbt2/Xqq6+qQ4cOGjZsmHbv3q3ly5drz549WrFihdq2batGjRrp4Ycf1smTJ/XFF1+oa9euOnbsmMqVK5fpeKNHj9b333+v++67T3fffbfWrl2rt956S5s3b9aWLVtUpkwZR9389t85c+Zo3bp16tu3rzp37pztjZB0PdnUpk0bRUdHq1u3burbt69M09SJEye0atUq3XfffapTp46j/qBBg/T555+rZs2aGjZsmAzD0IoVK/T000/r+++/zzLplNf+DgAAkFemaab7PG7cONlsNt1+++2qUaOGLly4oPXr12v06NH6+eeftXjx4iyP88wzz2jLli3q3bu3evXqJTc3N0lSTEyMRo0apTZt2qhbt27y8/PT6dOntXLlSvXp00cffvihnnjiiUzHu3r1qrp166aYmBjdfffdunLlipYsWaJ77rlH3377rebOnaudO3fqzjvvlIeHh8LDwzVy5EhVrlxZ999/f7pj5faeIDAwUJMnT3asAfHss886jhEWFub4/2+++UYDBgzQtWvX1KdPHwUFBem3337T8uXL9dVXX2njxo267bbbcn2NCku/fv0UGBiYKX4fHx9H/IsWLdKJEyeyfb1wRleuXFHv3r21bt06+fv768EHH1T58uV1/Phxx5i+bt26NzxGfq9XXu5fd+zYoR49eigmJkYdOnTQgAEDdOnSJe3bt09TpkzRSy+9JDc3Nz3++OOaPHmylixZoscffzzdMS5fvqxPP/1Ut9xyi+66665cXR8AsIwJABaSZEoyJ0+enOnPv/71L9M0TTM6OtqUZD7yyCPp9n3kkUdMSWbNmjXNEydOOMqvXr1qtmvXzpRkbtu2Ld0+R48eNe12e6Y4xo8fb0oylyxZkq588uTJpiRz48aNuTqf1PodOnTI8pwmT55sbt26NctrMHToUPPatWuO8r1795pubm5mgwYN0tXfsGGDKcm84447zAsXLqTb9q9//cuUZI4aNSpdeUBAgCnJ7NKli3nx4sV02+Lj400fHx+zdOnSZlRUVLptL7zwgiO+6OhoR3lOP5M5c+akK09MTDR79OhhGoZh7ty501FesWJFs3r16pliMk3TPHv2bKayjE6cOGFKMt3d3c3ExMQc66f1+OOPm5LM4cOHpys/cOCAWb58ebNUqVLmsWPHHOUbN250XItPP/003T6PPfaYKcmsUKGCOWPGjHTbXn75ZVOS+dZbb6UrT71WlSpVMo8fP+4oT0lJMQcMGGBKMqdNm5Zun/z237Jly6a77qmy+jmuXLkyyz5kmqaZnJxsxsfHOz5/9tlnpiSzefPm6X6GFy9eNG+77bYsr1Ve+zsAAEB2UscVGW3YsMG02WymYRiOMeyRI0cy1UtJSTEffPBBU1KmMXrqWK169erpxoSpkpKSzFOnTmUqj4mJMRs2bGhWrFjRvHz5crptqWPyPn36mElJSY7y77//3jGWbN68uRkbG+vYFh0dbZYqVcoMCwvLdI75uScICAjIFHNq3D4+PmblypXN/fv3p9u2Z88e08vLK1MMOV2jG+nQoYNjHJrdfdPp06dzHX/q8bKSen+WVur4uW/fvul+FqZ5/Wd75swZx+es7gkLcr1ye/+anJxsBgYGmpLMxYsXZzqvkydPOv7/jz/+MEuVKmU2a9YsU70FCxaYkswJEyZkcXUAoHghWQHAUqk3GFn9SR1Q5vRg/KOPPsp03IULF5qSzHfeeSdXcZw7d86UZD766KPpyvObrLjRnzfffDPTNShbtmy6h8Cp2rdvb0pKt61fv36mJHPv3r1ZxhAWFmb6+fmlK0u9MYqMjMxU/5NPPsny3E3TNBMSEkwfH59cJSvOnTtnurm5mS1atMgyrqioKFOS+dxzzznKfH19zcDAwEw3CLn1008/mZLMqlWr5mm/5ORk09PT0yxXrpwZExOTafuECRNMSebUqVMdZanJinbt2mWqv2nTJlOSGRgYmO4BvGn+f0JlyJAh6cpT+2/GhIRpXk9K2Gw2MzAwMFfnk1P/zSrxYJpZ/xz/+9//mpLM8ePH59huly5dTEnmd999l2nbt99+a0oyO3XqlK48r/0dAAAgOxm/+DRhwgRz4MCBpru7uynJHD16dI7H2LFjR6Zxn2n+/1gt49g9N+bMmWNKMjdt2pSuPHVMfvTo0Uz71KpVy5Rkrl+/PtO2Tp06mW5ubunGmfm9J8juYf9bb71lSjLfe++9LLc/++yzpiRzz549jrKCXKPU5MKN/mS8dymsZMW1a9fMChUqmJ6enubvv/+eY6xZ3RMW5Hrl9v41PDzclGTeddddOcZomqZ57733mpLMX375JV15y5YtTZvNlu5+DgCKK14DBaBYMDNM086L5s2bZyrz9/eXdP1VP2ldunRJc+fO1YoVK3To0CElJCSka/v333/PdxxpTZ48OU8LbNerV0/ly5fPVJ56HhcuXHBs37p1q0qVKqWlS5dmeawrV67o7NmzOn/+vCpVquQo9/DwyPKVTpGRkZKktm3bZtpWrlw5hYWFZfl+1oy2b9/ueN9vVud+9epVSdff65rqwQcf1DvvvKPGjRvrvvvuU/v27dW6detMr7DKTurPLu07aXPj4MGDSkxMVNu2bbNc46Jr16565ZVXtHPnzkzbmjVrlqksdSG/sLCwTNPeU7f99ttvWcbSoUOHTGW1a9eWv7+/jh8/rgsXLsjHx0dS/vvv7bffnmV5dvHUqFFDs2bNUmRkpHr16qU2bdpkeW6RkZGy2WxZnkOnTp3k5uaW5TXMS38HAADIydSpUyVdHxP6+Piobdu2Gjp0qAYPHuyoc/78ec2ePVtff/21jh07pkuXLqU7Rn7GUXv37tXs2bP1/fff6/Tp00pKSsrxmD4+Pqpdu3am8urVqys6OjrbsWZKSor+/PNP1ahRQ1L+7wmys3XrVklSVFRUlmP5Q4cOSbo+lm/cuHG6bXkZa2a0cePGQl/jIicHDhxQXFycbr/99iwX5M6Nglyv3N6/btu2TZJ055135iqmESNGaNmyZfrwww/14YcfOuL7+eefdeeddzpeAQwAxRnJCgAlXlYPtlPf2592sbyrV6+qc+fO+vnnnxUcHKz7779ffn5+KlWqlKTrNznJycnOCTqD7B7OZ3Ue58+f17Vr1xw3Zdm5ePFiuhuTqlWrZvlQPy4uzrE9K9mVZ3T+/HlJ15MWqQsZZhdXqjfffFN16tTRwoULNXPmTM2cOVPu7u7q3bu33njjjSxv5NJKvbk4d+6ckpKS0q3vcCOp53zLLbdkub1atWrp6qV1o/52o22pyZqMsru+t9xyi06cOKG4uDj5+PgUqP9md55Z8fb21rZt2zR58mT997//1TfffCNJ8vPz09NPP62JEyc6zikuLk6+vr6OGDKed+XKlXXmzJlM2/LS3wEAAHKS0xefLly4oBYtWig6OlotW7bUww8/LF9fX7m7u+vChQuaO3dunsdR27ZtU+fOnXXt2jV16dJFd911l7y9vWWz2RQVFaWVK1dmecycxkG5HU/m954gO6lj+dT14G50vIzyMtYsDlLXb0tN/ORHQa5Xbu9f8xpnx44d1bBhQy1evFivv/66ypUr50haPPXUU7k6BgBYjWQFAJexcuVK/fzzz3rkkUe0aNGidNtOnz6d40C/uKhQoYLsdrtiYmLytF92sw+8vb0lSX/99VeW27Mrzyou6fqC0W+88Uau9nFzc9OoUaM0atQonTlzRlu2bNF//vMfLVu2TPv27dOePXtUunTpbPf39/dXzZo1dfLkSX3//ffq3r17nmL9888/s9x++vTpdPWK0l9//aX69etnKk+NLTWGgvTfvM48ufXWW7VgwQKZpql9+/Zpw4YNeu+99zRlyhTZ7XZHWxUqVFBMTIyuXr2aKWFx7do1nTt3ztG/AAAArPLRRx8pOjo6y9nPW7du1dy5c7PdN7tx1IwZM5SYmJjlzICZM2dq5cqVBQ37hvJ7T3Cj40nSr7/+qpCQkDztm9exptVSZy0XZFZ9Qa5XbuUnzuHDh+uZZ57R4sWL9eCDD+qzzz5TjRo11Lt37yKJEQAKm83qAADAWY4cOSJJuueeezJt27Rpk7PDybdWrVopNjZWe/fuLZTjNW3aVJK0ZcuWTNsuXryoqKioXB2nZcuWstls2rx5c77iqFKligYMGKClS5eqc+fOOnz4sPbs2ZPjfk888YSk6zeMdrv9hnVTv91Wv359lS1bVlFRUZleFSZdn44uSbfddlteTyPPsup7x44d06lTpxQYGOi4SbGi/xqGocaNG+vvf/+7vvvuO0nSihUrHNubNm0qu92u77//PtO+33//vVJSUpxyDQEAAG6kKMZRR44cka+vb5avMHLGvUV+7gnc3NyyncHaqlUrScr3WL4kadCggXx8fLRr1y7Hl5TyyhnXK7WNtWvX5nqfRx55RF5eXvrwww+1ePFiJSQkaNiwYZle5woAxRXJCgAuI/UdnakPolMdO3ZML7zwggUR5c/o0aMlSY8//rj++OOPTNsvXbrkeL9pbtx9992qUKGCPvvsM/3666/pts2YMcMx/TgnVapU0YMPPqgdO3Zo+vTpunbtWqY6R48eVXR0tKTriYP169dnmrZ/9epVxzfEcvNap9GjRys0NFSbN2/Www8/nGW8Fy9e1LRp0zRnzhxJUunSpfXggw/q4sWLmjRpUqYY3377bZUqVUoPPfRQrs69IObOnasTJ044Ptvtdj3//POy2+169NFHHeXO6r979uzR8ePHM5WnzrBJ+zN57LHHJEnjx4/X5cuXHeWXL1/WuHHjJElDhw4ttNgAAADyI7txVGRkpGbOnJnvY8bExGjXrl3pyhcsWJCnh8v5lZ97gkqVKuns2bOZ1taQpEcffVQ+Pj6aOnWqfv7550zb7XZ7rtaxKwnc3Nw0YsQIJSYmasSIEbpy5Uq67anrfdyIM65X3759FRgYqC+//DLLtUmymnHh7e2tBx98UDt37tTkyZPl5uamYcOGFSgOAHAmXgMFwGX07dtXQUFBevPNN7Vnzx41bdpUJ0+e1OrVq9W7d2+dPHmy0NqKiIjIdoFtHx8fPfvss/k+dpcuXTRr1iyNHz9edevWVa9evVSrVi1dvHhRJ06c0KZNm9S2bVvHWgM58fb21vvvv6/BgwerTZs2uu+++1StWjX9+OOP+vXXX9WhQwdt2rRJNlvO+e13331Xhw8f1qRJk/TJJ5+obdu2qlq1qv744w/t379f27dv15IlS1SrVi0lJiaqa9euCgwM1O23366AgAAlJSXpu+++0/79+9WnTx81atQoxzbLli2rb775RgMHDtRnn32mVatWqVu3bgoKCpLdbteRI0e0fv16xcfH691333XsN2vWLG3evFnvvvuutm/frk6dOuncuXNaunSpEhIS9O6776pWrVq5uoYF0bZtW4WFhen+++9XhQoVtHbtWv36669q1qyZxo4d66jnrP67bt06jRkzRm3atFGDBg1UpUoV/fbbb1q5cqUMw9Dzzz/vqDto0CCtXLlSS5cuVePGjdWvXz8ZhqEvv/xS0dHRuu+++/Tggw8WSlwAAAD59fDDD2v27NkaPXq0IiIiVLduXR0+fFirV6/WgAED9Pnnn+f5mM8++6zWrl2rtm3b6r777lOFChW0Y8cObdmyRQMHDlR4eHgRnMn/y889QZcuXbR9+3bdeeedateunUqXLq3Q0FD17dtXlSpVUnh4uPr3769WrVqpS5cuaty4sWw2m06ePKmtW7fq/PnzWSY6CmLRokXZPtQPCwtTv379CrW9VJMnT9ZPP/2kL7/8UvXq1VPv3r1Vvnx5nTp1St9++61mz56tIUOGZLu/M65X6dKltWzZMnXv3l3333+/PvjgA7Vs2VKJiYnav3+/NmzYkOUXxEaMGKF58+bp9OnTuuuuu3TrrbfmOwYAcDaSFQBchpeXlzZs2KBx48YpIiJCmzdvVu3atfXSSy9pzJgx+bpJyc6mTZuynf4dEBBQoGSFJL3wwgu644479Pbbb2vLli1auXKlKlSooBo1auiJJ57QoEGD8nS8QYMGqWLFipo+fbo+//xzeXh4qH379tq6dauee+45Sblbv8Hb21ubNm3SvHnztHjxYn3xxRdKSkpS1apVVbduXb355pvq1q2bpOs/j1dffVUbN27Ujz/+qC+//FLly5dXnTp19M9//tPxrf3cuOWWW/T999/riy++0JIlS7Rt2zatXr1aNptNNWvW1L333qvHHntMbdq0cezj6+urrVu3aubMmVq+fLneeOMNeXp6qmXLlnr++edzvf5FQb3xxhtasWKF5s+fr+PHj6tSpUoaNWqUpk2blm4Wg7P6b48ePfTss8/q+++/18qVKxUfH69q1aqpW7dujiRGWkuWLFGHDh20cOFCxwJ+DRs21D/+8Q8NHz68UGICAAAoiOrVq2vz5s0aN26ctmzZorVr16pBgwZ6//331bVr13yNo3r27KlVq1ZpxowZ+vzzz+Xm5qaWLVtq48aNOnbsWJEnK6S83xO8+OKLunDhglatWqXNmzcrJSVFjzzyiPr27SvpejJj165dmjNnjtauXavNmzerdOnSql69ujp37pzla7QK6uOPP8522yOPPFJkyYrSpUvrm2++0QcffKB///vf+vjjj2WapqpXr67+/furbdu2OR7DGderefPmioqK0qxZs7RmzRpt2bJF5cuXV1BQULZr1oWGhqpp06aKjIxkYW0AJY5hZnz/BgAA/5OSkqLatWvrypUr+X6fK7I2ZMgQffzxx4qOjna8mgAAAAAACiI+Pl41atRQpUqVdOzYsVzNkAeA4oLfWAAAXbhwId2aA5JkmqZmzJihkydPFsm3qAAAAAAAhev999/XxYsXNWLECBIVAEocXgOVCxcvXtSLL76opUuXKiYmRg0aNNC4ceP0wAMPWB0aABSKbdu26f7771f37t0VGBioixcvatu2bYqKilJAQIAmT55sdYgAAAAAgCzExcXpnXfe0e+//66FCxeqRo0aGjFihNVhAUCekazIhQEDBmj79u2aNWuW6tWrp8WLF+tvf/ub7HZ7nt8LDwDFUf369XXXXXfpxx9/1Jo1a3T16lX5+/tr1KhRmjBhgvz8/KwOEQAAAACQhdjYWL300ksqU6aMWrZsqXfeeUflypWzOiwAyDPWrMjB119/rd69ezsSFKm6d++uvXv36uTJk3Jzc7MwQgAAAAAAAAAASjZeXpeDFStWqFy5crr33nvTlT/66KP6448/9NNPP1kUGQAAAAAAAAAANweSFTnYs2ePGjZsKHf39G/MCgkJcWwHAAAAAAAAAAD5x5oVOTh//rxq166dqdzX19exPSvJyclKTk52fLbb7YqJiVGlSpVkGEbRBAsAAICbjmmaSkhIUPXq1WWz8V0jq9ntdv3xxx8qX74843oAAADkGuP6nJGsyIUb3YRkt23mzJmaOnVqUYUEAAAAF3Pq1CndeuutVofh8v744w/5+/tbHQYAAABKKMb12SNZkYNKlSplOXsiJiZG0v/PsMho/PjxGjNmjONzXFycatasqRMnTsjb27togs1G2NRvndpecWGTqbo+pg5fMGSX633rLWpyd0vbP3R7K0vbt4pps+lSUJC8jhyRYbdbHY7T1ftpm2Vt/zF1q2VtW8mUqXifq/K+UEqGC/6uqz65taXtN9i829L2rWIzTTW8lqT97mVkd7Fvlh9o18TpbcbHxysgIEDly5d3etvILPXncOrUKaeP6/PLbrfr7Nmz8vPz41t8oD8gE/oE0qI/ICP6ROGJj4+Xv78/4/obIFmRgyZNmmjJkiW6du1aunUrdu++/nAiODg4y/08PDzk4eGRqdzHx8f5NzUeXs5tr9gw5V7GlDwMyQUf4Pn4+FjafnkXe3iVyjQM2dzd5WUYLvlqCCv73UWPcpa1bSVTpswyV+Xt4ZrJCqt/16mciw4yTVNu19wld0/JxX7XWdHnUm8KXfHfleIo9efg7e1dopIVSUlJ8vb25iED6A/IhD6BtOgPyIg+UfgY12ePHpaD/v376+LFi/riiy/SlX/88ceqXr26br/9dosiAwAAAAAAAADg5sDMihzceeed6tatm4YPH674+HgFBQVpyZIl+uabb/Tpp5/Kzc3N6hABAAAAAAAAACjRSFbkwvLlyzVx4kRNmjRJMTExatCggZYsWaIHHnjA6tAAAAAAAAAAACjxSFbkQrly5TR37lzNnTvX6lAAAAAAAAAAALjpsGYFAAAAAMAlBAYGqkGDBgoLC1OjRo303nvv5Vh/z549OR734sWLevbZZxUUFKQmTZooNDRUgwcPVnR0tKPOL7/8op49e6p27doKDg5W69at9eWXX0qShgwZIg8PDx07dsxR/7nnntOUKVMkSRERETIMQzNmzHBs37NnjwIDA28Y16JFi+Tj46OmTZuqYcOGCg0N1bRp05SYmJjjOd3IpUuXdPvttys0NFShoaHq2bOnjh8/XqBjAgAAkKwAAAAAALiM8PBwRUVFae3atZo4caJ27dpVoOOZpqlevXopMTFRu3fv1u7duxUZGamuXbvq8OHDkqS9e/eqR48eevrpp3Xs2DHt2bNH4eHhiouLcxynevXqmjhxYrbtVKtWTXPnztW5c+fyFF/Xrl0VGRmp/fv3a926dYqKitJTTz2Vv5P9H09PT61bt06//vqrfv31V/Xs2VNjxowp0DEBAABIVgAAAAAAXI6/v7/q1aun/fv3q3///mrSpImCg4M1b968THW3b9+uhg0byjRNR1nr1q21Zs0arV+/XtHR0Xr33Xfl6ekpSbLZbBoyZIi6d+8uSZo1a5Yee+wx9e3b17F/jRo19Mgjjzg+Dx8+XJs3b9bOnTuzjLd69ep68MEHNX369Hyfs5+fnxYsWKDNmzdr7969jnPr3Lmzmjdvrttuu01ffPGFo/67776runXrqnnz5nrppZdUuXJlx/mVL19e0vVkTXx8vGw2Hi8AAICCYTQBAAAAAHA5u3fv1oEDB7R06VI1aNBAu3fv1oYNGzR9+nT9/PPP6eq2aNFCvr6+Wr9+vSRp586dOnfunHr27KmdO3fqtttuU6lSpbJt65dfflHr1q1vGE/ZsmU1adIkvfDCC9nWefHFF7V48eJ0r5fKq4oVK6pWrVrau3evLly4oCeffFKfffaZduzYoW+//VZjxozRn3/+qV27dmnmzJn64YcftGPHDiUkJGQ6VteuXXXLLbdo6dKlevvtt/MdEwAAgESyAgAAAADgQgYOHKiwsDA9+eSTWrhwoSIiIvT0009LkqpUqaIBAwY4khJpjRo1yrHGxTvvvKMRI0bIMAxJcvxXkjZv3qywsDAFBQVp2rRpeYpt6NChOnXqlL777rsst1euXFmjRo3Siy++mKfjZpQ6Q+THH3/UsWPHdOeddyosLExdu3aVaZo6ePCgIiIi1KtXL1WpUkWS9Oijj2Y6zrp163T69Gndf//96dbTAAAAyA+SFQAAAAAAl5G6ZsWPP/6ogQMHSkqfbMjqsyQNGDBAUVFRioyM1KpVqxwP75s2baqdO3fq6tWrkqR27dopKipKgwcPVnx8vCSpWbNm2rp1a46xubm56ZVXXtG4cePSvXIqrTFjxigiIkKRkZG5P+k0YmNjFR0dreDgYJmmqZCQEEVFRTn+nDx5Uh06dJBpmlleh4xsNpsef/xxffLJJ/mKBwAAIBXJCgAAAABAiWZPTtHVs5d19a9LSkm4kqd9u3bt6lin4uzZs1qxYoU6d+6cqZ67u7uefPJJ3XXXXbrnnnvk4+Pj2N/f31+jRo1SUlKSo/6lS5cc/z927FgtXLhQX331laPst99+y3J9jAEDBqh06dJavnx5lvGWLVtWL730kiZNmpSn80w9v6FDh6p9+/Zq1KiR2rRpo8OHD2vDhg2OOlFRUbpy5Yo6duyor7/+2rGg98cff+yo89dffykmJsbx+T//+Y9CQkLyHA8AAEBa7lYHAAAAAABAflw5laCLW//Q5V/PSin/PxOhdEB5lWtTQ56NK8lwv/F39N5++2099dRTCgkJkd1u18SJE9WyZcss6w4dOlQTJkzQyJEjHWWGYWjNmjV68cUX1bhxY5UtW1bly5dXnTp1NGHCBElSkyZNtGbNGk2cOFF///vf5eXlJW9v72zXp3jttdfUvn37bGMeNmyY3nzzTSUnJ9/w3KTrr2pq2rSpEhMT5eHhoX79+mnIkCGSrq9fsWrVKj3//PMaPXq0rl69qpo1a+rLL79UaGioxo4dq1atWqlatWrq3LmzKlSoIOl6ouXxxx/XtWvXZJqm6tSpo08//TTHWAAAAG7EMLObW4pCFR8frwoVKiguLk7e3t5ObTtw3Fc5V7oJ2WSqYUVT+2MN2ZXz9OWbzfFZvS1tf3+Dhpa2bxXTZtOl+vXldfCgDLvd6nCcruGB/Za1/du4zZa1bSVTpuIqXlWF2FIyXPB33a2z2lna/i0boyxt3yo201TwtUTtcfeUPRevCLmZ/NkpzOltWjmORGYl8edht9t15swZValSRTbbzTG53kyxK3bFEV3e8ZdkMyR7httaQ5Ipuft5qvLQJnL38SiUdpcuXaoPP/wwyzUtSoq89IeEhASVL19ekjRlyhQdOXKEpMRN6Gb8HYH8oz8gI/pE4SmJ40hnY2YFAAAAAKDEME1TMcsOKTHq7PWCjIkKSfpf0bXziTr7z19VZWSY3MqXLlC7PXv21KFDh7RixYoCHackGTdunH744QdduXJFtWrV0vz5860OCQAA3MRIVgAAAAAASozLO8/8f6IiJ3YpJSFZscsPq/IjjQvU7jfffFOg/YvKmTNn1L1790zl3bp10+zZswt07Pfee69A+wMAAOQFyQoAAAAAQIlxccvvjtc85YpdStofo2sxSXL3LVOUoVmiSpUqioqKsjoMAACAAuNFYwAAAACAEuHKqQRdPX0p94mKVIZ06ec/iyQmAAAAFA6SFQAAAACAEuHKqYT87WhKV07GF24wAAAAKFQkKwAAAAAAJYL9Skq+72LtSdcKNxgAAAAUKpIVAAAAAIASwebhJtnzua9nqcINBgAAAIWKZAUAAAAAoETwqF0hfzsakkedfO4LAAAApyBZAQAAAAAoEUpV9VLpQG/JyOOOhuTV4pYiiQkAAACFg2QFAAAAAKDEKN+uhmTmYQdD8gz1k1v50kUWEwAAAAqOZAUAAAAAoMTwbFxZ5drWyF1lm+Repawq3h1UtEEBAACgwNytDgAAAAAAgLyo0LuWDA83JWw4eb0g40wLmyHZTZUO8FblhxrJVoZbXwAAgOKOERsAAAAAoEQxDEMVugXIq3lVXfr5T1366bTsl69d32gz5BlcSeVaV1fpQG8ZRl4XuAAAAIAVSFYAAAAAAEok94plVKFHoLy7B8i8YpdS7DLKuMuwkaAAAAAoaUhWAAAAAABKNMMwZHi4SXKzOhQAAADkEwtsAwAAAAAAAAAAS5GsAAAAAAAAAAAAliJZAQAAAAAAAAAALEWyAgAAAAAAAAAAWIpkBQAAAAAAAAAAsBTJCgAAAAAAAAAAYCmSFQAAAAAAAAAAwFIkKwAAAAAAAAAAgKVIVgAAAAAAAAAAAEuRrAAAAABQrEVERMgwjCz/bNu2LVfHOHPmjIYMGaLKlSurbNmyat26tdavX1/EkQMAAADILXerAwAAAACA3HjllVfUqVOndGXBwcE57pecnKwuXbrowoULmjt3rqpUqaL33ntPPXv21Lp169ShQ4eiChkAAABALpGsAAAAAFAi1K1bV61atcrzfgsWLNCePXv0448/qnXr1pKkTp06KTQ0VGPHjtVPP/1U2KECAAAAyCNeAwUAAADgprZixQrVr1/fkaiQJHd3dw0ePFg///yzfv/9dwujAwAAACCRrAAAAABQQjz99NNyd3eXt7e3evTooS1btuRqvz179igkJCRTeWrZ3r17CzVOAAAAAHnHa6AAAAAAFGsVKlTQqFGj1LFjR1WqVElHjhzR7Nmz1bFjR3311Vfq0aPHDfc/f/68fH19M5Wnlp0/fz7bfZOTk5WcnOz4HB8fL0my2+2y2+35OR2ns9vtMk2zxMSLokV/QEb0CaRFf0BG9InCwzXMGckKAAAAAE4TERGRaZHs7ERGRiosLExNmzZV06ZNHeXt2rVT//791aRJE40dOzbHZIUkGYaRr20zZ87U1KlTM5WfPXtWSUlJObZbHNjtdsXFxck0TdlsTK53dfQHZESfQFr0B2REnyg8CQkJVodQ7JGsAAAAAOA09evX1/z583NVt2bNmtlu8/HxUZ8+ffTBBx8oMTFRnp6e2datVKlSlrMnYmJiJCnLWRepxo8frzFjxjg+x8fHy9/fX35+fvL29s7NaVjObrfLMAz5+fnxkAH0B2RCn0Ba9AdkRJ8oPGXKlLE6hGKPZAUAAAAAp6lWrZqGDRtWKMcyTVPSjWdGSFKTJk20e/fuTOWpZcHBwdnu6+HhIQ8Pj0zlNputRN2wG4ZR4mJG0aE/ICP6BNKiPyAj+kTh4PrljCsEAAAAoMSJjY3V6tWrFRYWluO31Pr3768DBw7op59+cpRdu3ZNn376qW6//XZVr169qMMFAAAAkANmVgAAAAAo1gYNGqSaNWuqefPmqly5sg4fPqzXX39df/31lxYtWpSu7tChQ/Xxxx/r6NGjCggIkCQ99thjeu+993Tvvfdq1qxZqlKlit5//30dPHhQ69ats+CMAAAAAGREsgIAAABAsRYSEqLPP/9cH3zwgS5evChfX1+1bdtWn3zyiVq0aJGubkpKilJSUhyviJKuv8pp/fr1Gjt2rP7+97/r8uXLCgsL05o1a9ShQwdnnw4AAACALJCsAAAAAFCsjRs3TuPGjctV3UWLFmWabSFJVatW1ccff1zIkQEAAAAoLKxZAQAAAAAAAAAALEWyAgAAAAAAAAAAWIpkBQAAAAAAAAAAsBTJCgAAAAAAAAAAYCmSFQAAAAAAAAAAwFIkKwAAAAAAAAAAgKVIVgAAAAAAAAAAAEuRrAAAAAAAAAAAAJYiWQEAAAAAAAAAACxFsgIAAAAAAAAAAFiKZAUAAAAAAAAAALAUyQoAAAAAAAAAAGApkhUAAAAAAAAAAMBSJCsAAAAAAAAAAIClSFYAAAAAAAAAAABLkawAAAAAAAAAAACWIlkBAAAAAAAAAAAsRbICAAAAAAAAAABYimQFAAAAAAAAAACwFMkKAAAAAAAAAABgKZIVAAAAAAAAAADAUiQrAAAAAAAAAACApUhWAAAAAAAAAAAAS5GsAAAAAAAAAAAAliJZAQAAAAAAAAAALEWyAgAAAAAAAAAAWIpkBQAAAAAAAAAAsBTJCgAAAAAAAAAAYCmSFQAAAAAAAAAAwFIkKwAAAAAAAAAAgKXcrQ4AAIDCcOusdlaHYAm73a5SZ86oSpUqstn4DgIAAAAAACiZeKoBAAAAAAAAAAAsRbICAAAAAAAAAABYimQFAAAAAAAAAACwFMkKAAAAAAAAAABgKZIVAAAAAAAAAADAUiQrAAAAAAAAAACApUhWAAAAAAAAAAAAS5GsAAAAAAAAAAAAliJZAQAAAAAAAAAALEWyAgAAAAAAAAAAWIpkBQAAAAAAAAAAsBTJCgAAAAAAAAAAYCmSFQAAAAAAAAAAwFIkKwAAAAAAAAAAgKVIVuQgIiJChmFk+Wfbtm1WhwcAAAAAAAAAQInnbnUAJcUrr7yiTp06pSsLDg62KBoAAAAAAAAAAG4ezKzIpbp166pVq1bp/pQrV87qsAAAAICb3pAhQ7Kd7ZybGc+LFi3Kdt8///zTSWcBAAAA4EaYWQEAAACgWHvppZf01FNPZSrv27evPDw81KJFi1wd51//+pcaNGiQrqxSpUqFEiMAAACAgiFZkUtPP/20HnjgAZUtW1atW7fWSy+9pLZt21odFgAAAHDTq1OnjurUqZOubNOmTTp37pxefPFFubm55eo4wcHBat68eVGECAAAAKCASFbkoEKFCho1apQ6duyoSpUq6ciRI5o9e7Y6duyor776Sj169Mhyv+TkZCUnJzs+x8fHS5LsdrvsdrtTYk9lk+nU9ooLm0wZMl32XWfO7mcZmTbXvPKmzSbTMFz2/K3ud67IbrfLNE2uvUVspov+G2uaMkzTJc/fir9r/P3O2oIFC2QYhh577DGrQwEAAABQCFwqWREREZFpkezsREZGKiwsTE2bNlXTpk0d5e3atVP//v3VpEkTjR07NttkxcyZMzV16tRM5WfPnlVSUlL+TiCfGlZ0vQcJ0vUFWW4tJxmS7C6YsDlz5oyl7V+qX9/S9q1iGoaSa9SQJBku+BDP6n7niux2u+Li4mSapmwumiSzUvC1RKtDsIQhUwH2K1KKZMqwOhynsuL3XEJCgtPbLO7i4uIUHh6uLl26qFatWrner0+fPjp79qwqVKigjh07atq0aQoODi7CSAEAAADklkslK+rXr6/58+fnqm7NmjWz3ebj46M+ffrogw8+UGJiojw9PTPVGT9+vMaMGeP4HB8fL39/f/n5+cnb2zvvwRfA/ljXeoiQyiZTpqQDsZLdxR6kSFKVKlUsbT/m4EFL27dK6owKr0OHZLjgN2Gt7neuyG63yzAM+fn5kaywwB7301aHYAmbaUqmtNfNU3bDtf6NteL3XJkyZZzeZnG3ZMkSJSYmaujQobmqf8stt2jixIlq1aqVvL29tXv3bs2aNUutWrXSDz/8oNDQ0Gz3LU4zpvOLWXhIi/6AjOgTSIv+gIzoE4WHa5gzl0pWVKtWTcOGDSuUY5n/+8a0kc0NuoeHhzw8PDKV22w2pz9McsUH9alMXT9/V7wGVj+0dMUH9akM05Rht7vkNbC637kqwzAs+fcFcrkH9WmZhiH7//64Eiv+nt1sf7fzM9s5owULFqhSpUrq379/ro7Ts2dP9ezZ0/G5ffv26t27t5o0aaJJkyZp5cqV2e5bnGZM5xez8JAW/QEZ0SeQFv0BGdEnCg8zpnPmUsmKwhIbG6vVq1crLCyMb7oBAAAAeVDQ2c67du3Sjh07NGrUqCy/HJRbgYGBatu2rbZt23bDesVpxnR+MQsPadEfkBF9AmnRH5ARfaLw8Bw5ZyQrcjBo0CDVrFlTzZs3V+XKlXX48GG9/vrr+uuvv7Ro0SKrwwMAAABKlILOdl6wYIEkFcqM6dx8Q7A4zZguCGbhIS36AzKiTyAt+gMyok8UDq5fzkhW5CAkJESff/65PvjgA128eFG+vr5q27atPvnkE7Vo0cLq8AAAAACXkZycrE8//VQtW7Ys8MLY0dHR+uGHH9S1a9dCig4AAABAQZCsyMG4ceM0btw4q8MAAAAAXN6XX36pmJiYG86qGDp0qD7++GMdPXpUAQEBkqSuXbuqffv2CgkJcSyw/dprr8kwDE2fPt1Z4QMAAAC4AZIVAAAAAEqEBQsWyMvLSw888EC2dVJSUpSSkiLTNB1lTZo00eeff645c+YoMTFRVapUUefOnfXSSy+pXr16zggdAAAAQA5IVgAAAAAoEb799tsc6yxatCjT2nJvvvlmEUUEAAAAoLCwqgcAAAAAAAAAALAUyQoAAAAAAAAAAGApkhUAAAAAAAAAAMBSJCsAAAAAAAAAAIClSFYAAAAAAAAAAABLkawAAAAAAAAAAACWIlkBAAAAAAAAAAAsRbICAAAAAAAAAABYimQFAAAAAAAAAACwFMkKAAAAAAAAAABgKZIVAAAAAAAAAADAUiQrAAAAAAAAAACApUhWAAAAAAAAAAAAS5GsAAAAAAAAAAAAliJZAQAAAAAAAAAALEWyAgAAAAAAAAAAWIpkBQAAAAAAAAAAsBTJCgAAAAAAAAAAYCmSFQAAAAAAAAAAwFIkKwAAAAAAAAAAgKVIVgAAAAAAAAAAAEuRrAAAAAAAAAAAAJYiWQEAAAAAAAAAACxFsgIAAAAAAAAAAFiKZAUAAAAAAAAAALAUyQoAAAAAAAAAAGApkhUAAAAAAAAAAMBSJCsAAAAAAAAAAIClSFYAAAAAAAAAAABLkawAAAAAAAAAAACWIlkBAAAAAAAAAAAsRbICAAAAAAAAAABYimQFAAAAAAAAAACwFMkKAAAAAAAAAABgKZIVAAAAAAAAAADAUiQrAAAAAAAAAACApUhWAAAAAAAAAAAAS5GsAAAAAAAAAAAAliJZAQAAAAAAAAAALEWyAgAAAAAAAAAAWIpkBQAAAAAAAAAAsBTJCgAAAAAAAAAAYCmSFQAAAAAAAAAAwFIkKwAAAAAAAAAAgKVIVgAAAAAAAAAAAEuRrAAAAAAAAAAAAJYiWQEAAAAAAAAAACxFsgIAAAAAAAAAAFiKZAUAAAAASyQkJGjs2LHq3r27/Pz8ZBiGpkyZkm39nTt3qmvXripXrpx8fHw0YMAAHTt2LNftrVu3Tq1bt1bZsmVVuXJlDRkyRGfOnCmEMwEAAABQUCQrAAAAAFji/PnzmjdvnpKTk9WvX78b1j1w4IA6duyoK1euaOnSpVq4cKEOHTqkdu3a6ezZszm2tWnTJt15552qWrWqVq5cqblz52rdunXq0qWLkpOTC+mMAAAAAOSXu9UBAAAAAHBNAQEBio2NlWEYOnfunD766KNs606aNEkeHh5avXq1vL29JUnNmjVT3bp1NWfOHL366qs3bOv5559XvXr1FB4eLnf367dBtWrV0h133KGFCxdq+PDhhXdiAAAAAPKMmRUAAAAALGEYhgzDyLHetWvXtHr1at1zzz2ORIV0PdnRqVMnrVix4ob7//7779q+fbseeughR6JCktq0aaN69erluD8AAACAokeyAgAAAECxdvToUSUmJiokJCTTtpCQEB05ckRJSUnZ7r9nzx5H3az2T90OAAAAwDq8BgoAAABAsXb+/HlJkq+vb6Ztvr6+Mk1TsbGxqlatWr72T92eleTk5HRrWsTHx0uS7Ha77HZ77k/CQna7XaZplph4UbToD8iIPoG06A/IiD5ReLiGOSNZAQAAAKDAIiIi1KlTp1zVjYyMVFhYWJ7buNEro3LzOqns6txo35kzZ2rq1KmZys+ePXvD2RzFid1uV1xcnEzTlM3G5HpXR39ARvQJpEV/QEb0icKTkJBgdQjFHskKAAAAAAVWv359zZ8/P1d1a9asmadjV6pUSZKynAERExMjwzDk4+OT7/2zmnGRavz48RozZozjc3x8vPz9/eXn55du/YzizG63yzAM+fn58ZAB9AdkQp9AWvQHZESfKDxlypSxOoRij2QFAAAAgAKrVq2ahg0bViTHrlOnjjw9PbV79+5M23bv3q2goKAb3vwFBwc76vbq1SvT/qnbs+Lh4SEPD49M5TabrUTdsBuGUeJiRtGhPyAj+gTSoj8gI/pE4eD65YwrBAAAAKBYc3d3V9++fbV8+fJ00+dPnjypjRs3asCAATfcv0aNGmrZsqU+/fRTpaSkOMq3bdumgwcP5rg/AAAAgKJHsgIAAACAZdasWaPw8HCtWrVKkrRv3z6Fh4crPDxcly9fdtSbOnWqLl++rD59+mjNmjVasWKFevfurcqVK+sf//hHumO6u7urS5cu6cpeffVVHThwQPfee6/WrVunxYsX67777lNwcLAeffTRoj9RAAAAADfEa6AAAAAAWGb48OE6ceKE4/OyZcu0bNkySVJ0dLQCAwMlSQ0aNFBERIReeOEFDRw4UO7u7urcubPmzJkjPz+/dMdMSUlJN4NCkjp27Kivv/5akyZNUt++fVW2bFn16dNHs2fPzvI1TwAAAACci2QFAAAAAMscP34813WbNWumdevW5VjPNM0sy7t166Zu3brluj0AAAAAzsNroAAAAAAAAAAAgKVIVgAAAAAAAAAAAEuRrAAAAAAAAAAAAJYiWQEAAAAAAAAAACxFsgIAAAAAAAAAAFiKZAUAAAAAAAAAALAUyQoAAAAAAAAAAGApkhUAAAAAAAAAAMBSJCsAAAAAAAAAAIClSFYAAAAAAAAAAABLOT1Z8cUXX8hutzu7WQAAAAD/w5gcAAAAQHHj9GTFvffeq4CAAL388ss6c+aMs5sHAAAAXB5jcgAAAADFjdOTFREREWrdurWmTp2qmjVr6qGHHtK2bducHQYAAADgshiTAwAAAChunJ6saN++vZYuXaoTJ05o7NixWr9+ve644w41a9ZMixYtUnJysrNDAgAAAFwKY3IAAAAAxY1lC2xXq1ZN06ZN08mTJ/Xpp5/KZrNp6NChuvXWWzV+/HidPn3aqtAAAAAAl8CYHAAAAEBxYVmyIlV0dLR++uknHT58WG5ubmrSpInmzp2revXqadWqVVaHBwAAANz0GJMDAAAAsJolyQrTNPXf//5XPXr0UMOGDbV48WKNHDlSx48f14YNG3T8+HF17NhRo0ePtiI8AAAA4KbHmBwAAABAceLu7AZfffVVffDBBzpx4oRCQ0M1f/58DRo0SB4eHo46VapU0fPPP69OnTo5OzwAAADgpseYHAAAAEBx4/RkxYsvvqi77rpLixYtUocOHbKtV6dOHU2aNMmJkQEAAACugTE5AAAAgOLG6cmKI0eOKCAgIMd6NWrU0OTJk50QEQAAAOBaGJMDAAAAKG6cvmZFbm6KAAAAABQdxuQAAAAAihunz6x47LHHst1ms9nk4+OjFi1aqH///ipdurQTIwMAAABcA2NyAAAAAMWN05MVGzduVFxcnC5cuCB3d3dVqlRJ58+f17Vr1+Tj4yPTNPXGG2+ofv36ioiIUNWqVZ0dIgAAAHBTY0wOAAAAoLhx+mugvvjiC5UvX15LlixRYmKiTp8+rcTERC1evFjly5fX2rVrtWXLFsXGxmrChAnODg8AAAC46TEmBwAAAFDcOH1mxZgxY/Tcc8/p/vvvd5S5ubnpgQce0F9//aUxY8Zoy5YteuGFFzRnzhxnhwcAAADc9BiTAwAAAChunD6zYvv27WrUqFGW24KDgxUZGSlJCgsL07lz55wZGgAAAOASGJMDAAAAKG6cnqzw9vbWxo0bs9y2YcMGeXt7S5ISExNVvnx5Z4YGAAAAuATG5AAAAACKG6cnKwYNGqRXX31VEydOVFRUlE6fPq2oqCiNHz9es2fP1uDBgyVJv/zyixo2bFgkMSQkJGjs2LHq3r27/Pz8ZBiGpkyZkm39nTt3qmvXripXrpx8fHw0YMAAHTt2rEhiAwAAAIpacRiTAwAAAEBaTl+zYubMmTp9+rRmzpypWbNmOcpN09Tf/vY3vfLKK5Kk1q1bq0ePHkUSw/nz5zVv3jyFhoaqX79++uijj7Kte+DAAXXs2FFhYWFaunSpkpKSNGnSJLVr105RUVHy8/MrkhgBAACAolIcxuQAAAAAkJbTkxWlS5fW4sWL9dJLL2nTpk06f/68KlWqpPbt26d7b27Xrl2LLIaAgADFxsbKMAydO3fuhsmKSZMmycPDQ6tXr3ZMh2/WrJnq1q2rOXPm6NVXXy2yOAEAAICiUBzG5AAAAACQllOTFYmJiQoKCtIHH3ygvn37Wjal3DCMXNW7du2aVq9erYcfftiRqJCuJzs6deqkFStWkKwAAABAiVJcxuQAAAAAkJZT16zw9PRUYmKivLy8nNlsvh09elSJiYkKCQnJtC0kJERHjhxRUlKSBZEBAAAA+VPSxuQAAAAAXIPTXwPVpUsXrVu3Tp07d3Z203l2/vx5SZKvr2+mbb6+vjJNU7GxsapWrVqm7cnJyUpOTnZ8jo+PlyTZ7XbZ7fYiijhrNplOba+4sMmUIdP5q8gXE87uZxmZNte88qbNJtMwXPb8re53rshut8s0Ta69RWymi/4ba5oyTNMlz9+Kv2tF0WZJGpMDAAAAcA1OT1ZMmDBB99xzj8qUKaMBAwaoWrVqmV7LlFVyIDsRERHq1KlTrupGRkYqLCwsL+FKuvFro7LbNnPmTE2dOjVT+dmzZ50+G6NhRdd7kCBdnzZ0aznJkGR3wYTNmTNnLG3/Uv36lrZvFdMwlFyjhiTJcMGHeFb3O1dkt9sVFxcn0zRlc9EkmZWCryVaHYIlDJkKsF+RUiRTuXu95s3Cit9zCQkJhX7Mwh6TAwAAAEBBOT1Z0axZM0nSlClTsnyYL0kpKSm5Pl79+vU1f/78XNWtWbNmro8rSZUqVZL0/zMs0oqJiZFhGPLx8cly3/Hjx2vMmDGOz/Hx8fL395efn1+69S+cYX+saz1ESGWTKVPSgVjJ7mIPUiSpSpUqlrYfc/Cgpe1bJXVGhdehQzJc8JvuVvc7V2S322UYhvz8/EhWWGCP+2mrQ7CEzTQlU9rr5il7LtcCu1lY8XuuTJkyhX7Mwh6TAwAAAEBBOT1ZMWnSpFwvcJ0b1apV07BhwwrteGnVqVNHnp6e2r17d6Ztu3fvVlBQULY3jx4eHvLw8MhUbrPZnP4wyRUf1Kcydf38XfEaWP3Q0hUf1KcyTFOG3e6S18DqfueqDMOw5N8XyOUe1KdlGobs//vjSqz4e1YUbRb2mBwAAAAACsrpyYopU6Y4u8l8c3d3V9++fbV8+XK99tprKl++vCTp5MmT2rhxo0aPHm1xhAAAAEDelaQxOQAAAADX4PRkRVqJiYmKiYlR1apV5e7u3FDWrFmjS5cuOd4BvG/fPoWHh0uSevXqpbJly0qSpk6dqhYtWqhPnz4aN26ckpKSNGnSJFWuXFn/+Mc/nBozAAAAUNisHJMDAAAAQCpL3hexceNGtW7dWuXLl1dAQIB27dolSXr66ae1fPlyp8QwfPhw3XvvvXrsscckScuWLdO9996re++9N93CiQ0aNFBERIRKlSqlgQMHasiQIQoKCtL3338vPz8/p8QKAAAAFLbiMCYHAAAAgFROT1Zs2LBB3bt3V1JSkp577jnZ07zTvXLlylq0aJFT4jh+/LhM08zyT2BgYLq6zZo107p163Tp0iXFxcVpxYoVqlOnjlPiBAAAAApbcRmTAwAAAEAqpycrJk2apF69eikyMlIzZsxIty00NFRRUVHODgkAAABwKYzJAQAAABQ3Tn8pbWRkpJYtWyZJMgwj3TY/P790r2ACAAAAUPgYkwMAAAAobpw+s8Ld3V1Xr17NctuZM2dUvnx5J0cEAAAAuBbG5AAAAACKG6cnK1q0aKFPPvkky23h4eFq3bq1kyMCAAAAXAtjcgAAAADFjdNfAzVu3Dj16NFD/fv318MPPyzDMPTTTz9p4cKFCg8P18aNG50dEgAAAOBSGJMDAAAAKG6cnqzo2rWrPv74Yz377LNauXKlJOnpp5+Wj4+PFi1apLZt2zo7JAAAAMClMCYHAAAAUNw4PVkhSYMHD9Y999yjH3/8UX/99ZcqV66sO+64Q15eXlaEAwAAALgcxuQAAAAAihNLkhWS5OnpqS5duljVPAAAAODyGJMDAAAAKC4sSVaYpqnt27frxIkTSkxMzLT94YcftiAqAAAAwHUUhzF5QkKCpk+frqioKEVGRurcuXOaPHmypkyZkq5eSkqK5s6dq2+//VZ79uxRTEyMAgICdPfdd2vcuHHy8fHJsa2OHTtq06ZNmcp79Oihb775ppDOCAAAAEB+OT1ZcejQId111106fPiwTNPMtN0wDJIVAAAAQBEqLmPy8+fPa968eQoNDVW/fv300UcfZVkvMTFRU6ZM0d/+9jcNGzZMlStX1s6dOzVjxgytWrVKO3bskKenZ47t1a5dW5999lm6stwkOgAAAAAUPacnK55++mklJSXp888/V0hIiDw8PJwdAgAAAODSisuYPCAgQLGxsTIMQ+fOncs2WeHp6ano6GhVqlTJUdaxY0fVrFlT9957r7744gsNHjw4x/Y8PT3VqlWrQosfAAAAQOFxerLi559/1vz58zVw4EBnNw0AAABAxWdMbhhGruq5ubmlS1SkatmypSTp1KlThRoXAAAAAOezObvBcuXKydvb29nNAgAAAPifm2VMvmHDBklS48aNc1X/6NGj8vX1lbu7u+rUqaOJEydmuV4HAAAAAOdz+syKRx99VIsXL1bPnj2d3TQAAAAA3Rxj8t9//13jxo1T8+bN1adPnxzrt23bVvfff78aNGigxMRErVmzRq+99pq2bNmijRs3ymbL+ntcycnJSk5OdnyOj4+XJNntdtnt9sI5mSJmt9tlmmaJiRdFi/6AjOgTSIv+gIzoE4WHa5gzpycrgoODtWTJEt11113q27dvltO5BwwY4OywAAAAAJdRFGPyiIgIderUKVd1IyMjFRYWlqfjpxUTE6NevXrJNE19/vnn2SYa0poxY0a6z7169VJgYKCee+45rVy5Uv37989yv5kzZ2rq1KmZys+ePaukpKT8nYCT2e12xcXFyTTNXF0r3NzoD8iIPoG06A/IiD5ReBISEqwOodhzerJi0KBBkqTo6GitXr0603bDMJSSkuLssAAAAACXURRj8vr162v+/Pm5qluzZs08HTut2NhYdevWTb///rs2bNig2rVr5/tYgwcP1nPPPadt27Zlm6wYP368xowZ4/gcHx8vf39/+fn5lZhXadntdhmGIT8/Px4ygP6ATOgTSIv+gIzoE4WnTJkyVodQ7Dk9WbFx40ZnNwkAAAAgjaIYk1erVk3Dhg0r9OOmFRsbq65duyo6Olrr169XSEhIoRz3RjfeHh4e8vDwyHKfknTDbhhGiYsZRYf+gIzoE0iL/oCM6BOFg+uXM6cnKzp06ODsJgEAAACkURLH5KmJimPHjum7775T06ZNC3zMjz/+WJLUqlWrAh8LAAAAQME4PVlxI1evXtWpU6cKNJUbAAAAQP45e0y+Zs0aXbp0yfEO33379ik8PFzS9XUlypYtq8TERPXo0UORkZF66623dO3aNW3bts1xDD8/P9WpU8fx2d3dXR06dND69eslSZs3b9bLL7+s/v37q3bt2kpKStKaNWs0b948de7cWX379nXKuQIAAADInlOSFW5ubtq6datatmwpSTJNUz169ND777+voKAgR72dO3eqTZs2rFkBAAAAFLLiOiYfPny4Tpw44fi8bNkyLVu2TNL1NTUCAwP1119/afv27ZKkUaNGZTrGI488okWLFjk+p6SkpIu/WrVqcnNz0/Tp03Xu3DkZhqG6detq2rRp+sc//sGUfAAAAKAYcEqywjTNdJ/tdrvWrVun+Ph4ZzQPAAAAuLziOiY/fvx4jnUCAwMzxX8jGesGBQXpq6++ymtoAAAAAJyIrxABAAAAAAAAAABLkawAAAAAAAAAAACWIlkBAAAAAAAAAAAs5bRkhWEYuSoDAAAAUDQYkwMAAAAorpyywLYkderUSTZb+txIu3bt0pXZ7XZnhQMAAAC4HMbkAAAAAIorpyQrHnnkEWc0AwAAACAbjMkBAAAAFGdOSVb861//ckYzAAAAALLBmBwAAABAccYC2wAAAAAAAAAAwFIkKwAAAAAAAAAAgKVIVgAAAAAAAAAAAEuRrAAAAAAAAAAAAJYiWQEAAAAAAAAAACxFsgIAAAAAAAAAAFiKZAUAAAAAAAAAALCUuzMaqVWrlgzDyFVdwzB09OjRIo4IAAAAcC2MyQEAAAAUZ05JVnTo0CHXN0YAAAAACh9jcgAAAADFmVOSFYsWLXJGMwAAAACywZgcAAAAQHHGmhUAAAAAAAAAAMBSTplZkZW4uDgdOnRIiYmJmba1b9/egogAAAAA18KYHAAAAEBx4fRkxbVr1/TUU0/p3//+t1JSUrKsk105AAAAgIJjTA4AAACguHH6a6DefPNNrVq1SgsXLpRpmnr33Xf14Ycfqnnz5qpbt67WrFnj7JAAAAAAl8KYHAAAAEBx4/RkxSeffKKJEyfqb3/7myTp9ttv17Bhw/TTTz8pICBAGzdudHZIAAAAgEthTA4AAACguHF6suLYsWMKDQ2VzXa96aSkJMe2p556Sp999pmzQwIAAABcCmNyAAAAAMWN05MVXl5eunLligzDkK+vr06cOOHY5unpqfPnzzs7JAAAAMClMCYHAAAAUNw4PVnRoEEDRUdHS5LatGmjN954Q7/99pvOnDmj1157TfXr13d2SAAAAIBLYUwOAAAAoLhxd3aD999/vw4dOiRJmjp1qtq3b6+AgABJUqlSpbR8+XJnhwQAAAC4FMbkAAAAAIobpycrRowY4fj/pk2bat++fVqxYoVsNpu6devGt7gAAACAIsaYHAAAAEBx4/RkRUb+/v565plnrA4DAAAAcFmMyQEAAABYzdJkxdmzZ5WYmJipvGbNmhZEAwAAALgexuQAAAAAigOnJysSEhI0evRoLVmyRElJSVnWSUlJcXJUAAAAgOtgTA4AAACguHF6suLZZ5/V4sWLNXToUIWEhMjDw8PZIQAAAAAujTE5AAAAgOLG6cmKr776SrNmzdKoUaOc3TQAAAAAMSYHAAAAUPzYnN1gUlKSmjRp4uxmAQAAAPwPY3IAAAAAxY3TkxW9evXS5s2bnd0sAAAAgP9hTA4AAACguHH6a6BefPFFDRw4UOXLl1ffvn1VqVKlTHV8fX2dHRYAAADgMhiTAwAAAChunJ6sCA4OliQ9//zzev7557Osk5KS4syQAAAAAJfCmBwAAABAceP0ZMWkSZNkGIazmwUAAADwP4zJAQAAABQ3Tk9WTJkyxdlNAgAAAEiDMTkAAACA4sbpC2wDAAAAAAAAAACk5ZSZFf/+97/Vu3dvVapUSf/+979zrP/www87ISoAAADAdTAmBwAAAFCcOSVZMWTIEG3btk2VKlXSkCFDbljXMAxujAAAAIBCxpgcAAAAQHHmlGRFdHS0qlev7vh/AAAAAM7FmBwAAABAceaUZMV7772nZ555RrfeeqsCAgIkSXa7XTYbS2YAAAAAzsCYHAAAAEBx5pQ7k9dff11//PGH43NKSopKlSqlnTt3OqN5AAAAwOUxJgcAAABQnDklWWGaZq7KAAAAABQNxuQAAAAAijPmfAMAAAAAAAAAAEuRrAAAAAAAAAAAAJZyygLbknTw4EG5u19vLiUlRZJ04MCBLOvedtttzgoLAAAAcBnFbUyekJCg6dOnKyoqSpGRkTp37pwmT56sKVOmZKo7ZMgQffzxx5nK69evn+05ZLRu3Tq99NJL+vXXX1W2bFn16dNHr732mqpUqVLQUwEAAABQQE5LVgwZMiRT2UMPPZTus2maMgzDceMEAAAAoPAUtzH5+fPnNW/ePIWGhqpfv3766KOPbljf09NTGzZsyFSWG5s2bdKdd96p3r17a+XKlTpz5oxeeOEFdenSRTt27JCHh0e+zwMAAABAwTklWfGvf/3LGc0AAAAAyEZxHJMHBAQoNjZWhmHo3LlzOSYrbDabWrVqla+2nn/+edWrV0/h4eGO2SW1atXSHXfcoYULF2r48OH5Oi4AAACAwuGUZMUjjzzijGYAAAAAZKM4jskNw3BKO7///ru2b9+umTNnOhIVktSmTRvVq1dPK1asIFkBAAAAWIwFtgEAAACUCImJibrlllvk5uamW2+9VSNHjlRMTEyO++3Zs0eSFBISkmlbSEiIYzsAAAAA6zhtzQoAAAAAyK/Q0FCFhoYqODhY0vU1KN58802tX79e27dvV7ly5bLd9/z585IkX1/fTNt8fX0d27OSnJys5ORkx+f4+HhJkt1ul91uz9e5OJvdbpdpmiUmXhQt+gMyok8gLfoDMqJPFB6uYc5IVgAAAAAosIiICHXq1ClXdSMjIxUWFpan448ePTrd527duqlp06YaOHCg5s+fn2l7VrJ77dSNXkc1c+ZMTZ06NVP52bNnlZSUlGObxYHdbldcXJxM05TNxuR6V0d/QEb0CaRFf0BG9InCk5CQYHUIxR7JCgAAAAAFVr9+fc2fPz9XdWvWrFkobfbv319eXl7atm3bDetVqlRJkrKcQRETE5PljItU48eP15gxYxyf4+Pj5e/vLz8/P3l7e+czcuey2+0yDEN+fn48ZAD9AZnQJ5AW/QEZ0ScKT5kyZawOodgjWQEAAACgwKpVq6Zhw4Y5vd3cfMsv9dVRu3fvVq9evdJt2717t2N7Vjw8POTh4ZGp3GazlagbdsMwSlzMKDr0B2REn0Ba9AdkRJ8oHFy/nHGFAAAAAJRI4eHhunz5slq1anXDejVq1FDLli316aefKiUlxVG+bds2HTx4UAMGDCjqUAEAAADkgJkVAAAAACyzZs0aXbp0yfEO33379ik8PFyS1KtXL5UtW1YnTpzQoEGD9MADDygoKEiGYWjTpk1666231Lhx40wzOtzd3dWhQwetX7/eUfbqq6+qW7duuvfeezVixAidOXNG48aNU3BwsB599FHnnTAAAACALJGsAAAAAGCZ4cOH68SJE47Py5Yt07JlyyRJ0dHRCgwMlLe3t6pWrao33nhDf/31l1JSUhQQEKBnnnlGEyZMkJeXV7pjpqSkpJtBIUkdO3bU119/rUmTJqlv374qW7as+vTpo9mzZ2f5micAAAAAzkWyAgAAAIBljh8/nmOdihUravny5bk+pmmaWZZ369ZN3bp1y/VxAAAAADgPa1YAAAAAAAAAAABLkawAAAAAAAAAAACWIlkBAAAAAAAAAAAsRbICAAAAAAAAAABYimQFAAAAAAAAAACwFMkKAAAAAAAAAABgKZIVAAAAAAAAAADAUiQrAAAAAAAAAACApUhWAAAAAAAAAAAAS5GsAAAAAAAAAAAAlnLJZEVCQoLGjh2r7t27y8/PT4ZhaMqUKVnWHTJkiAzDyPSnQYMGzg0aAAAAAAAAAICblLvVAVjh/PnzmjdvnkJDQ9WvXz999NFHN6zv6empDRs2ZCoDAAAAAAAAAAAF55LJioCAAMXGxsowDJ07dy7HZIXNZlOrVq2cFB0AAAAAAAAAAK7FJZMVhmFYHQIAAAAAAAAAAPgfl1yzIq8SExN1yy23yM3NTbfeeqtGjhypmJgYq8MCAAAAAAAAAOCm4JIzK/IiNDRUoaGhCg4OliRt2rRJb775ptavX6/t27erXLlyWe6XnJys5ORkx+f4+HhJkt1ul91uL/rA07DJdGp7xYVNpgyZLpuRc3Y/y8i0ueaVN202mYbhsudvdb9zRXa7XaZpcu0tYjNd9N9Y05Rhmi55/lb8XePvNwAAAABXUOKTFREREerUqVOu6kZGRiosLCxPxx89enS6z926dVPTpk01cOBAzZ8/P9P2VDNnztTUqVMzlZ89e1ZJSUl5iqGgGlZ0vQcJ0vVpQ7eWkwxJdhdM2Jw5c8bS9i/Vr29p+1YxDUPJNWpIkgwXfIhndb9zRXa7XXFxcTJNUzYXTZJZKfhaotUhWMKQqQD7FSlFMuVar9e04vdcQkKC09sEAAAAAGcr8cmK+vXra/78+bmqW7NmzUJps3///vLy8tK2bduyrTN+/HiNGTPG8Tk+Pl7+/v7y8/OTt7d3ocSRW/tjXeshQiqbTJmSDsRKdhd7kCJJVapUsbT9mIMHLW3fKqkzKrwOHZLhgt+EtbrfuSK73S7DMOTn50eywgJ73E9bHYIlbKYpmdJeN0/ZXWwtMCt+z5UpU8bpbQIAAACAs5X4ZEW1atU0bNgwp7eb0zdYPTw85OHhkancZrM5/WGSKz6oT2Xq+vm74jWw+qGlKz6oT2WYpgy73SWvgdX9zlUZhmHJvy+Qyz2oT8s0DNn/98eVWPH3jL/bAAAAAFwBdz75EB4ersuXL6tVq1ZWhwIAAAAAAAAAQIlX4mdW5NeaNWt06dIlxzuA9+3bp/DwcElSr169VLZsWZ04cUKDBg3SAw88oKCgIBmGoU2bNumtt95S48aNLZnRAQAAAAAAAADAzcZlkxXDhw/XiRMnHJ+XLVumZcuWSZKio6MVGBgob29vVa1aVW+88Yb++usvpaSkKCAgQM8884wmTJggLy8vq8IHAAAAAAAAAOCm4bLJiuPHj+dYp2LFilq+fHnRBwMAAAAAAAAAgAtjzQoAAAAAAAAAAGApkhUAAAAAAAAAAMBSJCsAAAAAAAAAAIClSFYAAAAAAAAAAABLkawAAAAAAAAAAACWIlkBAAAAAAAAAAAsRbICAAAAAAAAAABYimQFAAAAAAAAAACwFMkKAAAAAAAAAABgKZIVAAAAAAAAAADAUiQrAAAAAAAAAACApUhWAAAAAAAAAAAAS5GsAAAAAAAAAAAAliJZAQAAAAAAAAAALEWyAgAAAAAAAAAAWIpkBQAAAAAAAAAAsBTJCgAAAAAAAAAAYCmSFQAAAAAAAAAAwFIkKwAAAAAAAAAAgKVIVgAAAAAAAAAAAEuRrAAAAAAAAAAAAJYiWQEAAAAAAAAAACxFsgIAAAAAAAAAAFiKZAUAAAAAAAAAALAUyQoAAAAAAAAAAGApkhUAAAAALJGQkKCxY8eqe/fu8vPzk2EYmjJlSpZ1DcPI9k+DBg1ybKtjx45Z7tuzZ89CPisAAAAA+eFudQAAAAAAXNP58+c1b948hYaGql+/fvroo4+yrbt169ZMZT/99JOeffZZ9e/fP1ft1a5dW5999lm6Mh8fnzzFDAAAAKBokKwAAAAAYImAgADFxsbKMAydO3fuhsmKVq1aZSr78MMPZRiGhg4dmqv2PD09szwOAAAAAOuRrAAAAABgCcMw8r1vQkKCli1bpg4dOigoKKgQowIAAABgBdasAAAAAFDi/Oc//9GlS5c0bNiwXO9z9OhR+fr6yt3dXXXq1NHEiROVmJhYhFECAAAAyC1mVgAAAAAocRYsWCAfHx/dc889uarftm1b3X///WrQoIESExO1Zs0avfbaa9qyZYs2btwomy3r73ElJycrOTnZ8Tk+Pl6SZLfbZbfbC34iTmC322WaZomJF0WL/oCM6BNIi/6AjOgThYdrmDOSFQAAAAAKLCIiQp06dcpV3cjISIWFheW7rb179+qnn37S008/rTJlyuRqnxkzZqT73KtXLwUGBuq5557TypUrs12ke+bMmZo6dWqm8rNnzyopKSnvwVvAbrcrLi5Opmlmm5SB66A/ICP6BNKiPyAj+kThSUhIsDqEYo9kBQAAAIACq1+/vubPn5+rujVr1ixQWwsWLJCkPL0CKiuDBw/Wc889p23btmWbrBg/frzGjBnj+BwfHy9/f3/5+fnJ29u7QO07i91ul2EY8vPz4yED6A/IhD6BtOgPyIg+UXhy+yUbV0ayAgAAAECBVatWrcDJg9y4cuWKPvnkEzVr1qxAszPSutGNt4eHhzw8PLLcpyTdsBuGUeJiRtGhPyAj+gTSoj8gI/pE4eD65YwrBAAAAKDE+O9//6tz585p6NChBT7Wxx9/LElq1apVgY8FAAAAoGCYWQEAAADAMmvWrNGlS5cc7/Ddt2+fwsPDJV1fV6Js2bLp6i9YsECenp4aNGhQtsd0d3dXhw4dtH79eknS5s2b9fLLL6t///6qXbu2kpKStGbNGs2bN0+dO3dW3759i+jsAAAAAOQWyQoAAAAAlhk+fLhOnDjh+Lxs2TItW7ZMkhQdHa3AwEDHtlOnTunbb7/V4MGDVaFChWyPmZKSopSUFMfnatWqyc3NTdOnT9e5c+dkGIbq1q2radOm6R//+AdT8gEAAIBigGQFAAAAAMscP34813X9/f3TJSGyY5pmus9BQUH66quv8hoaAAAAACfiK0QAAAAAAAAAAMBSJCsAAAAAAAAAAIClSFYAAAAAAAAAAABLkawAAAAAAAAAAACWIlkBAAAAAAAAAAAsRbICAAAAAAAAAABYimQFAAAAAAAAAACwFMkKAAAAAAAAAABgKZIVAAAAAAAAAADAUiQrAAAAAAAAAACApUhWAAAAAAAAAAAAS5GsAAAAAAAAAAAAliJZAQAAAAAAAAAALEWyAgAAAAAAAAAAWIpkBQAAAAAAAAAAsBTJCgAAAAAAAAAAYCmSFQAAAAAAAAAAwFIkKwAAAAAAAAAAgKVIVgAAAAAAAAAAAEuRrAAAAAAAAAAAAJYiWQEAAAAAAAAAACxFsgIAAAAAAAAAAFiKZAUAAAAAAAAAALAUyQoAAAAAAAAAAGApkhUAAAAAAAAAAMBSJCsAAAAAAAAAAIClSFYAAAAAAAAAAABLkawAAAAAAAAAAACWIlkBAAAAAAAAAAAsRbICAAAAAAAAAABYimQFAAAAAAAAAACwFMkKAAAAAAAAAABgKZIVAAAAAAAAAADAUiQrAAAAAAAAAACApUhWAAAAAAAAAAAAS5GsAAAAAAAAAAAAliJZAQAAAAAAAAAALEWyAgAAAAAAAAAAWIpkBQAAAAAAAAAAsBTJCgAAAAAAAAAAYCmSFQAAAAAAAAAAwFIkKwAAAAAAAAAAgKVIVgAAAAAAAAAAAEuRrAAAAAAAAAAAAJYiWQEAAAAAAAAAACxFsgIAAAAAAAAAAFiKZAUAAAAAAAAAALAUyQoAAAAAltiwYYMee+wxNWjQQF5eXqpRo4buvvtu/fLLL1nW37lzp7p27apy5crJx8dHAwYM0LFjx3Ld3rp169S6dWuVLVtWlStX1pAhQ3TmzJnCOh0AAAAABUCyAgAAAIAl/vnPf+r48eMaNWqUvv76a82dO1dnzpxRq1attGHDhnR1Dxw4oI4dO+rKlStaunSpFi5cqEOHDqldu3Y6e/Zsjm1t2rRJd955p6pWraqVK1dq7ty5Wrdunbp06aLk5OSiOkUAAAAAueRudQAAAAAAXNN7772nKlWqpCvr2bOngoKC9Morr6hz586O8kmTJsnDw0OrV6+Wt7e3JKlZs2aqW7eu5syZo1dfffWGbT3//POqV6+ewsPD5e5+/TaoVq1auuOOO7Rw4UINHz68kM8OAAAAQF4wswIAAACAJTImKiSpXLlyatSokU6dOuUou3btmlavXq177rnHkaiQpICAAHXq1EkrVqy4YTu///67tm/froceesiRqJCkNm3aqF69ejnuDwAAAKDokawAAAAAUGzExcVp586daty4saPs6NGjSkxMVEhISKb6ISEhOnLkiJKSkrI95p49exx1s9o/dTsAAAAA67jka6A2bNigTz/9VD/++KNOnTolHx8fNW/eXJMmTVKzZs0y1d+5c6fGjh2rbdu2yd3dXZ07d9acOXNUu3ZtC6IHAAAAbl5PP/20Ll26pIkTJzrKzp8/L0ny9fXNVN/X11emaSo2NlbVqlXL8pg57Z+6PSvJycnp1rSIj4+XJNntdtnt9lyckfXsdrtM0ywx8aJo0R+QEX0CadEfkBF9ovBwDXPmksmKf/7znzp//rxGjRqlRo0a6ezZs3r99dfVqlUrrV27Nt27cVMX8gsLC9PSpUuVlJSkSZMmqV27doqKipKfn5+FZwIAAAAUDxEREerUqVOu6kZGRiosLCxT+UsvvaTPPvtM77zzTpZfIjIMI9tj3mhbTnVutO/MmTM1derUTOVnz5694WyO4sRutysuLk6macpmY3K9q6M/ICP6BNKiPyAj+kThSUhIsDqEYs8lkxXOXMgPAAAAcAX169fX/Pnzc1W3Zs2amcqmTp2qGTNm6OWXX9bIkSPTbatUqZIkZTkDIiYmRoZhyMfHJ9v2cto/qxkXqcaPH68xY8Y4PsfHx8vf319+fn7p1s8ozux2uwzDkJ+fHw8ZQH9AJvQJpEV/QEb0icJTpkwZq0Mo9lwyWZHXhfwefvjhbBfyI1kBAAAASNWqVdOwYcPyte/UqVM1ZcoUTZkyRRMmTMi0vU6dOvL09NTu3bszbdu9e7eCgoJuePMXHBzsqNurV69M+6duz4qHh4c8PDwyldtsthJ1w24YRomLGUWH/oCM6BNIi/6AjOgThYPrlzOu0P8UxUJ+AAAAAG5s+vTpmjJlil588UVNnjw5yzru7u7q27evli9fnm76/MmTJ7Vx40YNGDDghm3UqFFDLVu21KeffqqUlBRH+bZt23Tw4MEc9wcAAABQ9FxyZkVWCnshv+K0EJ9NplPbKy5sMmXIdNmMnNWL9pgumi02bTaZhuGy5291v3NFLHZmLZvpov/GmqYM03TJ87fi79rN/Pf79ddf16RJk9SzZ0/17t1b27ZtS7e9VatWjv+fOnWqWrRooT59+mjcuHGOteQqV66sf/zjH+n2c3d3V4cOHbR+/XpH2auvvqpu3brp3nvv1YgRI3TmzBmNGzdOwcHBevTRR4v2RAEAAADkqMQnK4rrQn7FaSG+hhVd70GCdH3a0K3lJEOS3QUTNmfOnLG0/Uv161vavlVMw1ByjRqSJMMFH+JZ3e9cEYudWSv4WqLVIVjCkKkA+xUpRTKV86LGNxMrfs/dzAvxrVq1SpL0zTff6Jtvvsm03Uzzb2mDBg0UERGhF154QQMHDpS7u7s6d+6sOXPmyM/PL91+KSkp6WZQSFLHjh319ddfa9KkSerbt6/Kli2rPn36aPbs2Vm+5gkAAACAc5X4ZEVxXcivOC3Etz/WtR4ipLLJlCnpQKxkd7EHKVLWa7M4U8zBg5a2b5XUGRVehw7JuIm/CZsdq/udK2KxM2vtcT9tdQiWsJmmZEp73Txlv8GXOm5GVvyeu5kX4ouIiMhT/WbNmmndunU51jOz+cJAt27d1K1btzy1CQAAAMA5Snyyorgu5FecFuJzxQf1qUxdP39XvAZWP7R0xQf1qQzTlGG3u+Q1sLrfuSoWO7OOqz2oT8s0DNn/98eVWPH3jL/bAAAAAFyBy975OGMhPwAAAAAAAAAAkLMSP7MiP4pqIT8AAAAAAAAAAJB3LpmsKKqF/AAAAAAAAAAAQN65ZLKiqBbyAwAAAAAAAAAAeeeya1YAAAAAAAAAAIDigWQFAAAAAAAAAACwFMkKAAAAAAAAAABgKZIVAAAAAAAAAADAUiQrAAAAAAAAAACApUhWAAAAAAAAAAAAS5GsAAAAAAAAAAAAliJZAQAAAAAAAAAALEWyAgAAAAAAAAAAWIpkBQAAAAAAAAAAsBTJCgAAAAAAAAAAYCmSFQAAAAAAACg0gYGBatCggcLCwtSoUSO99957Odbfs2dPjse9ePGinn32WQUFBalJkyYKDQ3V4MGDFR0d7ajzyy+/qGfPnqpdu7aCg4PVunVrffnll5KkIUOGyMPDQ8eOHXPUf+655zRlyhRJUkREhAzD0IwZMxzb9+zZo8DAwBvGFRcXp8GDBys4OFghISEKDg7W4sWLJUlRUVFaunTpDfc3DEMXL17M8fwBOM/Vq1c1depUNWjQQI0bN1bTpk3Vr18/RUVFSZKOHj2qe++9V7Vq1VKTJk1022236aOPPpIkTZkyRYZhaPPmzY7jvfvuuxo+fLgk6cSJEzIMQ8OGDXNsv3jxogzDuGFMERERKlu2rMLCwhQWFqbGjRtr/vz5hXzm1y1atEgDBw6UJO3YsUMPPvhgkbSTEckKAAAAALgJuNrDwdy0e+uttyosLEwNGzbUk08+qatXr+Z4TACFIzw8XFFRUVq7dq0mTpyoXbt2Feh4pmmqV69eSkxM1O7du7V7925FRkaqa9euOnz4sCRp79696tGjh55++mkdO3ZMe/bsUXh4uOLi4hzHqV69uiZOnJhtO9WqVdPcuXN17ty5XMf24osvqmrVqtq9e7d27dqlrVu3qkWLFpJyl6wAUPw8+uijioyM1NatW7V3715FRkZq6NCh2rt3r/7880+1bdtW3bt3V3R0tHbv3q1169bp2rVrjv0DAwP1wgsvZHv8cuXKafXq1dq3b1+e4mrUqJGioqIUFRWl7777TiNHjlRCQkK+zzM3mjdvrs8++6xI20hFsgIAAAAAbhKu9HAwN+2OGzdOUVFRioyM1K5du/TBBx/k4yoAKAh/f3/Vq1dP+/fvV//+/dWkSRMFBwdr3rx5mepu375dDRs2lGmajrLWrVtrzZo1Wr9+vaKjo/Xuu+/K09NTkmSz2TRkyBB1795dkjRr1iw99thj6tu3r2P/GjVq6JFHHnF8Hj58uDZv3qydO3dmGW/16tX14IMPavr06bk+x5MnT6pGjRqOb0WXL19edevW1ZkzZzRp0iStW7dOYWFheuqppyRJy5cvV4MGDdS6des8tQPAOQ4fPqwVK1Zo4cKFqlixoqO8b9++evDBB/Xee++pXbt2evzxxx3bfH19HX/HJWnAgAFKSkrSihUrsmzDw8ND48aN0/jx4/Md54ULF+Tl5aVSpUpJkt544w21aNFCTZs2VcuWLfXTTz9JkhITE3X//ferUaNGCg0NdfzOlKRPPvlEt99+u2677TZ16NAhyy+yREREqHnz5pKk48ePq3Llypo0aZKaNWumoKAgff31146627dvV+fOndW8eXPddttt+uKLL/J0TiQrAAAAAOAmk/bh4IABA9SpUyeFhITcVA8Hc9NuqjJlyqhdu3Y6ePBgro8PoHDs3r1bBw4c0NKlS9WgQQPt3r1bGzZs0PTp0/Xzzz+nq9uiRQv5+vpq/fr1kqSdO3fq3Llz6tmzp3bu3KnbbrvN8VAuK7/88otat259w3jKli2rSZMm3fAbzy+++KIWL16cbgbZjTz77LN6+eWX1bx5c40cOVKrV6+WJFWpUkXTpk1T165dFRUVpQ8++EBnzpzR448/rpUrV2rr1q3y8PDIVRsAnCcyMlJBQUHy9fXNcntuftcYhqFZs2ZpwoQJSklJybLOiBEjtHv3bv3www+5jm3fvn2OWbRNmzbVrFmzVKZMGUnSQw89pO3btysyMlJvv/22hg4dKkn65ptvFBsbq3379unXX3/Vf/7zH0nSDz/8oP/85z/6/vvvtXPnTs2YMSNXr3s6f/68mjVrpl9++UXvvvuuRo8eLel68uTJJ5/UZ599ph07dujbb7/VmDFj9Oeff+b6/EhWAAAAAMBNJuPDwY0bN2rdunU31cPB3LSbKjY2VmvXrlWzZs1yVR9AwQ0cOFBhYWF68skntXDhQkVEROjpp5+WdP0h/oABAxy/d9IaNWqU4zV277zzjkaMGOGYsZD2fe6bN29WWFiYgoKCNG3atDzFNnToUJ06dUrfffddltsrV66sUaNG6cUXX8zV8Tp16qSTJ09q2rRp8vHx0ZNPPuk414y2bdum2267TfXr15ckPfHEE3mKHYBzpP19c/ToUYWFhal+/frpZlPkpHv37qpRo4YWLlyY5fbSpUtr2rRpGjt2bK6PmfoaqH379uno0aN6+eWXHV8GiYyMVIcOHRQcHKynnnpK+/bt05UrVxQaGqoDBw5oxIgR+vzzzx3jupUrV+rXX3/V7bffrrCwMP3973/X2bNndeXKlRvG4OXlpbvvvlvS9S+4HD16VJL0448/6tixY7rzzjsVFhamrl27yjTNPH1ZhGQFAAAAANwksno4OGLECEk338PB3Jg1a5aaNm2qLl266J577tGQIUMK7dgAbiz1tXQ//vijY5HWjIvHZrWY7IABAxyvb1u1apUeffRRSVLTpk21c+dOx9oz7dq1U1RUlAYPHqz4+HhJUrNmzbR169YcY3Nzc9Mrr7yicePGpZtVltaYMWMUERGhyMjIXJ2vl5eXevXqpRkzZuiLL77QJ598kmW97NoDUHw0bdpUhw8fVmxsrCSpTp06ioqK0vjx4xUbG5vr3zWS9Oqrr2rq1Km6fPlyltsffPBBXb58WStXrsxznLfeeqtuv/12rV+/XleuXNE999yjN954Q3v27NH3338v0zR15coV1a5dW/v27VPPnj31ww8/KDg4WLGxsTJNU4899phjDYyoqCj98ccfKl269A3bTZ3JIV3/fZo6c8Q0TYWEhKQ73smTJ9WhQ4dcnxPJCgAAAAC4SbjSw8HctDtu3DhFRkZq586dmjRpUpbnDiBnsZeuaN73R3Xn3O/VfMZ3avnyOt3zzx+1bMcpJV3N+vUmGXXt2tXxKrqzZ89qxYoV6ty5c6Z67u7uevLJJ3XXXXfpnnvukY+Pj2N/f39/jRo1SklJSY76ly5dcvz/2LFjtXDhQn311VeOst9++y3LV+ANGDBApUuX1vLly7OMt2zZsnrppZc0adKkHM/t22+/dTzUlK7P/KpTp44kydvbO91aOq1bt1ZkZKQOHTokSfroo49yPD6AwnP2ylXtv5iow5eSFH8t699fdevW1d13362hQ4fqwoULjvLU3zcjRozQpk2b9K9//cuxLSYmRm+99VamYzVr1kxt27bVP//5zyzbMgxDM2fOzNeXNeLi4vTLL7+ofv36SkpK0tWrV+Xv7y/p+pdPUv32228yDEN33XWX5syZI9M0derUKfXt21f//ve/derUKUmS3W7Xjh078hxHqjZt2ujw4cPasGGDoywqKirHmRppkawAAAAAgGIq+dgxnV/4L5156y2d++c/Ff/ddzL/lzjIja5du2r+/PmSbr6Hg3lpF0D+2O2mXvvmgFq+sk4z1xzQ/tMJOnfxis4kJCvyZKyeD9+l5jPWaen2Uzke6+2339auXbsUEhKiTp06aeLEiWrZsmWWdYcOHarff/9dI0eOdJQZhqE1a9bI3d1djRs3VpMmTdSmTRv9+eefjveyN2nSRGvWrNHcuXNVu3ZtNWnSRPfff79uueWWLNt57bXXdPz48WxjHjZsWI7fMJauv3qvffv2Cg4OVkhIiP773//q008/lSR16dJFly5dUmhoqJ566ilVqVJF8+bNU9++fdWmTRvZbDyaA4raVbup1WcuqN/Ow2ryw1512n5Q7X4+oIZbduupvcf104WLmb5IsWjRIjVp0kS33367GjVqpDvuuEPr1q3T888/r2rVqmnLli1avXq1atWqpZCQEHXr1k3e3t5Ztv/yyy/r999/zza+nj17qnbt2rk6l9Q1K8LCwtSqVSsNHjxYd911l7y9vTVt2jS1bNlS7du3T7cezu7du9WmTRuFhITotttu00MPPaSQkBC1b99er7zyiu6++26FhoYqODhYn3/+ea7iyErFihW1atUqTZ8+XaGhoWrUqJHGjRsnu92e62MYJvPPnCI+Pl4VKlRQXFxcth23qASO+yrnSjchm0w1rGhqf6whu1zvG1THZ/W2tP39DRpa2r5VTJtNl+rXl9fBgzLy8Mv4ZtHwwH6rQ3A5drtdZ86cUZUqVbjRscAtG6OsDsESNtNU8LVE7XH3lN3FvqX8Z6cwp7dp5TgSmTnr53Fx8xadnzdPl7dvl2y2638k6do1ufn6quLf/qZKQx+TrWxZxz6BgYFavXq1goODHWV//fWXnnzySR06dEg2m01///vf9eSTT2ZZ/+zZs6patap+/fVXNWnSJN05v/jii/rqq69UtmxZlS9fXnXq1NGECRPUsOH1Md/27ds1ceJEHTlyRF5eXvL29tYLL7ygu+66S0OGDHEsOitdf5VU+/btNXnyZE2ZMkURERF67rnnHN/ku3btmho3bqzk5OQbPkTMa7u4jrEDMsquT9jtpsYs+1VfRmb/gC2tCb0a6In2dQolpqVLl+rDDz/M8rV1KFr8jkBGJb1P/JV8VYN2HdXei0lyk5RxLoWbIaWY0t1VfPR2w5ryKMJzZFyfM5IVTkKywvlIVpCssALJCpIVzlbSB44lHckKkhXOwE1N8eKMn8f5BQt0Zvac6wmK7MYTNps86tVTzX8tlHvFijc8Xm7/reDhoGtg7ICMsusTH2w6qllrDuTpWIsebaGO9asUKJ6ePXvq0KFDWrFihUJDQwt0LOQdvyOQUUnuE7FXr+nOXw7pVOKVTEmKjGySulXy1sImteRWRPc4jOtz5m51AAAAAACA6y6Eh19PVEjZJyr+ty358GGdeuIJBXz6qWxppvrnR9qHgwBw5ZpdH2w6mqd9bIb0fsTRAicrvvnmmwLtX1TOnDmj7t27Zyrv1q2bZs+ebUFEAHIy8fDvuUpUSJJd0rfn4/Xx7+f02K1+RR3aDTVv3lzXrl1LV9a4cWN99tlnFkXkPCQrAAAAAKAYsCcm6q+Zs3K/Q0qKknbvUfyqVfL532La+cXDQQBpfbP3T124nPv1cSTJbko/R8foyJkEBVUpX0SRWadKlSqKioqyOgwAuXT2ylWtPBObq0RFWvN/O6tHa1SWYeEM8oIscl3SkawAAAAAgP9r777jczr/P46/7gyJiIgkthJ7r9qjEqu1Y9aIvWqLESvRIAQ1o0YptUdtam9atKhZxbfa0hE1SxAi4/z+8MvdRGJWcyfyfj4eHppzzn1cJ7l67jvX+1zXJwkI3bqV6FiFq1+KycTtxUtI17SpRX+p/q9ocFDEMvacv4a1yUTUK64cbmWC3eevv5VhhYgkLyuu3uZVix8YwK8PH3Pozn2qpNd9zBIUVqQAlq5dYCnJeU09EREREUl57qxa/fw6FQkxDML/9z/CL1zAvlDKrBkmIm/e3w8ev3JQAWBtZXrlGRkiIv+F7+8+4HUqiVoDJ0PDFFZYiMIKEflPpNRCywrJRFIWSxRbTgp0rxP5bzz+/fdXCypiifjzT4UVIvLG2NtaY+LJU8avwjDA3lafDUTE8u5Hvd5nKisTPHjN18q/p3cQERERERGRJMCIetVVlWO/Vr9Ui8ibky+TI1avsbRcZLRBPi0BJSJJQDqbJ6Hrq4o2wNFaQ+aWou+8iIiIiIhIEmCTIcPrv9bN9Q22RERSupZlcxD9GstAOTvYUqtwpv+gRSIir6ZSesfXel0UUMn59V4r/57CChERERERkSQgXcOGT2pWvCKbjBlJXbLkm2+QiKRY77g44FEgA9ZWL/9cspUJ2pTPSSobDTWJiOU1z5SeVK9wD4MnA+VFHO0p5eTw3zRKXkjvICIiIiIiIkmAc9Mm8KrLrlhZkb51a0zW1v9No0QkxRrdsCiOdjZYv8RtydrKRL6Maenumee/b5iIyEtIZ2tDq8wurzT4HQ10fycjptdYBk/eDIUVIiIiIiIiSYCNqysu7du9fGBhbY21S3qcW3z43zZMRFKkHK4OrOxWAec0qXjWw8mm//9TIJMjS7uUx9HOJjGbKCLyXB/nzUbxtKl52Uc62mZxpVmm9P9pm+T5FFaIiIiIiIgkERkHDiTtBx+8+EBra6wcHckx/wts0uuXahH5bxTK4sROn6r41MyPm2OqePtzZUjD6EZFWdujMhnS2lmghSIiz+ZgbcWaknnxcEkLgE0Cwas1T0LXXjkyMqFAds2qsDBF3iIiIiIiIkmEydqabFMmc6tAfm59sYDoe/ee1LGIjn5ygLU1REeTplJFMo8YQaocOSzbYBF567k62tG3Rj56eubh2OW/uXk/HGsrE9mcU1M8ezoN7IlIkuZoY82y4rk5eS+MBX/cZOP1Ozw2DADS2VjTJqsr7bK6kjO1AtekQGGFiIiIiIhIEmKyssKtRw9cOnXi3vbt3Nu/n6g7d7BKnRq7PHlx/rA5qd55x9LNFJEUxsbaiop5XC3dDBGRV2YymXjXKQ3vFk5DcKEc3I+KxtoEDlZWClyTGIUVIiIiIiIiSZCVnR3pvLxI5+Vl6aaIiIiIvBWsTCacbF62ioUkNtWsEBERERERERERERERi1JYISIiIiIiIiIiIiIiFqWwQkRERERERERERERELEphhYiIiIiIiIiIiIiIWJTCChERERERERERERERsSiFFSIiIiIiIiIiIiIiYlEKK0RERERERERERERExKIUVoiIiIiIiIiIiIiIiEUprBAREREREREREREREYtSWCEiIiIiIiIiIiIiIhalsEJERERERERERERERCzKxtINSCkMwwAgNDTUwi1JOaKjo7l37x729vZYWSmXk8ShfieJTX1OLEH9LnHFfH6M+TwplpUcP9fr/1mJTf1BnqY+IbGpP8jT1CfeHH2ufzGFFYnk3r17ALzzzjsWbomIiIiIJEf37t0jXbp0lm5GiqfP9SIiIiLyb+hz/bOZDEU5iSI6OpqQkBDSpk2LyWSydHNShNDQUN555x1+//13nJycLN0cSSHU7ySxqc+JJajfJS7DMLh37x5Zs2bV02xJQHL8XK//ZyU29Qd5mvqExKb+IE9Tn3hz9Ln+xTSzIpFYWVmRPXt2SzcjRXJyctLNVBKd+p0kNvU5sQT1u8SjJ6+SjuT8uV7/z0ps6g/yNPUJiU39QZ6mPvFm6HP98ynCERERERERERERERERi1JYISIiIiIiIiIiIiIiFqWwQt5adnZ2BAQEYGdnZ+mmSAqifieJTX1OLEH9TiR50f+zEpv6gzxNfUJiU3+Qp6lPSGJSgW0REREREREREREREbEozawQERERERERERERERGLUlghIiIiIiIiIiIiIiIWpbBCREREREREREREREQsSmGFiIiIiIhIErN//35MJlOCf7799ts4x544cYKaNWvi6OiIs7MzTZo04ZdffknwvJ9++ikFCxbEzs6OXLlyMWrUKCIiIuIdd/36dTp06ICbmxsODg5UrFiRPXv2/CfXKi/vm2++oW7duqRPn57UqVOTL18+AgMD4xyj/pAydOjQ4Zn3iKfvE+oTKcPJkydp1KgRWbNmxcHBgYIFCzJ69GjCwsLiHKf+kHIcPXqUDz74gLRp0+Lo6Ei1atU4dOhQgseqX0hSobBCREREREQkiQoKCuLIkSNx/hQtWtS8/8KFC3h6evL48WNWrVrFF198wf/+9z/ee+89bty4EedcY8eOpV+/fjRp0oQdO3bQs2dPgoKC6NWrV5zjwsPDqVGjBnv27CE4OJiNGzeSKVMmateuzYEDBxLluiW+5cuX4+HhQbp06Vi8eDFbt25lyJAhGIZhPkb9IeUYMWJEvHvDkSNHcHNzI1u2bJQtWxZQn0gpfvzxRypVqsTly5eZNm0amzdvpmXLlowePZpWrVqZj1N/SDmOHTtG1apVefjwIUuWLGHJkiU8evSIGjVqcOTIkTjHql9IkmKIJBM///yzERkZaelmiIiIiIj85/bt22cAxurVq597XPPmzQ03Nzfj7t275m2XL182bG1tjcGDB5u33bx507C3tze6desW5/Vjx441TCaTce7cOfO2mTNnGoBx+PBh87aIiAijcOHCRrly5f7tpclr+OOPP4w0adIYPXr0eO5x6g8p2/79+w3A8Pf3N29Tn0gZ/Pz8DMC4dOlSnO3dunUzAOP27duGYag/pCQffPCBkSlTJuPBgwfmbaGhoYabm5tRqVKlOMeqX0hSopkVkiyMHz+eypUrc+DAAaKjoy3dHEkhzpw5w5EjR9i/f7+lmyIplBHrSUkREZGnRUZGsnnzZpo2bYqTk5N5e86cOalWrRrr1683b9u+fTuPHj2iY8eOcc7RsWNHDMNgw4YN5m3r16+nQIECVKxY0bzNxsaGNm3acPToUf7888//7qIkQfPmzePBgwcMGTLkmceoP8j8+fMxmUx06tQJUJ9ISWxtbQFIly5dnO3Ozs5YWVmRKlUq9YcU5tChQ3h6euLg4GDeljZtWqpWrcrhw4e5evUqoPuEJD0KKyRZqFu3LqlSpWLgwIEcOHCAqKgoSzdJ3nL+/v40a9aMqlWrUr16dbp160ZISIilmyVvsQMHDjB//nxmzpxpnpZrMpks3CpJKWIHYwrJRJKWXr16YWNjg5OTEx988AHffPONed/PP//Mw4cPKV68eLzXFS9enEuXLvHo0SMAfvjhBwCKFSsW57gsWbLg5uZm3h9z7LPOCXDu3Ll/f2HySg4ePIiLiwsXLlygZMmS2NjYkDFjRrp3705oaCig/pDS3b17lzVr1lCjRg1y5coFqE+kJO3bt8fZ2ZkePXrwyy+/cO/ePTZv3sycOXPo1asXadKkUX9IYR4/foydnV287THbzp49C+g+IUmPwgpJ8qKjoylevDjbtm3j/v379OvXj3379imwkP9M165dmTVrFk2aNGHixIm8//77zJs3j4CAAEs3Td5SgwcPpkOHDgwYMIA+ffrg7e3Njh07LN0secvFDiUePXpkHuyK2a6ZjCKWlS5dOvr168ecOXPYt28fwcHB/P7773h6eprfI27dugWAi4tLvNe7uLhgGAZ///23+Vg7OzvSpEmT4LEx54o59lnnjP3vSuL5888/CQsLo3nz5rRo0YLdu3fj6+vL4sWLqVu3LoZhqD+kcCtWrODhw4d07tzZvE19IuVwd3fnyJEj/PDDD+TJkwcnJycaNGhA+/btCQ4OBtQfUprChQvz7bffxvlMHxkZyXfffQf883NRv5CkxsbSDRB5HsMwsLJ6kqm5ubkxc+ZMateuTVBQEFZWVnh6epr3i7wJXbt2ZePGjXz55Zd4enpia2tL3bp1GTx4MPPnz6dFixbUrFnT0s2Ut0ifPn348ssvCQ4OpmLFily/fh1PT08OHjzIBx98YOnmyVvKMAzzzJ01a9YwZ84czp07h7OzM/Xq1aNXr164u7sTHR2t91kRCylVqhSlSpUyf/3ee+/RuHFjihUrxuDBg+O8RzxvJl7sfS973KseK/+96OhoHj16REBAAEOHDgXA09OTVKlS4ePjw549e8xLfag/pEzz58/H1dWVxo0bx9unPvH2u3z5Mg0aNCBTpkysWbOGDBky8N133zFmzBju37/P/PnzzceqP6QMffr0oXPnzvTu3Rs/Pz+io6MZNWoUV65cAYj3GV/9QpIK/fYpSVrMzWvx4sXUqFGDGTNmkCtXLg4dOsSgQYNUw0LeqMmTJzN//nxmzJhBrVq1zP0vf/781KlTB3iyxqLImzJ9+nQ2bdrEF198QYsWLXB3d6dcuXJUq1YNk8nEw4cPefjwofl4Lc8jb0rM/W3JkiW0adOGHDly0LNnT0qXLs2SJUuoVasWv/32m4IKkSTG2dmZ+vXrc+bMGR4+fIirqyuQ8NOJt2/fxmQy4ezsDICrqyuPHj0iLCwswWNjP/3o6ur6zHNCwk9fyn8r5mf99IMMMZ9RT5w4of6Qgp05c4bjx4/Tpk2bOMu+qE+kHEOHDiU0NJQdO3bQtGlTqlatiq+vL9OmTeOLL77gwIED6g8pTKdOnRg/fjxLliwhe/bs5MiRgx9//JFBgwYBkC1bNkD3CUl69BuoJHlbtmyhS5cutGvXjqlTp/Ltt9+yatUqbt26Rb9+/VTDQt6YsLAwMmbMyKhRo/jrr7+wsbExvwk/ePAAOzs70qZNa+FWytvi2rVrXLhwgQ8//JAqVarEGRR+8OABhw8fpkiRInh6ejJ8+HBAT5/Im/XDDz8wcuRI/Pz8mDp1Kv7+/ixZsgR7e3vSpEljXptWRJKWmODaZDKRJ08eUqdObV53OrazZ8+SN29e7O3tgX/Wl3762L/++oubN29StGhR87ZixYo985xAnGMlcSS07jf80x+srKzUH1KwmKfmu3TpEme7+kTKcerUKQoXLhxveZ6yZcsCmJeHUn9IWYYMGcLNmzc5e/Ysly9f5vDhw/z999+kSZOG0qVLA7pPSBJkiCRxXbt2NUqVKmXcvHkzzvbvv//eyJw5s1G+fHljz549RmRkpIVaKG+LqKgoIzg42HBzczMKFChgXLlyxTAMw/juu+8Me3t7Y+TIkRZuobxttm/fbly4cCHOtv79+xv29vZG+/btjYCAAKNatWqGyWQy/P39LdRKeVutX7/eyJ49u3H8+HHztoYNGxrZs2c3Tp06ZRiGYZw7d864c+eOpZooIk+5ffu2kS1bNqNkyZLmbR9++KGRMWNGIzQ01LztypUrRqpUqYwhQ4aYt926dcuwt7c3unfvHuec48aNM0wmk3Hu3DnztlmzZhmA8e2335q3RUREGEWKFDHKly//X1yavMCOHTsMwBg7dmyc7VOmTDEA4+uvvzYMQ/0hJXr06JHh4uJilCtXLsH96hMpQ7Vq1YwMGTIY9+7di7N97ty5BmBs2LDBMAz1h5TuypUrRrp06QwfH58429UvJClRWCFJSkRERJyvw8PDDU9PT6Ns2bKGYTwZTI6OjjYMwzAiIyON4cOHGyaTyahYsaKxY8eORG+vJH8//vijsX79emPr1q2GYfwTWGTIkMEoUqSIsW3bNiNt2rRGly5djMePHxuGYZj7oMjrelYfWrBggeHg4GB89dVXxoMHDwzDMIyffvrJqFSpklGmTBnj7t27idlMeUvF9L9JkyYZrq6uxv379w3DMIw6deoY77zzjjmoOH36tNGxY0fjyJEjFmurSErWqlUrY8iQIcbq1auNffv2GXPnzjUKFChg2NjYGLt27TIfd/78ecPR0dGoWrWqsXXrVmPdunVG0aJFjaxZsxrXr1+Pc84xY8YYJpPJGD58uLF//35j4sSJhp2dndG1a9c4xz169MgoUqSI8c477xjLli0zdu3aZTRu3NiwsbEx9u/fnyjXL/E1aNDAsLOzMwIDA41du3YZ48aNM+zt7Y369eubj1F/SHlWrlxpAMbcuXMT3K8+kTJs3LjRMJlMRoUKFYwvv/zS2LNnjzF27FjD0dHRKFy4sBEeHm4YhvpDSnL27Flj5MiRxubNm41du3YZkyZNMtzc3IwyZcrEC7XULyQpUVghScKff/4Z5+vNmzebB1OGDh1qpEmTxjh//rxhGHEDjTlz5hgFCxY00qZNayxevDjxGixvBX9/f6NChQpG9uzZjX79+hk3btwwDOPJQN706dONzJkzGyaTyWjdurVhGIZm78gb8bywKywszDhx4kS87b179zayZMli3Lp1679smrzlnu5727ZtM0wmk7F27VqjSZMmRrZs2YyTJ08ahvHkYYEpU6YYFStWNE6fPm2B1orIuHHjjJIlSxrp0qUzrK2tjQwZMhiNGzc2jh49Gu/Y48ePGzVq1DAcHBwMJycno1GjRsalS5cSPG9wcLCRP39+I1WqVEaOHDmMgIAA8wMZsf31119Gu3btDBcXF8Pe3t6oUKFCnJBEEl9YWJgxZMgQ45133jFsbGyMHDlyGMOGDTMePXoU5zj1h5SlVq1aRpo0aeI8Ef009YmUYe/evcb7779vZM6c2UidOrWRP39+Y+DAgfFWqVB/SBkuXrxoVK1a1XBxcTFSpUpl5M2b1/D39zc/qPQ09QtJKkyGoWqdYlkXL15kwoQJ5M6dG39/fxYtWkTHjh1ZtWoVzZo1Y/fu3bRt25asWbOybt06cubMCUB4eDijRo0iVapUDBkyhNSpU1v4SiQ56d69O+vXr2f06NFUq1aN/PnzA0/W/TWZTERHRxMcHExwcDCpUqVi//79ZM2alcjISBXZljfi119/JWvWrOYiiM/qWzdv3qRz586kTZuWhQsXqv/JK4m5pwFER0fHqY1y/fp1vL292b9/P87Ozpw8eZLs2bNz//591q5di4+PD2PHjqVnz56War6IiIiIiIikIBrxEIuzs7Pj6tWrLF26lBMnTrBx40ZmzZpF/fr1AahZsya9e/dmypQpeHh4MHXqVKytrbl06RIzZ85k2rRp5qDi6YEYkYSMHTuWjRs3snjxYmrUqBFn8DcmqLCysqJfv35YW1szZswYqlevzt69exVYyGuLPWj8ww8/ULx4cebOnYu3tzepU6c296nYx4WHh7NlyxYOHz7M9OnT1e/klcTuS1u3buXLL78kOjqasmXL0rdvXzJmzEivXr0ICwvju+++Y8WKFdjY2PDzzz+zdOlSBg8ebA4qYp9LRERERERE5L+gmRWSJISEhFC7dm1++OEH6taty+bNmwF4/PgxqVKlAmDmzJksWLCAEydOAODm5saAAQMYOnSoxdotyc9vv/1G06ZNqV+/PkOGDMHe3j7B46KiorC2tsYwDD799FPGjx+Pvb09Bw8eJHv27IncaknuYg/0nj17litXrtCqVSvSpEnDhAkTaNmypXmGRYxffvmFzZs3M2zYMIYPH46fn58lmi5vgaVLl/LRRx9RqlQpbt68yf/+9z+8vb1ZsmQJAPv372fVqlVs2bKF6OhoKleuTJ06dWjfvj2gBwFEREREREQkcSisEIvYsGED169fp23bttjb2/Prr79SpUoVnJ2d+e233xg6dCj+/v7AkyeLYwbxbt26xffff4+9vT1p06alVKlSgAZS5OXt37+f6tWrc/jwYSpUqPDcY2P6VXR0NFOnTmXo0KF88cUXtG3bNpFaK2+bBQsWEBQURIECBYiIiODEiROEhYUxffp02rZtaw5nDxw4QN++fQkPD+ejjz6if//+gO518nJih2N37tyhTp06NGnShJ49e3L//n2WLVuGv78/9erV48svvzT3qWvXrpE6dWpSpUplDnLV50RERERERCSxaD0JSXT+/v6sXbuWBw8eULFiRYoVK0bu3LnZt28fYWFhDBs2jPHjx2MYBiNGjMDOzo6IiAhsbW1xdXXl/fffj3M+DaTIq/jrr78ASJs27TOPiRnou3DhAjdu3MDDw4P+/ftTs2ZNSpQokVhNlbfMvn376NWrF2PHjqVly5ZkyZKFo0ePMmXKFHr37o3JZMLb2xs7Ozs8PDz46KOPyJs3r/mep3udvKyYoGLjxo1ER0fj7u6Ol5cXadKkIU2aNHTq1Al7e3sGDRpEq1atWLJkCalSpSJTpkzAk3tgDPU5ERERERERSSz6DVQSVc+ePfn888/p378/e/fupVixYkRGRgJQoEABSpUqxYQJE6hcuTITJkwgMDAQAFtbW5YtW0bNmjXjnVMDKfIqYp4W/vnnn4Enyz09LWagb+fOnfj4+HDnzh2srKzMQUV0dHQitVbeJkePHsXFxYWGDRuSOXNmAMqWLcuECROoWrUq/fv3Z9WqVdy/fx94cr+MCSoMw9C9Tp5r8+bN3Lp1y/z1X3/9xdChQ2natClnzpzBxcUFeNKXnJ2dadOmDZMmTWLbtm20bt2a8PBw82tVm0JEREREREQsQSMfkmimTZvG1q1bWbhwIR06dCBv3rwAWFtbxzmuRIkSjBs3jsqVKxMUFETnzp2ZPHky3bp101Pt8q+9++67ZMuWjeHDh3Pjxg2sra0TDB+uXbvGwYMH8fDwwNnZOc4+DRrL6/j5558JDw8nT548mEwmIiIiMJlM5MyZk5YtW3Lv3j369u3L1q1bgbhBmgaP5XlWrFiBt7c39+7dM2/LnDkzQUFB1KhRg0uXLnHw4EHgn1kTTk5OtGnThrFjx7Ju3Tr27dtnkbaLiIiIiIiIxNCImySK8PBwduzYQYMGDXjvvffM67IDnD9/ni1bthAUFMSaNWt48OAB7777LuPHj8fLy4uNGzcyefJkRo0axeTJk4G4S1SIvIocOXLQpEkTfvzxR/r06cONGzewsrIyz/CBJ4PEW7du5cSJE1SvXt2CrZXkLOY+FfN3rVq1uHXrFsHBwcCTGWMx/c7d3Z2KFSvi4eFB586d+f333+MFuSLP0rJlS44dO4a7uzsXLlwwL3fXuHFj+vXrR9GiRenYsSPffPONuQ4PPAks2rZty+nTp6ldu7YlL0FEREREREREYYUkjuvXr7N3717y58+Po6OjeaBk9uzZNGvWjAYNGuDv78+HH35I+/btuX//PqVKlWL69OkcP36cHTt2MGjQIODJEjx6ylheR0y/Cw4OpkaNGqxatYqWLVual4SCJ0+/z5s3j169etG9e3caNmxoqeZKMhQ7SI25T8X8XbRoUUqVKsXUqVNZsmQJADY2NkRERHD06FEyZ85M//79sba2Zu3atfHOJ/K0q1evAk/6WP78+Tl9+jSFCxdm1qxZXLt2DYD69esTGBhIrly5qFevXrzAwtnZmWLFigFa4k5EREREREQsS2GFJIr06dNTqFAhFi5cyKVLl/j666/p3LkzvXr14uHDh0yaNIkNGzbw3nvvsW7dOpYtWwZAxowZcXd3Nw+kaN12+TesrKzMS+vs2rWLpk2bsm/fPt59912qVatGnTp1aNSoEePHj2fkyJEMHToU0ACevJyYwuwAO3bsYMCAATRu3Jhu3brxv//9j0KFCjFt2jQAfHx8GDBgAIsXL2bEiBGMHDmSJk2aUKpUKSB+0CHytJEjR+Lt7c2pU6fM20qUKEG3bt345JNPmDdvnjmwqFu3LkFBQeTKlQsvLy8OHjyY4Hup3l9FRORts3DhQkwmE/b29ly5ciXefk9PT4oWLWqBlsH+/fsxmUysWbPGIv/+q7p8+TL16tXDxcUFk8mEj4/PM491d3fHZDKZ/zg6OlK+fHkWL16ceA22kOXLl5s/84uIyKuzsXQDJGVwdHSkWbNmTJ06leLFixMeHo6DgwN9+vShY8eOlCxZEoD3338fBwcHfvzxxwTPo4E7+besra2JjIzExsaG1atXs2zZMg4cOMDZs2e5ffs2LVq0oEKFCuZi7tHR0RrAk5cSc39asGABvXv3pnLlyoSGhnL69GnWrFnDyJEj6du3L8uXL2fq1KnMmTOHhw8fkj17dsaNG4e3tzdr1qzB0dERd3d3y16MJHk2NjacOHGCsWPH4u/vb67p9Nlnn2FnZ8fHH38MQJcuXciUKRN169bFZDIxcOBAPD09+fPPP8mSJYslL0FERCTRhIeH4+/vb57dKq+uf//+fPfdd3zxxRdkzpz5hZ8jKleuzKRJkwD4448/mDRpEu3bt+fBgwf06NEjMZpsEcuXL+eHH354bpgjIiLPprBC/nMxTxv7+/uTM2dOfvjhB27evEnv3r3Jnz8/adKkMR/7008/4eLiQv78+S3YYknOYj/d/iw2NjZERUVhbW2Nt7c33t7ehIeHYzKZ4tRTUVAhr+rAgQMMHjyYwMBA2rVrh5ubG5cvXyZ37tzs2LGD1q1bU6lSJYoVK0ZYWBi3bt0iS5YspE+fnm+++Yb+/ftTpUoVvLy8LH0pksT5+/vj6OjI6NGjiY6OZsSIEebgP6YuytOBRZ06dQgPDycsLExBhYiIpCi1a9dm+fLlDBo0yBzwpxQPHz7E3t7+Xz/498MPP1CuXDkaNWr0Usc7OztToUIF89c1a9YkZ86cTJky5V+HFW/qmpKThw8fkjp1aks3Q0TkP6dROPnPmUwm8zI6bdu2ZcKECcyfP59SpUrFebONiIhg3759ODg4ULx4cUs1V5KpxYsXm+uZvMw6/08XL7azs4sTVICWRJFXd+TIEXLmzEnjxo1xc3MDoFevXri7uzNmzBjc3NyIiooibdq0ZMqUicKFC/P48WPGjBlD586dqVChAitXrgS0/Jg8W0zf8PHxwd/fn3379hEYGBhnSajg4GB69+7Nxx9/zBdffGEuut2oUSNat24d5zwiIiJvu8GDB+Pq6sqQIUOee9zly5cxmUwsXLgw3j6TycTIkSPNX48cORKTycSZM2do3rw56dKlw8XFhQEDBhAZGcnFixepXbs2adOmxd3dnU8++STBf/PRo0cMGDCAzJkzkzp1ajw8PDh58mS8444fP07Dhg1xcXHB3t6eUqVKsWrVqjjHxCx7tXPnTjp16kSGDBlwcHAgPDz8mdf822+/0aZNGzJmzIidnR2FChVi8uTJ5s8JMctVXbp0iW3btpmXdrp8+fJzv5dPc3Z2pkCBAubluI4fP07Lli1xd3cnderUuLu706pVq3jLdT3vmi5dukTHjh3Jly8fDg4OZMuWjQYNGnD27Nk454i5huXLlzNkyBCyZMmCo6MjDRo04Nq1a9y7d49u3brh5uaGm5sbHTt25P79+3HOYRgGs2bNomTJkqROnZr06dPTrFkzfvnlF/Mxnp6ebNmyhStXrsRZBitGzOf+ggULYmdnR4YMGejYsSM3btyI82+5u7tTv3591q1bR6lSpbC3t2fUqFEArF69mvLly5MuXTocHBzInTs3nTp1eqWfhYhIUqaROEkUMYO+MYPI0dHRcZ5av3v3LitXrmTIkCH07duX9957z2JtleRnz549dOjQgSZNmrxSYCHyphiGgWEYfP3111hbW5MrVy7gSa2As2fPsn79ekqVKsWuXbto164dERER5te6ubmRPn16evTowerVqwHN6pHns7KyMvehAQMGPDew6NevH35+fsyYMYOHDx/GO4+IiEhKkDZtWvz9/dmxYwd79+59o+f+8MMPKVGiBGvXrqVr165MnTqV/v3706hRI+rVq8f69eupXr06Q4YMYd26dfFeP3z4cH755RfmzZvHvHnzCAkJwdPTM84g+L59+6hcuTJ37tzhs88+Y+PGjZQsWZIWLVokGKx06tQJW1tblixZwpo1a7C1tU2w7Tdu3KBSpUrs3LmTwMBANm3aRM2aNRk0aBC9e/cG4N133+XIkSNkzpyZypUrc+TIEY4cOfLKszQjIiK4cuUKGTJkAJ4EQwUKFGDatGns2LGDCRMmcPXqVcqWLcvNmzdf6ppCQkJwdXVl/PjxbN++nZkzZ2JjY0P58uW5ePFigt/r69evs3DhQiZPnsz+/ftp1aoVTZs2JV26dKxYsYLBgwezZMkShg8fHue1H330ET4+PtSsWZMNGzYwa9Yszp07R6VKlcx1wmbNmkXlypXJnDmz+ft05MgR4Mnney8vL8aPH0/r1q3ZsmUL48ePZ9euXXh6esb7nHbixAl8fX3p27cv27dvp2nTphw5coQWLVqQO3duVq5cyZYtW/j444+JjIx8pZ+FiEiSZohY2ObNm43evXsbWbJkMcaNG2feHh0dbcFWSXJy7do1Y9SoUYa9vb3RuHFjIyoqyjCMV+tD6m/yKkJDQ407d+7E2+7r62vkzJnT+Omnn4z69esb2bNnN06ePGl+zdixY43333/fuHLlimEY//S72P0vpv+KxPaie9TEiRON9OnTG02aNDH3uZjXdenSxZg+ffp/3EIREZGkZ8GCBQZgHDt2zAgPDzdy585tlClTxvy+6uHhYRQpUsR8/K+//moAxoIFC+KdCzACAgLMXwcEBBiAMXny5DjHlSxZ0gCMdevWmbdFREQYGTJkMJo0aWLetm/fPgMw3n333Tjv85cvXzZsbW2NLl26mLcVLFjQKFWqlBERERHn36pfv76RJUsW8+fHmOtt167dS31/hg4dagDGd999F2d7jx49DJPJZFy8eNG8LWfOnEa9evVe6rw5c+Y06tata0RERBgRERHGr7/+arRv394ADF9f3wRfExkZady/f99IkyaNERwcbN7+KtcUGRlpPH782MiXL5/Rv39/8/aY73WDBg3iHO/j42MARt++feNsb9SokeHi4mL++siRIwn+rH///XcjderUxuDBg83b6tWrZ+TMmTNe21asWGEAxtq1a+NsP3bsmAEYs2bNMm/LmTOnYW1tHef7bxiGMWnSJANI8PcQEZG3hR6pkzfGeI0n2bds2YKvry//+9//GD9+PEOHDgUwPx0v8iKGYZAxY0Z69uzJ8OHD+eqrr2jWrNkrzbAwYtW5CAkJISoq6r9utiRTO3fu5OOPP6ZChQqULVuWNm3aMG/ePPP+kiVL8ttvv1G1alVOnz7N3r17KVmyJI8fP2bt2rXMmDGDVq1akSNHDuCfotyx73d62l2eFvsedfDgQSZMmEDXrl1ZsGABly5dAmDQoEH4+fmxb98+Ro8ebZ5hYTKZ+Pzzz+nTp4+lmi8iIpIkpEqVijFjxnD8+PF4yyf9G/Xr14/zdaFChTCZTNSpU8e8zcbGhrx588Zb4gigdevWcT4L5syZk0qVKrFv3z4ALl26xIULF/D29gYgMjLS/Kdu3bpcvXo13iyCpk2bvlTb9+7dS+HChSlXrlyc7R06dMAwjH81C2Xr1q3Y2tpia2tLrly5WLVqFX369GHMmDEA3L9/nyFDhpA3b15sbGywsbHB0dGRBw8ecP78+XjnS+iaIiMjCQoKonDhwqRKlQobGxtSpUrFTz/9lOA5EvpZAdSrVy/e9tu3b5uXgtq8eTMmk4k2bdrE+f5nzpyZEiVKsH///hd+PzZv3oyzszMNGjSIc46SJUuSOXPmeOcoXrx4vFqeZcuWBZ7M5lm1ahV//vnnC/9dEZHkRgW25V9bvHgxbdq0wcrK6qWKG8dWr1490qVLR7Zs2czLpmj5E3kZMf0kps+5ubnRvXt3AEaPHk2zZs1Ys2bNC/tl7H2ffPIJX375JVu3biVTpkyJdi2SPIwYMYItW7YQFhZGoUKFuHv3Lps2bWL58uUcOHCAJUuW0Lp1a86dO8e4ceOoW7cuN2/e5O+//2bnzp2MHz8ePz8/OnToALxcMXgR+CfMWrBgAQMHDiRbtmw8fvyYL774gvLly9OtWzc6dOjAwIEDsba2Zty4cYwYMYKAgADKlCljPo/6nIiIpHQtW7Zk0qRJ+Pn50aRJkzdyThcXlzhfp0qVCgcHB+zt7eNtDw0Njff6zJkzJ7jt9OnTAOYlhgYNGsSgQYMSbMPTyya97BJNt27dwt3dPd72rFmzmve/ripVqjB16lRMJhMODg7kyZMnTo3A1q1bs2fPHkaMGEHZsmVxcnLCZDJRt27deEsiQcLXNGDAAGbOnMmQIUPw8PAgffr0WFlZ0aVLlwTPkdDP6nnbHz16hKOjI9euXcMwjGf+jpg7d+4XfDee/Bzv3LkTr05ijJf5GVatWpUNGzYwffp02rVrR3h4OEWKFMHPz49WrVq9sA0iIsmBwgr5V2JqBaxbt45169a9UmARM9hcpUoV8zbDMBRUyAvNmTOHhw8fUqNGDdzd3UmbNi0AGTJkoEuXLsCTgncvCixib/v000/x8/Nj2rRpCioknu7du7Nx40YCAwOpVq0aefLkAeD8+fN069aNZcuWcf/+fdavX8/YsWOxt7dn5syZ1KpVC1tbW/Lnz8/48ePNa/8qlJVXtWPHDnNB7RYtWpAjRw6++eYbqlatSoECBWjQoAGurq74+PgQERHBkCFDzAFuDAUVIiKS0plMJiZMmECtWrWYO3duvP0xAcPTBan/zaD9i/z1118JbnN1dQWe1DcDGDZs2DMDlgIFCsT5+mXf811dXbl69Wq87SEhIXH+7deRLl26OA9NxHb37l02b95MQECAeXUFePJ9v337doKvSeiali5dSrt27QgKCoqz/ebNmzg7O79225/m5uaGyWTi66+/xs7OLt7+hLYldA5XV1e2b9+e4P6Y32ljPOtn6OXlhZeXF+Hh4Xz77beMGzeO1q1b4+7uTsWKFV/iakREkjaFFfKvFCtWjJEjRzJu3LiXfpI9Ruyi2wkthSKSkICAAAIDA4Enfah8+fKULl2a+vXrU7p0abJkycKgQYMwDIOgoCAaNWrEhg0b4vXLp4MKHx8f5s6dS+fOnS12bZI0de/enVWrVrFq1SqqVq1qfhoqMjKSQoUKsXz5cnOYMXToUMaPH8+IESNo0aIFoaGh2NnZ4eLiQrZs2QAFFfJqYvrL+vXrqVKlCu3atTMHqjNmzOCdd97Bx8cHV1dX7t+/j6OjI76+vtSqVYuSJUtatvEiIiJJUM2aNalVqxajR4/mnXfeibMvU6ZM2Nvbc+bMmTjbN27c+J+1Z8WKFQwYMMD8u8mVK1c4fPgw7dq1A54EEfny5eP06dPxBuX/rRo1ajBu3DhOnDjBu+++a96+ePFiTCYT1apVe6P/XoyY5XqfHuSfN2/eKy3JazKZ4p1jy5Yt/Pnnn+TNm/eNtBWeLB81fvx4/vzzTz788MPnHmtnZ5fgrI769euzcuVKoqKiKF++/L9uk52dHR4eHjg7O7Njxw5OnjypsEJE3goKK+S1xa4VYDKZXmnpndjniF0rIFOmTFhbWydG8yWZevfdd0mTJg1OTk54eHhw7949Vq1axWeffUa6dOmoU6cODRo0oG7dugCMHz+etm3bsmjRImxsntzyEgoq5syZo6BC4okJsTZs2EDNmjXj7LOxsSE6Opp33nmHWbNm4enpyZo1a+jVqxfvvPNOvDVmQbPH5NXF/DL/3XffUaJECXNQUa9ePc6ePcvmzZspXrw4u3bt4pdffqFdu3akTp3aHFQoHBMREYlvwoQJlC5dmuvXr1OkSBHz9pi6BF988QV58uShRIkSHD16lOXLl/9nbbl+/TqNGzema9eu3L17l4CAAOzt7Rk2bJj5mDlz5lCnTh0++OADOnToQLZs2bh9+zbnz5/nxIkTrF69+rX+7f79+7N48WLq1avH6NGjyZkzJ1u2bGHWrFn06NEjwc+zb4KTkxNVq1Zl4sSJuLm54e7uzoEDB5g/f/4rzYioX78+CxcupGDBghQvXpzvv/+eiRMnkj179jfa3sqVK9OtWzc6duzI8ePHqVq1KmnSpOHq1at88803FCtWjB49egBPHuhct24ds2fPpnTp0lhZWVGmTBlatmzJsmXLqFu3Lv369aNcuXLY2tryxx9/sG/fPry8vGjcuPFz2/Hxxx/zxx9/UKNGDbJnz86dO3cIDg7G1tYWDw+PN3rNIiKWorBCXplqBYgleXl5sXLlSpo1a8ajR48YOHAgpUqVYufOnezdu5c9e/awdOlSsmfPTurUqcmdOzcrVqwgPDyc1atXYzKZzP1u+vTpDBw4kDlz5piXjxKJERoaal479tKlS9y9e5d06dLFOcbKyoqoqChy5sxJQEAAnTp14vz58/Ge0ouh2WPyIgm9b5pMJlxdXc1FOb28vDhz5ow5qLh37x6bN2/m1q1bNG/enNSpU5tfq6BCREQkvlKlStGqVasEQ4jJkycDT35HvX//PtWrV2fz5s0J1nZ4E4KCgjh27BgdO3YkNDSUcuXKsXLlSvOyowDVqlXj6NGjjB07Fh8fH/7++29cXV0pXLjwC5/0f54MGTJw+PBhhg0bxrBhwwgNDSV37tx88sknDBgw4E1c3jMtX76cfv36MXjwYCIjI6lcuTK7du2KV+z6eWIG6seNG8f9+/d59913WbduHf7+/m+8vXPmzKFChQrMmTOHWbNmER0dTdasWalcuXKcAuX9+vXj3LlzDB8+nLt372IYBoZhYG1tzaZNmwgODmbJkiWMGzcOGxsbsmfPjoeHB8WKFXthG8qXL8/x48cZMmQIN27cwNnZmTJlyrB37944oZuISHJmMgzDsHQjJPl4Vq0AgKtXrzJv3jxGjhyJl5fXK9UKGDBgANOmTaNXr16Jej2SPEydOpUcOXLQtGlT87YNGzbQqlUrSpcuTXBwMKVLlwaezNC5fv06K1eu5NixY3z77bc8fPiQ0aNHx/nQun79epo2bcrcuXMVVMgzXb16leHDh7Ns2TKCgoLo3Lkz6dOnj3NMzP3s4MGDeHp6sn79ery8vCzUYknOYr83njlzBhcXF/OTgZ988glBQUFkyJCBR48esXfvXvLly0dERARLly4lICCACRMmqLiiiIiIiIiIJFuaWSEvTbUCJLEZhsHly5cZOHAghQsXxt7e3vykTaNGjVi5ciWtWrViwIABBAYGUrVqVbJmzUrWrFkpUaIEJpOJo0ePYjKZKFu2rPmcJpOJtGnTsmXLFurUqWPJS5QkLkuWLIwbN46oqCiGDx8OQJcuXczT02Pfz/744w+sra3/s+ny8naL3ZdWrFjB6NGjKViwIPPnz8fFxYV27drx1VdfcfjwYdq0aUO+fPk4deoUBw4cYNiwYQQEBJiDipdZhlFEREREREQkqdHMCnlpGzdupE2bNnFqBRw7dozbt2/HqRWQK1cutm7dyvjx42ncuPFL1QrQk+3yPAcOHKBOnTrkzZuXoKAg6tevb963ceNGWrVqRbly5RgzZgxVqlQBICIiAltb2zjn0drt8rr++usvBg8ezMqVKwkKCooTWMCTtYaHDBnC3bt3WbhwIWnTptVgsbyWRYsW0aNHD/z9/fHw8KBy5crme9eVK1fo06cPX3/9NVZWVtja2pIxY0Y6dOhgXqpB9zkRERERERFJrhRWyCvZsmULzZo1o06dOvTt2zderYBLly6ZawXY2try448/0qRJE3OtgBgxtQJmz56toEISFDPgFhUVhbW1NQcPHqRmzZoULFjwuYFFUFAQlSpVsmDL5W0VO7AYO3YsXbt2xdnZGcMwWLhwIQEBAUyePJnmzZtbuqmSTB0+fJhGjRoxdOhQunfvjoODAwA///wz0dHR5MuXj7CwMC5evMi3335LgQIFyJgxI0WLFgUUVIiIiIiIiEjyprBCnku1AsTSoqKiMJlMWFlZvTCwaNu2LUWLFiUoKAhPT0/LNVreWrEDi3HjxtGpUyf27NlDu3btCAgIYMiQIYCW4ZHX8+mnnzJ79myOHDlCunTpePDgAX369OHIkSP88ssvdOjQgbFjx+Lm5hbvtepzIiIiIiIiktwprJAExdQKyJMnD4ULF2bChAnmWgHwz5PsZcuWNdcKiP3a59UK2L17NxEREaoVIAkaPnw4//vf/2jevDklS5akQIECcfbv3buX2rVrkz9/fsaPHx8nsIgJwlavXh0nYBN5k2ICi7Vr11K/fn02btxorhkAerpdXt+kSZMYPHgwu3fv5uLFi3z66afcv3+fdu3a8eDBA4KDg9m8eTN169a1dFNFRERERERE3jiFFfJcqhUgienzzz/no48+AiBHjhyEhIRQvXp1KleuTKNGjciWLRsuLi4cO3aMKlWqUKBAAQIDA/Hy8jKf49dffyVXrlyWugRJhl7nifSYwGLp0qWMGTPGXHxb9zp5HTF98NSpU/To0YPvvvuOfPnyUaJECRYtWkTq1Kn58ccfee+991iwYAENGza0dJNFRERERERE3jiFFRKPagWIpWzdupUpU6Zw/PhxateuTYMGDZg6dSpnzpwhVapUZM6cmaZNm9K0aVOuX79Oo0aNeO+99+jVqxfNmjWLcy4NGsuLLF68mDZt2mBlZfVagcXvv//OL7/8goeHB6A+Jy8ndl+LjIwkMjKSqKgo0qRJA8CVK1f45ZdfSJ8+PSVLlgTg8ePHLFiwgEmTJrFq1SpKlSplqeaLiIiIiIiI/GcUVsgzqVaAWMKuXbsYN24cX3/9NevXr6d+/fqEhISwbNkyvv/+e3bv3s3t27epXLkyFy9e5ObNm5QsWZI1a9aQO3duSzdfkok9e/ZQq1YtGjZsyLp16147sIihoEJeRuw+tnHjRjZu3MihQ4coVqwYXl5etG3bNt5rrly5wu7du+nXrx+jR49mwIABid1sERERERERkUShkRUxGz58OM2aNePLL7/k4sWLWFtbmwffqlatyvbt27lw4QJDhw5l8+bN5td5eXmxaNEivv32W27dumWp5ksydfDgQWbOnImfnx8AtWrV4uOPP6Z8+fJ4eXmxYMECsmbNiq+vLytXruTQoUPs3r2bXLlyUbhwYQA6dOigoEJeSbFixRg5ciQ7duygWbNmREdHYzKZeJX8PvaxCirkZcQEFYsXL6Zjx45YW1vTsWNH7t69S/v27Zk1a1ac43fv3s2gQYMYNWoUH3/8sTmo0HMmIiIiIiIi8jbSzAoBVCtALMPX15edO3diMpn44IMP6NGjB+7u7sCTein+/v4cOXKEefPm0aFDB+CfJ9hjaqOcPXuWYsWKAa9Xe0BSnph+cvPmTWbPns3o0aNp0KABa9aseekZFrGPCQkJIVOmTFhbWydG8yWZ27JlC506dcLX15dBgwbx22+/UaxYMdzc3Pj111/59NNP6dWrFwCLFi3i3LlzVKhQgSZNmgCaxSMiIiIiIiJvL4UVAqhWgCS+nj17snr1aqZPn07lypXJkSNHvGMOHDjAiBEjOHz4MPPnz6d9+/bAkyXKrKyszE/Cm0wm9Tt5oZg+Eruv3Lhxg88+++yVAovY+z755BO+/PJLtm7dSqZMmRLtWiR5unXrFj169CBnzpxMnDiRs2fPUrFiRdq0aUPnzp3p378/hw8fZu7cuXTp0gWA0NBQnJycAL2/ioiIiIiIyNtNYYWYqVaAJJbg4GAmTpzI3LlzqV27tnnwLWYQOPZgcOzAYsGCBQmu6S7yInPmzOHhw4fUqFEDd3d30qZNa9539epV5s2bx8iRI/Hy8npuYBF726effsqAAQOYNm2a+Ul4kdgeP35MqlSp4mybO3cu7u7ulCpViooVK1KpUiVmzpxJ2rRpWbZsmfkeFxgYaF4eT0RERERERCQlsLF0A8RyDh48yNmzZwkJCWHs2LHUqlULW1tb/P398fLyYt68eXTs2BFfX18ALl68yJ9//snChQuxtrbm4MGDqhUgrywsLIzNmzdTr149qlSpEucp4ZhB4NgzJTw8PBg9ejSjRo2iffv2PHr0iK5du1qq+ZIMBQQEEBgYCDypLVG+fHlKly5N/fr1KV26NFmyZGHQoEEYhkFQUBCNGjViw4YN8QKLp4MKHx8f5s6dS+fOnS12bZI0ff755xw5coRz587RokULmjZtSs6cOQHo1q0bACtWrMDKyoq+fftib28PgI2NDQUKFKBQoUI4OjparP0iIiIiIiIilqCwIoV6ulbA5cuXcXd3x9PTk7Fjx+Lv70/Xrl0xmUzmWgH58uWjQIECvPfee6oVIK/tt99+Y8+ePXTr1s28tElCYg8Ue3p6EhkZye3bt3n06FEitlbeBu+++y5p0qTByckJDw8P7t27x6pVq/jss89Ily4dderUoUGDBtStWxeA8ePH07ZtWxYtWoSNzZO3yYSCijlz5iiokHh69erFV199Ra5cuQgLC2PQoEHcvXuXgIAATCaTuR9dunSJK1euULhwYWxtbQkPD+fSpUt88MEH+Pn5kSFDBgtfiYiIiIiIiEjiUliRAr2oVoCHhwdjxoxhxIgRdOnSBZPJRPv27bGysiIqKso8eFe0aFFAa2jLqwkLCwOeLI8CCfefmIHhkJAQ1qxZQ79+/ahZsybbtm0ja9asid5mSd68vLxYuXIlzZo149GjRwwcOJBSpUqxc+dO9u7dy549e1i6dCnZs2cnderU5M6dmxUrVhAeHs7q1avjDDBPnz6dgQMHMmfOHHNNAZEYXbp0Yc2aNaxatYqyZcuSPn16PvzwQz777DMGDx5MmjRpzMcWKVIER0dHunfvTrt27Thz5gwTJkxg2rRp5qBCDwKIiIiIiIhISqIR5hQmODiYTZs2sWjRIlq0aGEOKmJKl8T87eHhQWBgIJUqVaJz584sWbIEAGtr6zhL9QAKKuSVODs7Y2VlxalTp4B/ZlDEFtO3Dh8+zKRJk/jtt98AzEGFSu3Ii0ydOpW1a9eav65Xrx4rVqxg27Zt+Pv7c+nSJZo3b87s2bPZv38/J06coHXr1mTPnp1ff/0VgJIlS8YZKF6/fj0+Pj7Mnj1bQYXE0717dzZt2sS6det4//33cXZ2BsDT05PcuXNz+vRpdu7cyY8//ghA7dq1ad26Nfv27aNevXpMnjwZPz8/OnXqZD6nggoRERERERFJSVRgOwUJCwvDy8uL3LlzM3HixGcuwRP7Sff9+/czatQoDhw4wJw5c1QrQF5LREQEtra2ADx48IAmTZqwe/duNm7cSP369YH4Myzu3r3L0KFD+fvvv1m4cKF5TXeR5zEMg8uXL5MnTx4KFy7MhAkTqFevnnn/xo0badWqFWXLliUwMJCqVavGea3JZOLo0aOYTCbKli0bZ/vu3buJiIigTp06iX5dkrStWbOGDz/8kM6dO/P555/HmRHRokULNmzYgKOjI3///TdWVlbMnTuXTp068ejRI3744Qf+/vtvXFxcKF26NKAZiyIiIiIiIpIy6TfhFCSmVkDNmjVfqlYAPHki1M/Pj2LFiqlWgLyyrVu3MmTIEDp16sTRo0cBSJMmDZ07d8YwDAYOHMiuXbuAuDN0IiMj2bRpE+vXr6dhw4YKKuSlmUwmcuXKxb59+/jll18YNmwYmzdvNu/38vJixYoVHDt2jI8//phvvvnGvC8yMhKAcuXKmYOK6Oho86BzzZo1FVRIgtzd3WnZsiXz589n4sSJ5j7j4+PD1q1bCQ4O5vDhwyxdupTcuXPTpUsXDh48iL29PWXKlKFWrVoKKkRERERERCTFU82KFES1AiQx+fr6snXrVhwdHWnQoAGZMmUy7/vwww/5/fff8fX1pVWrVkyYMIFGjRrh6urKmTNn2Lt3L35+fvj5+dG6dWtAa7fLi8Xc06KiovDw8GD79u3UrFmT4cOHA5hn8cQEFq1atcLf35+goCAqVapknv0TmwaN5WWUKVMGPz8/DMNgyJAhODk5cfnyZebNm8eXX35J3bp1MZlMFChQgOjoaNq1a8fXX39N1apV470Xq8+JiIiIiIhISqVloFKQX375hfz589O/f38mTpwIPHsAePXq1QwYMIBDhw7FKcCtAWN5GT179mTVqlUEBwdTvXp1smTJkuBxM2fOJCgoiKtXr+Ls7IyTkxPh4eE4OzvTrVs3+vfvD+hJY3k1UVFRmEwmrKysOHjwIDVr1qRgwYIEBQWZAwt4siRU27ZtKVq0KEFBQXh6elqu0fJWOHfuHGPGjGH16tVER0ezb98+PDw8gH/uYydOnKBGjRrMnDnTHMaKiIiIiIiIiGZWvPVi1wrIlCkTNWrUYMqUKXh4eFC/fn1MJlOCtQL27t1L5cqVyZgxY5zzKaiQF5k0aRKbNm1i8eLF1KlTx9xnIiMjsbGx4dKlS2zbto0+ffrQq1cvypYty8WLFzl48CDR0dF4enpSsGDBOMvwKKiQ5xk+fDj/+9//aN68OSVLlqRAgQLmfVWrVmX79u3Url2boUOHAnFnWCxatIimTZty69Yti7Rd3i5FihRh2LBhODg4sGDBAo4dO2YOK6ysrIiMjGTv3r2kT5+efPnyWbi1IiIiIiIiIkmLwoq31NatWzlw4AAhISH06dOHcuXKmWsF7Nq1i4EDB2JnZ0etWrWeWStgypQpqhUgL80wDG7cuGEuYFy1alVzUBEVFYWNjQ2nTp2ievXqPH78mMjISPr370+5cuUoV64cbdu2TfCcCirkeT7//HPGjx8PwPHjxwkJCaF69epUrlyZRo0akS1bNqpXr86hQ4eoUqUKw4cPJyoqCi8vLwAaN27Mzz//TK5cuSx5GfIWKV68OD4+Pjx8+JDBgwdjGAa+vr7Ak0LcI0eOZMyYMeZAVkRERERERESe0CjgW8jX1xdfX1/2799PgQIF4tUKmDhxIj/99BOtWrVi/vz55ieKz5w5w4wZM+jevTt9+/aNUytA5EVMJhNXrlzh0KFD1K5dG0dHR/M+a2trTp8+TZkyZahWrRp58uRh4sSJTJs27YXnFHmemDDCycmJChUqsGDBAm7evElgYCAVK1akXLlyDBkyBMMwWLt2LT/++CPTpk1jzZo15nPEBBXR0dGWugx5yxQrVgw/Pz9atmzJkCFDmDRpEl999RXt2rXD19cXHx8fQO+vIiIiIiIiIrGpZsVbRrUCxJJ27NhBnTp1OHToEBUrVjRvDw8Pp2/fvtja2jJjxgwuXLiAt7c3ly5dYs6cObRs2dKCrZbkbteuXYwbN46vv/6a9evXU79+fUJCQli2bBnff/89u3fv5vbt21SuXJmLFy9y8+ZNSpYsyZo1a8idO7elmy/JxOvUbDp37hzjx49n2bJlAIwcOZKPP/4Y0PuriIiIiIiIyNO0DNRbRLUCxNJsbJ7cUn744QcqVqxIVFQU1tbW2NnZERAQgKurK9HR0RQsWBAfHx8GDRpE3rx5LdxqSW4OHjzI2bNnCQkJYezYsdSqVQtbW1v8/f3x8vJi3rx5dOzY0bz0zsWLF/nzzz9ZuHAh1tbWHDx4kA4dOiiokJf28OFDUqdObb6nvawiRYrg6+vL48eP8fDwoGfPnoDeX0VEREREREQSopkVb4GYWgFNmzalQoUKBAQEmJfgiRlYiV0rIDAw0Dxz4nnn1BI88jJi95WwsDAqVKjA7du3OXbsGFmyZIlT5D1GaGgo06ZN4/vvv2fmzJlkz57dEk2XZMjX15edO3diMpn44IMP6NGjB+7u7gAcOHAAf39/jhw5wrx58+jQoQPwz8BwTF88e/YsxYoVA3SvkxcLCAhg9+7dfPXVV7i4uLxyYAFw8+ZN3NzcAAUVIiIiIiIiIs+i35bfAqoVIJYU01d+/vlnHBwc8Pb2JiQkhGbNmnHt2jVsbW3j1AKIiIhg06ZNzJw5k8aNGyuokJfWs2dPFi5cyNChQ9m0aRMTJkwwBxUAHh4ejBkzhkqVKtGlSxcWLVoEgJWVlbnIO0DRokWBJ4PGutfJ80RHR/P777/zww8/0LFjR/7++2+sra2Jiop6qdfHPA8SE1QACipEREREREREnkG/Mb8lbt++DYCDg0Oc7eHh4cyaNYvu3buzdu1avvzyS7JkyUJAQAArV660RFPlLbR161Zq1qzJ3bt3GTRoEG3atOHIkSNUr16dEydOcPfuXeDJ+u2zZs3io48+om/fvuYn3zXBS14kODiYTZs2sWjRIlq0aEGOHDmAf/pOzN8eHh4EBgZSqVIlOnfuzJIlS4AnwW1MMBHztwaN5XkMw8DKyoo5c+bQqVMnDh48SNu2bbl9+/ZLBxYmkyne/e1lgw4RERERERGRlEYjNW+J2LUC4J/BkJhaAZMnT45TK8De3l61AuSNcXZ25sqVK2zYsAFra2sWLFhAnz59+O2333jvvfd477338PT0xMvLi9mzZxMYGIifnx+gp9vlxcLCwti8eTP16tWjSpUqcUKG2AFEzAweDw8PRo8ezXvvvUf79u35/PPPLdJuSb5ilgeLWTrsk08+oV27dhw6dIh27dq9dGARe5mxzz77jDNnzrzyElIiIiIiIiIiKYXCimQs9tOaFStWpGjRoowaNYqrV69ibW1NREQEAFmzZsXOzg4rKytCQ0P59ddfqVChApkzZ7ZU0+UtYhgGhQoVonLlyixfvpybN29ibW3NtGnTWLduHb179yZr1qxYW1vTu3dv5s2bx4ABAwCt3S4v57fffmPPnj3UrFkTJyenZx5nZWVlvi96enri5+dHsWLFePToUWI1Vd4C165dM4cQMfV2bG1tmTRpEm3btjUHFi9aEip2UDFz5kx69uzJyZMnE+ciRERERERERJIhjRImY6oVIIktoeVMTCYT6dOnp2HDhnzzzTfcuHEDeNI/a9WqxYQJE9i5cyc7d+7Ex8eHKlWqmM+loEJeRlhYGACPHz8GiHNfixHTN0NCQggODgagZs2abNu2jT59+iRSSyW5++ijj8iSJQsNGzYkMDCQb7/9locPH2IYhjmw6NChA/v373/uklCxg4pPP/2Uvn37Mn/+fNq3b2+JyxIRERERERFJFjRSmMypVoAklth95eLFi0RGRsZZzsTb25uMGTMyYcIEIiMj473m6aVPtPSTvCxnZ2esrKw4deoUEHcGRYyY/nT48GEmTZrEb7/9BjyZWQa618mLnTx5kk2bNgHw66+/EhQURLVq1ShRogQ+Pj6sWbOG8PBwpk6dSs+ePfn+++/x9vaON8Pi6aDCx8eHOXPm0LFjR4tdm4iIiIiIiEhyoLAimVOtAPmvzZ07l1OnTmEymTCZTGzfvp2SJUtSvnx5li9fzvnz5wHIlCkTnp6eHDhwwPwkvMjrilnGDp70rRo1ajBlyhQ2b94MxK1REePu3bvs3buXypUrkzFjxjj7dK+TF8mdOzcTJ04kf/782Nrasnv3bqZNm0aZMmVYtWoVLVq0IF++fLRq1YpcuXJRpEgRzp49i7e3t3mGBZBgUNGlSxdLXpqIiIiIiIhIsmAy9LhpsmUYBnfu3KFhw4Y4ODiwbNky3NzcMAyD3bt3s3v3bk6ePElUVBQNGjSgTJky5iV4VCtAXsY333xD7dq1KVu2LDNmzKBIkSL89ddfLFq0iL1793Lo0CHs7e3p2LEjzZo1w9XVlcKFCzNx4kT69etn6eZLMrR161YOHDhASEgIffr0oVy5cgCsWrWKli1bki9fPmbMmEGtWrXivC4yMpIVK1bg6+vLlClTaN26tSWaL8nQlStXyJYtGzY2Nty7d49NmzbRp08fypYty+zZs8mdOzdXrlzh0qVLrF27lq+//prr16+bl7wD2LRpE/Xr1zd//emnn9KvXz8+//xzOnfubInLEhEREREREUl2FFYkE7GXlYAntQJinuKcOHEiI0eO5Pjx4xQqVCjea2Mfm9C5RJ5n6tSpzJo1ixw5cjBp0iRKlSpl3rd9+3aOHDnCnDlziIqKws3NjTt37lCmTBlWrFhBmjRp1Nfkpfn6+rJ161YcHR1p0KABbdu2JWfOnOb9kydPxtfXFxcXFyZMmECjRo1wdXXlzJkz7N27Fz8/P/z8/Bg+fDige5282NixY5k7dy7Lly+nQoUKWFtbc//+fTZu3Ei/fv3ImzcvK1asIFeuXMA/Qf/+/fu5ePEiy5Yto02bNnTr1s18zkuXLtG2bVs6d+6sGRUiIiIiIiIir0BhRTIQ8yMymUxcvHiRPHnyYGNjY94fEhJC5cqV8fDwYN68edjY2GiQTv612CHXtGnTmD59Orly5WLq1KkUL148zrG//vor3377LYsXL+bgwYM8fvyYgwcPUrFiRUs0XZKhnj17smrVKoKDg6levTpZsmRJ8LiZM2cSFBTE1atXcXZ2xsnJifDwcJydnenWrRv9+/cHNHtMXs6WLVvo0qUL77zzDtOmTaN8+fJxAgsfHx8KFCjAokWLyJMnT7z31nv37pE2bVogbp+7fPky7u7ulrgkERERERERkWRLYUUSNnfuXMqVK0fJkiWBJ0+xN27cmMKFCzNw4EBKlSpFoUKFiIqKokuXLuzfv5/Tp0/j5OSksELeiNiDb7EDi2nTplGsWDFzzYDYg8Lbt2+nX79+VKpUiblz52JjY6O+KM81adIkpk2bxty5c6lTp465v0RGRmJjY8OlS5fYtm0bffr0AeDo0aNcvHiRgwcPEh0djaenJwULFqRs2bKAggp5edHR0ezdu5cOHTqQKVMmPv3002cGFosXLyZ37tzAP7N2nv5bfU9ERERERETk9SmsSKJUK0AsLaHBtylTpjBjxow4gUXMcbFnYrRs2ZKzZ89y4sQJ7OzsLHkZkoQZhsGNGzdo2rQpFSpUICAgAEdHR+CfmT2nTp2ievXqPH78mMDAQPPMieedU+GYvEjs+1V0dDR79uyhY8eOzw0sChcuzBdffEGePHks3HoRERERERGRt5Me/0uiqlSpQmBgIH/88Qd9+/bl5MmTZM6cmSFDhrBjxw7WrFlDr169WLJkCfXr16dBgwa4urqye/du7t+/jzIoeR2x+83ff//NrVu3+Ouvv4iIiABgwIAB9OrVi19//RUfHx/Onj1rfqrY2traPNOiRIkSAFy7di3xL0KSDZPJxJUrVzh06BC1a9c2BxUA1tbWnD59mjJlylCtWjXy5MnDxIkTmTZt2gvPKfIsBw4c4Pfff49Tx8nKygoPDw8WLFjA1atX6dOnD9999x1RUVE4Ojri5eXF9OnTOXXqFI0bNyY0NNSCVyAiIiIiIiLy9tLMiiRItQLEEmI/kb569WqCg4M5d+4c0dHRNGjQgJYtW1K/fn3gnxkWefPmZdKkSXH65e+//07BggVp3rw5CxcutMSlSDKyY8cO6tSpw6FDh+Lct8LDw+nbty+2trbMmDGDCxcu4O3tzaVLl5gzZw4tW7a0YKslOfLx8WH69Om4ublRr149ihcvTqNGjciRI4f5PXfXrl107NgRV1dXZs+eTbly5bCxseH+/fusWrWKqKgounbtauErEREREREREXk7KaxIolQrQBJT7KBi6dKldO7cmc6dO5MpUybCwsKYMmUK7u7uBAQE0KZNGwCCg4P55JNPyJo1K9u3b8fV1RWAn3/+mRUrVuDv7x/v3CJP27NnD7Vq1WLOnDl07do1TlgbEhKCq6srtra2WFlZsWTJEgYNGsSWLVsoU6aMhVsuycm1a9coUqQIt2/fJmPGjGTOnJmff/6Z6OhoChUqxIcffkiFChUoVaoUJ0+epEuXLri4uDB58mTKly+PjY2NuYYK6L4mIiIiIiIi8l9QWJEEqVaAWMr58+dp2LAhLVu2ZNiwYTg4OACwc+dOOnbsSIYMGZg/fz6lS5cGYPz48WTOnJkOHTokeD4Vm5WExB7oDQsLo0KFCty+fZtjx46RJUsWIiIisLW1jfOa0NBQpk2bxvfff8/MmTPJnj27JZouydjJkyfx9vbGxsaGxo0b06lTJ5YtW8a+ffs4fPgwDx8+pGTJkhQqVIj06dMza9YsqlWrhr+/P56engonRERERERERP5jGkVMIlQrQBLb2rVrGTNmDF26dOHAgQOEh4fz8OFDQkJCKF++vDmoiI6O5v3332f69OmcOXOGEydOmM8xdOhQc1CRUO6poEISEjPo+/PPP+Pg4IC3tzchISE0a9aMa9euYWtra76nAURERLBp0yZmzpxJ48aNFVTISwsLC+Py5cvcuXOHUqVKsWTJEsLDw5k/fz7ffPMNw4cPZ9euXXz99desWrWKHDly8N1337FgwQIA9u3bR2hoqIIKERERERERkUSgmRVJgGoFSGLr27cvq1at4vHjx9y5c4d06dIxaNAg0qZNS//+/dm+fTu1atUiIiICa2trrKysMAyD/PnzU7JkSVavXh1nRo/Iq9q6dSu9evXi1KlTODo60rFjR5YuXUqhQoVYsmQJuXLlIn369Jw7d47du3czfPhwhg8fjp+fH6BleOTFPv30U/bt28ePP/6Ir68v3t7e2Nvbc+rUKVq0aMGDBw8YOnQovXv3Nr8mMjKSyMhINm3axE8//YSTkxN9+vSx4FWIiIiIiIiIpBwKKyxMtQIksXXp0oV169Yxffp0SpQoQUhICIGBgVy+fJkpU6bg5+dHgQIF2Lx5M/BPP/rzzz+pXr06rVu3JiAgwMJXIcnd4cOHqVKlCgsWLKB9+/ZERUUxYMAAvvjiC6Kjo8mVKxdubm788ccf2NjY0K1bNwYMGABoeTF5sZ49e7J582ZatGhBtWrVqFmzJqlSpTLfz2ICi4cPHzJ06FB69uwJkOASZKA+JyIiIiIiIpIYFFYkEaoVIImhe/furF69mjVr1pjXYDcMg71791KrVi1atWpFvnz5GD16NO3atWPevHnY2Njw8OFDVq5cia+vLwsXLjTP9BF5HYZhcOfOHRo2bIiDgwPLli3Dzc0NwzDYvXs3u3fv5uTJk0RFRdGgQQPKlClDlSpVAN3b5MV8fX1ZsGABS5YswcPDw/x+Ck/6nmEYWFlZcfr0afMMi2HDhpkDi9iFtEVEREREREQk8ei3cQtYu3Yt58+f5/Lly7Rt25YKFSq8sFZA8+bNOXHihDmsGDp0qPl8Cc2g0GCePG3nzp3MnTuXxo0bU6FCBXOfMZlM5M2bFxcXF/LkyUOXLl24du0ac+fO5cSJExQsWJDo6Gh27NjB0KFDFVTIK3n6/hSzfFj69Olp2LAhI0eO5MaNG7i5uWEymahVqxa1atWKc2zsc+neJs+zd+9eNmzYwJQpU/jggw/i9ReTyYTJZCI6OpoSJUrw5Zdf0qJFCyZOnEhUVBR9+vRRUCEiIiIiIiJiIRr1SWR9+/alV69eTJkyhS+++IJGjRoxadIkvvnmGx49eoSdnR2AubA2QJMmTciTJw87d+4EngzgxaalnuRllC1blkGDBvHVV18REBDAgwcPzH3pxx9/5Pbt2xQrVozs2bMzcuRIli1bRqZMmTh16hTR0dHMnDnTXC8gdvFjkWeJPXHv4sWLREZGxgkfvL29yZgxIxMmTCAyMjLea56uiaJ7nbzIyZMnefz4MTVq1HhusGVlZWUOLFasWIHJZGL48OGcPXs2EVsrIiIiIiIiIrHp8cFE9KxaAbNnz2bKlCnkzp2b4OBgatWqha2trXnQLiQkBCsrK4oWLQrEH8ATeRnp06fH398fwzCYNGkSUVFRTJ48mRMnTtC8eXP69etH8+bNMQyDTJky0bJlS1q2bElERAQmk8n8tLGW4ZEXmTt3LuXKlaNkyZIAbN++ncaNG1O4cGEGDhxIqVKlKFSoEJkyZcLT05P9+/cTFhaGk5OTZRsuyZZhGERHR3Pw4EFcXFzIli3bM5dzipntc+HCBbJkyUKpUqVYvXo1P/74I8WKFbNA60VEREREREQENLMi0XTv3p3169ezdu1avL29KVasGO+//z6jRo0iJCSEjRs34u3tzdatW+nQoQORkZGYTCYePnzIzp07uXXrlnkJKJHX5eTkxIgRIxg4cCBTp06lTZs2eHp64u3tzbhx4+IcGzN7wtbW1hxOaBkeeZFvvvmGAQMG0L9/f86dOwdAyZIlGTlyJG5ubnTr1o333nsPX19fjh8/jp+fH3/++ScLFiwANHtCXo/JZMLa2hpHR0du3rwJgI2NDQmV5YrpYytWrGDRokVERUVRunRp2rZtC2jmmIiIiIiIiIilaNQxEcTUCvD09HxhrYCPPvqIJUuW8O677/Lhhx/Stm1b+vbtS//+/VUrQN6ImMBi0KBBrFu3jmzZsjF27Fjs7e2BfwbyYocSMf+tgWR5kSpVqhAYGMgff/xB3759OXnyJJkzZ2bIkCHs2LGDNWvW0KtXL5YsWUL9+vVp0KABrq6u7N69m/v37yc4uCzyPLdu3TL/d7Fixfjzzz+ZOXMm8OSelVCfOnfuHMuWLcPFxSXebEUFsiIiIiIiIiKWod/IE4FqBUhS4+TkxPDhw/Hx8eHixYtMmDCBsLAwSzdLkrmY+1r//v3p1asXv/76K4MGDeLMmTPmY2rXrs2oUaM4cuQI06dPx93dndDQULZv387Zs2cViMlLW7JkCR07dqRAgQIcOnQIgMaNG5M2bVpmz57Njh07gCeBRew6UJGRkezbtw9nZ2eKFClikbaLiIiIiIiISHwmQ4+xJorQ0FACAwOZPHky/fv3N9cKqFq1Kl27dmXq1KnmdbRjqFaA/Ndi90tfX18CAgJwcHCwdLMkGYt9n5o2bRrTp08nV65cTJs2jWLFipkD19j3su3bt9OvXz8qVarE3LlzsbGxUWghz9W3b1+2bt1K6dKlKV++PF5eXuTOnRuTycSyZcvo3LkzxYsXZ/DgwTRr1sz8utu3b7NlyxZ69OjBmDFj8PHxsdxFiIiIiIiIiEgcCisSUeyB4datW7Np0yZatWpFcHAw9vb25rAi9mBfzH8/HWSIvCmhoaGMHTuWiRMn0qNHD4KDgxMsSivyIgndw6ZMmcKMGTPiBBYxx0VFRZmX4GnZsiVnz57lxIkT2NnZWfIyJInr3bs3K1eu5PPPP6dKlSpkyJAB+Kf/hYaGsm7dOrp3747JZKJZs2bUrFmTW7ducezYMXbt2kX//v3NMxb1/ioiIiIiIiKSNCisSGShoaGMGTOGGTNmkDNnTr7++mvc3Nws3SxJ4UJDQxk2bBgFChSgb9++lm6OJCOxB3pv376NYRiEh4eTIUMGbG1tAZg8eTIzZ85MMLCICTbGjRvH0qVL2bZtGzly5LDkJUkSNmfOHIKCgvj0009p0KCBue9FRERga2vLhQsXWLp0Ke3bt+fvv/82L0P2+PFjbGxs+OCDD2jUqBHe3t6AZiyKiIiIiIiIJCUKKyzgzp07fPLJJ4wfP56BAwcyatQoLb0jFvf48WNSpUpl6WZIMhI7qFi9ejXBwcGcO3eO6OhoGjRoQMuWLalfvz7wzwyLvHnzMmnSJIoXL24+z++//07BggVp3rw5CxcutMSlSDIQFRVFixYtsLKy4rPPPsPFxcW83drampMnT1KhQgUiIiLo1q0bEyZMwMHBgevXr/PHH3+QOXNmMmbMSOrUqQEFFSIiIiIiIiJJjdZ6sQBnZ2eGDh1KREQEkydPxsrKSrUCxOIUVMiriB1ULF26lM6dO9O5c2dq1apFWFgYU6ZM4bvvvuPOnTu0adOGAQMGYG1tzSeffELnzp3Zvn07rq6uwJOgbNiwYfj7+8c7t0iM33//nS1btjBhwgRzUAFgbW3N6dOnqVKlCp07d8bZ2Znx48cTHR2Nr68v+fLlI1u2bMCTvhXzt4IKERERERERkaRFYYWFODk5MWLECKysrJg4cSL3799XrQARSTZiwoTz588zatQoBg8ezLBhw8yha40aNejYsSOTJk2iUKFClC5dmn79+vHw4UMyZ85sDioA8uTJYw4q9LS7PMuDBw+wsrIyv0/GLP0EsG/fPrp27crYsWNJkyaN+aGAPHnyMGTIEHMAFtNvFYaJiIiIiIiIJD0aGbcgJycn/Pz8uH//PgUKFFBQISJJ2tq1azl//jyXL1+mbdu2VKhQgYcPHxISEkL58uXNQUV0dDTvv/8+06dPp3nz5pw4cYLSpUsDMHToUPP5EppBoaBCniVLliykS5eObdu20bNnT2xtbc1LQPn4+MQJL2rWrEmuXLnInz8/oHBCREREREREJDnQ6LiFOTk5MXXqVC3BIyJJWt++fVm1ahWPHz/mzp07rF27lkGDBpE2bVoePXqEnZ0d8ORpd2trawCaNGlCnjx52LlzJ127djUPLMfQALK8CgcHBypXrszatWuZPn06ffv2xdra2hxSxAQVjx494sCBA2TLlo0CBQpYuNUiIiIiIiIi8rL0CGsSoKBCRJKyLl26sHTpUiZNmsSBAwfYtm0bRYoUYfbs2WTOnJncuXMTHBwMgK2trTmECAkJwcrKiqJFiwLECSpEXpW9vT2BgYE4OTkxfvx45s2bB2AOKQDCw8NZs2YNo0ePpnXr1hQuXNhSzRURERERERGRV6SwQkREnql79+6sX7+etWvX4u3tTbFixXj//fcZNWoUISEhbNy4EW9vb7Zu3UqHDh2IjIzEZDLx8OFDdu7cya1bt8xLQIn8WwULFmT16tXcv38fHx8ffH19uXXrFlFRUXz//fdMnjyZHj16MHDgQLp37w78U1RbRERERERERJI2k6Hf4kVEJAE7d+6kdu3aNG7cmKVLl5I6dWrzvitXrlC6dGl69uxJt27dGDt2LHPnzqVIkSIULFiQ6OhoduzYwdChQ/Hz87PgVcjb6OjRo7Rt25affvoJKysrnJ2diYqKIkeOHHTt2pXevXsDKtguIiIiIiIikpworBARkQT9/fffjBs3jmnTpuHj40NAQAD29vZYW1uzbds26tWrx5dffknz5s25du0a+/btY/78+Vy5coWiRYvSqFEj2rVrB2jQWN68q1evcvLkSb777jseP35MhQoVyJcvn3npJ/U5ERERERERkeRFYYWIiDxTaGgogYGBTJ48mf79+zN58mROnDhB1apV6dq1K1OnTsUwjDjFsiMiIjCZTNjY2AAaNJbE93SfFBEREREREZGkz8bSDRARkaTLycmJESNGADB58mSuXbvGpk2b8Pb2Zty4cXGOjQklbG1tiY6OBp4MGiuokMSmoEJEREREREQk+dHMChEReaHQ0FDGjBnDjBkzyJkzJ19//TVubm6WbpaIiIiIiIiIiLwl9LiriIi8kJOTE8OHD8fHx4eLFy8yYcIEwsLCLN0sERERERERERF5S2gZKBEReSnOzs4MHTqUiIgIJk+ejJWVFQEBATg4OFi6aSIiIiIiIiIikswprBARkZcWU8PCysqKiRMncv/+fYKDg83FtEVERERERERERF6HRpdEROSVODk54efnx/379ylQoICCChERERERERER+ddUYFtERF7L48ePSZUqlaWbISIiIiIiIiIibwGFFSIiIiIiIiIiIiIiYlFWlm6AiIiIiIiIiIiIiIikbAorRERERERERERERETEohRWiIiIiIiIiIiIiIiIRSmsEBERERERERERERERi1JYISIiIiIiIiIiIiIiFqWwQkRERERERERERERELEphhYiIiIiIiIiIiIiIWJTCChERERERERERERERsSiFFSIiIiIiIiIiIiIiYlEKK0RERERERERERERExKIUVoiIiIiIiIiIiIiIiEX9H61XCIylwY+eAAAAAElFTkSuQmCC", "text/plain": ["<Figure size 1600x1200 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "📊 BENCHMARK SUMMARY\n", "================================================================================\n", "Model                Final Energy    Error        Time (s)   Parameters  \n", "--------------------------------------------------------------------------------\n", "PolyGCNN_Std         -21.494316      N/A          136.1      7092        \n", "PolyGCNN_CP          -21.551206      N/A          37.8       4888        \n", "PolyGCNN_Deg3        -4.000202       N/A          29.6       5442        \n", "GCNN_Baseline        -21.523965      N/A          127.2      9400        \n"]}], "source": ["# ============================================================================\n", "# RESULTS ANALYSIS AND PLOTTING\n", "# ============================================================================\n", "\n", "def plot_benchmark_results(results, exact_energy=None, param_counts=None):\n", "    \"\"\"Create comprehensive benchmark plots.\"\"\"\n", "    \n", "    # Filter successful results\n", "    successful_results = {k: v for k, v in results.items() if v['converged']}\n", "    \n", "    if not successful_results:\n", "        print(\"❌ No successful results to plot\")\n", "        return\n", "    \n", "    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))\n", "    \n", "    # Colors for different model types\n", "    colors = plt.cm.tab10(np.linspace(0, 1, len(successful_results)))\n", "    \n", "    # Plot 1: Energy convergence\n", "    ax1.set_title(f\"Energy Convergence - {SYSTEM_CONFIG['system_type']}\")\n", "    for i, (name, result) in enumerate(successful_results.items()):\n", "        ax1.plot(result['iterations'], result['energies'], \n", "                label=name, color=colors[i], linewidth=2)\n", "    \n", "    if exact_energy is not None:\n", "        ax1.axhline(y=exact_energy, color='black', linestyle='--', \n", "                   linewidth=2, label='Exact')\n", "    \n", "    ax1.set_xlabel('Iteration')\n", "    ax1.set_ylabel('Energy')\n", "    ax1.legend()\n", "    ax1.grid(True, alpha=0.3)\n", "    \n", "    # Plot 2: Error convergence (if exact energy available)\n", "    if exact_energy is not None:\n", "        ax2.set_title('Error Convergence (Log Scale)')\n", "        for i, (name, result) in enumerate(successful_results.items()):\n", "            errors = [abs(e - exact_energy) for e in result['energies']]\n", "            ax2.semilogy(result['iterations'], errors, \n", "                        label=name, color=colors[i], linewidth=2)\n", "        ax2.set_xlabel('Iteration')\n", "        ax2.set_ylabel('|E - E_exact|')\n", "        ax2.legend()\n", "        ax2.grid(True, alpha=0.3)\n", "    else:\n", "        ax2.text(0.5, 0.5, 'No exact energy\\navailable', \n", "                ha='center', va='center', transform=ax2.transAxes)\n", "        ax2.set_title('Error Analysis')\n", "    \n", "    # Plot 3: Final energies comparison\n", "    ax3.set_title('Final Energies Comparison')\n", "    names = list(successful_results.keys())\n", "    final_energies = [successful_results[name]['final_energy'] for name in names]\n", "    bars = ax3.bar(range(len(names)), final_energies, color=colors[:len(names)])\n", "    \n", "    if exact_energy is not None:\n", "        ax3.axhline(y=exact_energy, color='black', linestyle='--', \n", "                   linewidth=2, label='Exact')\n", "        ax3.legend()\n", "    \n", "    ax3.set_xticks(range(len(names)))\n", "    ax3.set_xticklabels(names, rotation=45, ha='right')\n", "    ax3.set_ylabel('Final Energy')\n", "    ax3.grid(True, alpha=0.3)\n", "    \n", "    # Plot 4: Parameter efficiency\n", "    if param_counts:\n", "        ax4.set_title('Parameter Efficiency')\n", "        param_list = [param_counts.get(name, 0) for name in names]\n", "        scatter = ax4.scatter(param_list, final_energies, \n", "                            c=range(len(names)), cmap='tab10', s=100)\n", "        \n", "        for i, name in enumerate(names):\n", "            ax4.annotate(name, (param_list[i], final_energies[i]), \n", "                        xytext=(5, 5), textcoords='offset points', fontsize=8)\n", "        \n", "        if exact_energy is not None:\n", "            ax4.axhline(y=exact_energy, color='black', linestyle='--', \n", "                       linewidth=2, label='Exact')\n", "            ax4.legend()\n", "        \n", "        ax4.set_xlabel('Number of Parameters')\n", "        ax4.set_ylabel('Final Energy')\n", "        ax4.grid(True, alpha=0.3)\n", "    else:\n", "        ax4.text(0.5, 0.5, 'Parameter counts\\nnot available', \n", "                ha='center', va='center', transform=ax4.transAxes)\n", "        ax4.set_title('Parameter Analysis')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # Summary table\n", "    print(\"\\n📊 BENCHMARK SUMMARY\")\n", "    print(\"=\" * 80)\n", "    print(f\"{'Model':<20} {'Final Energy':<15} {'Error':<12} {'Time (s)':<10} {'Parameters':<12}\")\n", "    print(\"-\" * 80)\n", "    \n", "    for name in names:\n", "        result = successful_results[name]\n", "        energy = result['final_energy']\n", "        time_taken = result['optimization_time']\n", "        params = param_counts.get(name, 0) if param_counts else 0\n", "        \n", "        error_str = \"N/A\"\n", "        if exact_energy is not None:\n", "            error = abs(energy - exact_energy)\n", "            error_str = f\"{error:.6f}\"\n", "        \n", "        print(f\"{name:<20} {energy:<15.6f} {error_str:<12} {time_taken:<10.1f} {params:<12d}\")\n", "    \n", "    if exact_energy is not None:\n", "        print(f\"\\nExact ground state energy: {exact_energy:.6f}\")\n", "\n", "# Generate plots\n", "plot_benchmark_results(benchmark_results, exact_energy, param_counts)"]}], "metadata": {"kernelspec": {"display_name": "quantum", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 4}