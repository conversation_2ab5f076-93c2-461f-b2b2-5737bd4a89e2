#!/usr/bin/env python3
"""
Example demonstrating how to use KFAC optimization with polynomial Jastrow factors.
"""

import sys
import os
import jax
import jax.numpy as jnp
from jax import random

# Enable float64 precision
jax.config.update("jax_enable_x64", True)

# Add PyTC to path
pytc_path = os.path.abspath('repos/pytc')
if pytc_path not in sys.path:
    sys.path.insert(0, pytc_path)

# PySCF for quantum chemistry
from pyscf import gto, scf

# Import polynomial Jastrow components
from pytc.autodiff.jastrow.polynomial import CPJastrowEN, CPJastrowEE, CPJastrowEEN


def example_basic_kfac_usage():
    """Basic example of using KFAC with polynomial Jastrow."""
    print("=== Basic KFAC Usage Example ===")
    
    # Create H2 molecule
    mol = gto.M(atom='H 0 0 0; H 0 0 1.4', basis='sto-3g', unit='bohr', verbose=0)
    
    # Standard usage (no KFAC)
    print("1. Standard polynomial Jastrow (no KFAC):")
    cp_standard = CPJastrowEEN(mol, degree=4, rank=8, use_kfac=False)
    print(f"   KFAC enabled: {cp_standard.use_kfac}")
    
    # KFAC-enabled usage
    print("2. KFAC-enabled polynomial Jastrow:")
    cp_kfac = CPJastrowEEN(mol, degree=4, rank=8, use_kfac=True)
    print(f"   KFAC enabled: {cp_kfac.use_kfac}")
    
    # Both work identically for forward pass
    key = random.PRNGKey(42)
    r1, r2 = jnp.array([0.0, 0.0, 0.5]), jnp.array([0.0, 0.0, -0.5])
    
    params_standard = cp_standard.init_params(key=key)
    params_kfac = cp_kfac.init_params(key=key)
    
    val_standard = cp_standard._compute(r1, r2, params_standard)
    val_kfac = cp_kfac._compute(r1, r2, params_kfac)
    
    print(f"   Standard value: {val_standard:.6f}")
    print(f"   KFAC value: {val_kfac:.6f}")
    print(f"   Values match: {jnp.allclose(val_standard, val_kfac)}")
    print()


def example_kfac_with_sparsity():
    """Example of using KFAC with different sparsity patterns."""
    print("=== KFAC with Sparsity Patterns ===")
    
    mol = gto.M(atom='H 0 0 0; H 0 0 1.4', basis='sto-3g', unit='bohr', verbose=0)
    
    # All sparsity variants work with KFAC
    variants = ['CP', 'CP_sparse_LU', 'CP_sparse_degree']
    
    for variant in variants:
        print(f"Creating {variant} with KFAC:")
        cp = CPJastrowEEN(mol, degree=3, rank=6, cp_variant=variant, use_kfac=True)
        print(f"  Variant: {cp.cp_variant}")
        print(f"  KFAC enabled: {cp.use_kfac}")
        print(f"  Degree: {cp.degree}, Rank: {cp.rank}")
    print()


def example_kfac_all_components():
    """Example of using KFAC with all Jastrow components."""
    print("=== KFAC with All Components ===")
    
    mol = gto.M(atom='H 0 0 0; H 0 0 1.4', basis='sto-3g', unit='bohr', verbose=0)
    
    # Create all components with KFAC
    components = [
        ("Electron-Nuclear", CPJastrowEN),
        ("Electron-Electron", CPJastrowEE),
        ("Electron-Electron-Nuclear", CPJastrowEEN)
    ]
    
    for name, cls in components:
        print(f"Creating {name} with KFAC:")
        comp = cls(mol, degree=3, rank=6, use_kfac=True)
        print(f"  Class: {cls.__name__}")
        print(f"  KFAC enabled: {comp.use_kfac}")
        print(f"  Input dimension: {comp.input_dim}")
    print()


def example_create_full_ansatz_with_kfac():
    """Example of creating full ansatz with KFAC support."""
    print("=== Full Ansatz with KFAC ===")
    
    # Add qpolynets notebooks to path for model_analysis
    notebooks_path = os.path.abspath('qpolynets/notebooks')
    if notebooks_path not in sys.path:
        sys.path.insert(0, notebooks_path)
    
    try:
        from model_analysis import create_full_ansatz
        
        mol = gto.M(atom='H 0 0 0; H 0 0 1.4', basis='sto-3g', unit='bohr', verbose=0)
        mf = scf.RHF(mol)
        mf.kernel()
        
        # Create ansatz without KFAC
        print("Creating ansatz without KFAC:")
        ansatz_no_kfac, params_no_kfac, _, instances_no_kfac = create_full_ansatz(
            mol, mf, "polynomial", 
            degree=3, rank=6, use_kfac=False
        )
        print(f"  EN component KFAC: {instances_no_kfac['en'].use_kfac}")
        print(f"  EE component KFAC: {instances_no_kfac['ee'].use_kfac}")
        print(f"  EEN component KFAC: {instances_no_kfac['een'].use_kfac}")
        
        # Create ansatz with KFAC
        print("Creating ansatz with KFAC:")
        ansatz_kfac, params_kfac, _, instances_kfac = create_full_ansatz(
            mol, mf, "polynomial", 
            degree=3, rank=6, use_kfac=True
        )
        print(f"  EN component KFAC: {instances_kfac['en'].use_kfac}")
        print(f"  EE component KFAC: {instances_kfac['ee'].use_kfac}")
        print(f"  EEN component KFAC: {instances_kfac['een'].use_kfac}")
        
    except ImportError:
        print("  model_analysis not available - showing manual creation:")
        
        # Manual creation example
        from pytc.autodiff.jastrow import NuclearCusp, CompositeJastrow
        from pytc.autodiff.ansatz.sj import SlaterJastrow
        from pytc.autodiff.ansatz.det import SlaterDet
        
        # Create components with KFAC
        ncusp = NuclearCusp(mol)
        cp_en = CPJastrowEN(mol, degree=3, rank=6, use_kfac=True)
        cp_ee = CPJastrowEE(mol, degree=3, rank=6, use_kfac=True)
        cp_een = CPJastrowEEN(mol, degree=3, rank=6, use_kfac=True)
        
        print(f"  EN component KFAC: {cp_en.use_kfac}")
        print(f"  EE component KFAC: {cp_ee.use_kfac}")
        print(f"  EEN component KFAC: {cp_een.use_kfac}")
    
    print()


def example_kfac_optimization_setup():
    """Example of how KFAC would be used in optimization (conceptual)."""
    print("=== KFAC Optimization Setup (Conceptual) ===")
    
    mol = gto.M(atom='H 0 0 0; H 0 0 1.4', basis='sto-3g', unit='bohr', verbose=0)
    
    # Create KFAC-enabled components
    cp_een = CPJastrowEEN(mol, degree=4, rank=8, use_kfac=True)
    
    print("KFAC optimization would work as follows:")
    print("1. Create polynomial Jastrow with use_kfac=True")
    print(f"   ✓ Component created with KFAC: {cp_een.use_kfac}")
    
    print("2. During forward pass, KFAC automatically registers operations:")
    print("   - Each U[k] @ x matrix multiplication")
    print("   - Final C @ out linear layer")
    print("   - Bias addition")
    
    print("3. KFAC optimizer uses registered operations to:")
    print("   - Build Fisher Information Matrix approximation")
    print("   - Compute natural gradients")
    print("   - Apply second-order updates")
    
    print("4. Benefits:")
    print("   - Faster convergence than first-order methods")
    print("   - Better conditioning for polynomial optimization")
    print("   - Automatic handling of curvature information")
    
    print("\nNote: Actual KFAC optimization requires kfac_jax optimizer setup")
    print("      This implementation provides the registration infrastructure")
    print()


def main():
    """Run all usage examples."""
    print("KFAC-Enabled Polynomial Jastrow Usage Examples")
    print("=" * 50)
    
    example_basic_kfac_usage()
    example_kfac_with_sparsity()
    example_kfac_all_components()
    example_create_full_ansatz_with_kfac()
    example_kfac_optimization_setup()
    
    print("🎉 All examples completed!")
    print("\nKey takeaways:")
    print("  • Set use_kfac=True to enable KFAC registration")
    print("  • KFAC works with all sparsity patterns")
    print("  • KFAC works with all Jastrow components")
    print("  • Forward pass values are unchanged")
    print("  • Ready for KFAC optimizer integration")


if __name__ == "__main__":
    main()
