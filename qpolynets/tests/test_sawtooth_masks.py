#!/usr/bin/env python3
"""
Test script to verify the sawtooth mask implementation.
"""

import sys
import os
import jax
import jax.numpy as jnp
from jax import random

# Enable float64 precision
jax.config.update("jax_enable_x64", True)

# Add PyTC to path
pytc_path = os.path.abspath('../../repos/pytc')
if pytc_path not in sys.path:
    sys.path.insert(0, pytc_path)

# PySCF for quantum chemistry
from pyscf import gto

# Import polynomial Jastrow components
from pytc.autodiff.jastrow.polynomial import CPJastrowEEN


def test_sawtooth_mask_structure():
    """Test the structure of sawtooth masks."""
    print("=== Testing Sawtooth Mask Structure ===")
    
    mol = gto.M(atom='H 0 0 0; H 0 0 1.4', basis='sto-3g', unit='bohr', verbose=0)
    
    # Test different input_dim/rank combinations
    test_cases = [
        (3, 6),   # input_dim < rank (vertical repetition)
        (6, 3),   # input_dim > rank (horizontal repetition)
        (4, 4),   # input_dim == rank (square case)
        (2, 8),   # input_dim << rank
        (8, 2),   # input_dim >> rank
    ]
    
    for degree, rank in test_cases:
        print(f"\nTesting degree={degree}, rank={rank}:")
        
        cp = CPJastrowEEN(mol, degree=degree, rank=rank)
        
        # Test sawtooth mask generation
        mask1, mask2 = cp._generate_sawtooth_masks(l_offset=0, u_offset=0)
        
        print(f"  Input dim: {cp.input_dim}, Rank: {rank}")
        print(f"  Mask1 shape: {mask1.shape}")
        print(f"  Mask2 shape: {mask2.shape}")
        print(f"  Mask1 non-zeros: {jnp.sum(mask1)}")
        print(f"  Mask2 non-zeros: {jnp.sum(mask2)}")
        
        # Check shapes
        expected_shape = (cp.input_dim, rank)
        assert mask1.shape == expected_shape, f"Mask1 shape should be {expected_shape}, got {mask1.shape}"
        assert mask2.shape == expected_shape, f"Mask2 shape should be {expected_shape}, got {mask2.shape}"
        
        # Check data types
        assert mask1.dtype == jnp.float32, f"Mask1 should be float32, got {mask1.dtype}"
        assert mask2.dtype == jnp.float32, f"Mask2 should be float32, got {mask2.dtype}"
        
        # Check that masks are different (unless degenerate case)
        if cp.input_dim > 1 and rank > 1:
            assert not jnp.allclose(mask1, mask2), "Mask1 and Mask2 should be different"


def test_sawtooth_triangular_patterns():
    """Test that sawtooth masks create proper triangular patterns."""
    print("\n=== Testing Triangular Patterns ===")
    
    mol = gto.M(atom='H 0 0 0; H 0 0 1.4', basis='sto-3g', unit='bohr', verbose=0)
    
    # Test with square case for clearest pattern
    cp = CPJastrowEEN(mol, degree=4, rank=4)  # Creates 5x4 masks (input_dim=5 for EEN)
    
    # Test different offsets
    offsets = [(0, 0), (-1, 1), (1, -1)]
    
    for l_offset, u_offset in offsets:
        print(f"\nTesting offsets: l_offset={l_offset}, u_offset={u_offset}")
        
        mask1, mask2 = cp._generate_sawtooth_masks(l_offset=l_offset, u_offset=u_offset)
        
        print(f"  Mask1 (L pattern):\n{mask1[:4, :4]}")  # Show first 4x4 for clarity
        print(f"  Mask2 (U pattern):\n{mask2[:4, :4]}")

        # For the square part, check triangular properties
        min_dim = min(cp.input_dim, cp.rank)
        square_mask1 = mask1[:min_dim, :min_dim]
        square_mask2 = mask2[:min_dim, :min_dim]

        # Check that mask1 has lower triangular structure (from L_base)
        for i in range(min_dim):
            for j in range(min_dim):
                if j > i + l_offset:
                    assert square_mask1[i, j] == 0, f"Mask1[{i},{j}] should be 0 (lower triangular)"

        # Check that mask2 has upper triangular structure (from U_base)
        for i in range(min_dim):
            for j in range(min_dim):
                if j < i + u_offset:
                    assert square_mask2[i, j] == 0, f"Mask2[{i},{j}] should be 0 (upper triangular)"


def test_sawtooth_repetition_patterns():
    """Test that sawtooth masks repeat patterns correctly."""
    print("\n=== Testing Repetition Patterns ===")
    
    mol = gto.M(atom='H 0 0 0; H 0 0 1.4', basis='sto-3g', unit='bohr', verbose=0)
    
    # Test horizontal repetition (input_dim > rank)
    print("Testing horizontal repetition (input_dim > rank):")
    # Create a custom molecule with more nuclei to get larger input_dim
    mol_large = gto.M(atom='H 0 0 0; H 0 0 1; H 0 0 2; H 0 0 3', basis='sto-3g', unit='bohr', verbose=0)
    # For EEN: input_dim = 1 + 2*num_nuclei = 1 + 2*4 = 9 for 4 H atoms
    cp_h = CPJastrowEEN(mol_large, degree=2, rank=3)  # Creates 9x3 masks
    mask1_h, mask2_h = cp_h._generate_sawtooth_masks()
    
    print(f"  Input dim: {cp_h.input_dim}, Rank: {cp_h.rank}")
    print(f"  Mask1 shape: {mask1_h.shape}")
    
    # Check that patterns repeat horizontally
    min_dim = min(cp_h.input_dim, cp_h.rank)
    if cp_h.input_dim >= cp_h.rank and cp_h.rank > 1:
        # Compare first block with second block
        block_size = cp_h.rank
        num_complete_blocks = cp_h.input_dim // block_size
        print(f"  Complete blocks: {num_complete_blocks}, block_size: {block_size}")
        if num_complete_blocks >= 2:
            # For horizontal repetition, we compare blocks along the column dimension
            block1 = mask1_h[:, :block_size]  # All rows, first block_size columns
            block2 = mask1_h[:, block_size:2*block_size]  # All rows, second block_size columns
            print(f"  Block1 slice: [:, :{block_size}] -> shape {block1.shape}")
            print(f"  Block2 slice: [:, {block_size}:{2*block_size}] -> shape {block2.shape}")
            print(f"  First block:\n{block1[:min_dim, :]}")  # Show first few rows for clarity
            print(f"  Second block:\n{block2[:min_dim, :]}")
            # Blocks should be identical for repetition
            if block2.size > 0:  # Only compare if second block exists
                assert jnp.allclose(block1, block2), "Horizontal blocks should repeat"
                print("  ✓ Horizontal repetition verified")
            else:
                print("  Second block is empty - skipping comparison")
        else:
            print(f"  Not enough columns for repetition test (need {2*block_size}, have {cp_h.input_dim})")
    else:
        print(f"  Skipping repetition test (input_dim={cp_h.input_dim}, rank={cp_h.rank})")
    
    # Test vertical repetition (rank > input_dim) - need custom case
    print("\nTesting vertical repetition (rank > input_dim):")
    # Create a custom test case by directly calling the function
    cp_v = CPJastrowEEN(mol, degree=6, rank=6)  # This will have input_dim=5, rank=6
    mask1_v, mask2_v = cp_v._generate_sawtooth_masks()
    
    print(f"  Input dim: {cp_v.input_dim}, Rank: {cp_v.rank}")
    print(f"  Mask1 shape: {mask1_v.shape}")
    
    if cp_v.rank > cp_v.input_dim:
        print("  Vertical repetition case detected")


def test_sawtooth_functionality():
    """Test that sawtooth masks work in forward pass."""
    print("\n=== Testing Sawtooth Functionality ===")
    
    mol = gto.M(atom='H 0 0 0; H 0 0 1.4', basis='sto-3g', unit='bohr', verbose=0)
    r1 = jnp.array([0.0, 0.0, 0.5])
    r2 = jnp.array([0.0, 0.0, -0.5])
    key = random.PRNGKey(789)
    
    # Create a custom CP variant that uses sawtooth masks
    cp = CPJastrowEEN(mol, degree=3, rank=6)
    
    # Manually set sawtooth masks
    mask1, mask2 = cp._generate_sawtooth_masks()
    cp.masks = [mask1 if i % 2 == 0 else mask2 for i in range(cp.degree)]
    cp.cp_variant = 'CP_sawtooth'  # Custom variant for testing
    
    params = cp.init_params(key=key)
    val = cp._compute(r1, r2, params)
    
    print(f"  Sawtooth value: {val:.6f}")
    assert jnp.isfinite(val), "Sawtooth value should be finite"
    
    # Test with KFAC
    cp_kfac = CPJastrowEEN(mol, degree=3, rank=6, use_kfac=True)
    mask1_kfac, mask2_kfac = cp_kfac._generate_sawtooth_masks()
    cp_kfac.masks = [mask1_kfac if i % 2 == 0 else mask2_kfac for i in range(cp_kfac.degree)]
    cp_kfac.cp_variant = 'CP_sawtooth'
    
    params_kfac = cp_kfac.init_params(key=key)
    val_kfac = cp_kfac._compute(r1, r2, params_kfac)
    
    print(f"  Sawtooth + KFAC: {val_kfac:.6f}")
    assert jnp.allclose(val, val_kfac, atol=1e-10), "KFAC should not change values"
    print("  ✓ KFAC compatibility verified")


def test_sawtooth_offset_effects():
    """Test the effect of different offsets."""
    print("\n=== Testing Offset Effects ===")
    
    mol = gto.M(atom='H 0 0 0; H 0 0 1.4', basis='sto-3g', unit='bohr', verbose=0)
    cp = CPJastrowEEN(mol, degree=3, rank=4)
    
    # Test different offset combinations
    offset_cases = [
        (0, 0),   # Standard
        (-1, 1),  # Shifted
        (1, -1),  # Opposite shift
    ]
    
    for l_offset, u_offset in offset_cases:
        mask1, mask2 = cp._generate_sawtooth_masks(l_offset=l_offset, u_offset=u_offset)
        
        print(f"  Offsets ({l_offset}, {u_offset}):")
        print(f"    Mask1 non-zeros: {jnp.sum(mask1)}")
        print(f"    Mask2 non-zeros: {jnp.sum(mask2)}")
        
        # All masks should be valid
        assert jnp.all(mask1 >= 0), "Mask1 should be non-negative"
        assert jnp.all(mask2 >= 0), "Mask2 should be non-negative"
        assert jnp.all(mask1 <= 1), "Mask1 should be <= 1"
        assert jnp.all(mask2 <= 1), "Mask2 should be <= 1"


def main():
    """Run all sawtooth mask tests."""
    print("Testing Sawtooth Mask Implementation")
    print("=" * 40)
    
    try:
        test_sawtooth_mask_structure()
        test_sawtooth_triangular_patterns()
        test_sawtooth_repetition_patterns()
        test_sawtooth_functionality()
        test_sawtooth_offset_effects()
        
        print("\n🎉 All sawtooth mask tests passed!")
        print("\nSawtooth mask implementation is working correctly:")
        print("  ✓ Proper L/U triangular patterns generated")
        print("  ✓ Efficient repetition along elongated dimensions")
        print("  ✓ Configurable diagonal offsets")
        print("  ✓ Forward pass functionality verified")
        print("  ✓ KFAC compatibility maintained")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
