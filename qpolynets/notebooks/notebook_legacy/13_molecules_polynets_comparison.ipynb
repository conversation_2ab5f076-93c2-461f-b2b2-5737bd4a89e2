{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Molecules: PolyGCNN vs GAT (Quick Setup)\n", "H2 minimal example to verify models and plotting.\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"ename": "ModuleNotFoundError", "evalue": "No module named 'qpolynets.models'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mModuleNotFoundError\u001b[39m                       <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[6]\u001b[39m\u001b[32m, line 11\u001b[39m\n\u001b[32m      7\u001b[39m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01msys\u001b[39;00m\n\u001b[32m     10\u001b[39m sys.path.append(\u001b[33m'\u001b[39m\u001b[33m/Users/<USER>/Projects/Quantum/qpolynets/src/\u001b[39m\u001b[33m'\u001b[39m)\n\u001b[32m---> \u001b[39m\u001b[32m11\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mqpolynets\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mmodels\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m PolyGCNN, MolecularGAT\n\u001b[32m     13\u001b[39m \u001b[38;5;66;03m# Minimal H2 setup\u001b[39;00m\n\u001b[32m     14\u001b[39m n_atoms = \u001b[32m2\u001b[39m\n", "\u001b[31mModuleNotFoundError\u001b[39m: No module named 'qpolynets.models'"]}], "source": ["import jax, jax.numpy as jnp\n", "import netket as nk\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from flax import nnx\n", "\n", "import sys\n", "\n", "\n", "sys.path.append('/Users/<USER>/Projects/Quantum/qpolynets/src/')\n", "from qpolynets.models import PolyGCNN, MolecularGAT\n", "\n", "# Minimal H2 setup\n", "n_atoms = 2\n", "n_orbitals = 4\n", "n_electrons = 2\n", "\n", "# Spin-orbital fermions Hilbert space\n", "hi = nk.hilbert.SpinOrbitalFermions(n_orbitals=n_orbitals, n_fermions=n_electrons)\n", "\n", "# Placeholder Hamiltonian: simple 4-site Ising as stand-in (replace with your molecular Hamiltonian when ready)\n", "g4 = nk.graph.Chain(length=4, pbc=False)\n", "H = nk.operator.Ising(h=1.0, hilbert=nk.hilbert.Spin(s=0.5, N=4), J=0.5, graph=g4)\n", "\n", "# Molecular GAT\n", "gat = MolecularGAT(n_atoms=n_atoms, n_orbitals=n_orbitals, hidden_features=(8,4), n_heads=(2,1), rngs=nnx.Rngs(0))\n", "\n", "# PolyGCNN: here we use the chain graph as a surrogate, for a proper molecular adjacency/space-group supply real molecular graph\n", "g = nk.graph.Hypercube(length=4, n_dim=1, pbc=True)\n", "polygcnn = PolyGCNN(symmetries=g, layers=2, features=(8, 8), degree=2, mode=\"fft\")\n", "\n", "# Sampler/opt\n", "sampler = nk.sampler.MetropolisLocal(hi, n_chains=8)\n", "opt = nk.optimizer.Sgd(learning_rate=0.02)\n", "sr = nk.optimizer.SR(diag_shift=0.1)\n", "\n", "# VMC runs (few steps)\n", "vs_gat = nk.vqs.MCState(sampler, gat, n_samples=256)\n", "vmc_gat = nk.VMC(H, opt, variational_state=vs_gat, preconditioner=sr)\n", "\n", "def run_collect(vmc, vs, H, n_iter):\n", "    import numpy as _np\n", "    iters, energies = [], []\n", "    for k, _ in enumerate(vmc.iter(n_iter), start=1):\n", "        s = vs.expect(H)\n", "        e = s.mean if hasattr(s, 'mean') else s[0]\n", "        iters.append(k)\n", "        energies.append(_np.asarray(e))\n", "    return _np.array(iters), _np.array(energies)\n", "\n", "iters_gat,  ener_gat  = run_collect(vmc_gat,  vs_gat,  H, 5)\n", "iters_gcnn, ener_gcnn = run_collect(vmc_gcnn, vs_gcnn, H, 5)\n", "\n", "plt.figure(figsize=(7,4))\n", "plt.plot(iters_gat, ener_gat, label=\"Molecular GAT\")\n", "plt.plot(iters_gcnn, ener_gcnn, label=\"PolyGCNN (surrogate)\")\n", "# exact line omitted here because placeholder H uses a surrogate; add once real molecular Hamiltonian is wired\n", "plt.xlabel(\"Iteration\")\n", "plt.ylabel(\"Energy (arb.)\")\n", "plt.legend()\n", "plt.title(\"Molecules (H2 minimal): GAT vs PolyGCNN (setup)\")\n", "plt.tight_layout()\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "quantum", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 2}