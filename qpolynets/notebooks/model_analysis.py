"""
Model Analysis Utilities for Neural vs Polynomial Jastrow Comparison

This module contains utility functions for analyzing, comparing, and managing
neural network and polynomial Jastrow factors in quantum Monte Carlo calculations.
"""

import sys
import os
import time
import pickle
import json
import numpy as np
import jax
import jax.numpy as jnp
from jax import random
import matplotlib.pyplot as plt

from typing import Dict, Any, Tuple, Optional

# PyTC imports for training (will be imported in functions to avoid import errors)
# from pytc.autodiff.mcmc import optimize_ref_var, optimize, sample
# from pytc.autodiff.mcmc_utils import analyze_energies

# PySCF for quantum chemistry
from pyscf import gto, scf, fci

# PyTC imports
from pytc.autodiff.jastrow import (
    NeuralEN, NeuralEE, NeuralEEN, 
    CPJastrowEN, CPJastrowEE, CPJastrowEEN,
    NuclearCusp, CompositeJastrow
)
from pytc.autodiff.ansatz.sj import SlaterJastrow
from pytc.autodiff.ansatz.det import SlaterDet

# Activation function library
ACTIVATION_FUNCTIONS = {
    'tanh': jnp.tanh,
    'relu': jax.nn.relu,
    'gelu': jax.nn.gelu,
    'swish': jax.nn.swish,
    'sigmoid': jax.nn.sigmoid,
    'elu': jax.nn.elu,
    'leaky_relu': lambda x: jax.nn.leaky_relu(x, 0.01),
    'identity': lambda x: x,  # No activation
    'softplus': jax.nn.softplus,
    'selu': jax.nn.selu,
}

def get_activation_function(name):
    """Get activation function by name or return the function if already callable."""
    if isinstance(name, str):
        if name not in ACTIVATION_FUNCTIONS:
            raise ValueError(f"Unknown activation: {name}. Available: {list(ACTIVATION_FUNCTIONS.keys())}")
        return ACTIVATION_FUNCTIONS[name]
    else:
        return name  # Assume it's already a function

# Individual component configuration presets for different interaction types
INDIVIDUAL_CONFIG_PRESETS = {
    "simple_molecule": {
        "en_config": {
            "degree": 2, "rank": 3, "cp_variant": "CP",
            "activation_between_layers": "tanh", "final_activation": "tanh"
        },
        "ee_config": {
            "degree": 2, "rank": 4, "cp_variant": "CP",
            "activation_between_layers": "tanh", "final_activation": "tanh"
        },
        "een_config": {
            "degree": 3, "rank": 6, "cp_variant": "CP",
            "activation_between_layers": "tanh", "final_activation": "tanh"
        }
    },
    "complex_molecule": {
        "en_config": {
            "degree": 3, "rank": 6, "cp_variant": "CP",
            "activation_between_layers": "gelu", "final_activation": "tanh"
        },
        "ee_config": {
            "degree": 3, "rank": 8, "cp_variant": "CP_sparse_LU",
            "activation_between_layers": "swish", "final_activation": "tanh"
        },
        "een_config": {
            "degree": 4, "rank": 12, "cp_variant": "CP_sparse_degree_sawtooth",
            "activation_between_layers": "gelu", "final_activation": "tanh"
        }
    },
    "rank_analysis": {
        "en_config": {
            "degree": 2, "rank": 2, "cp_variant": "CP",
            "activation_between_layers": "tanh", "final_activation": "tanh"
        },
        "ee_config": {
            "degree": 2, "rank": 2, "cp_variant": "CP",
            "activation_between_layers": "tanh", "final_activation": "tanh"
        },
        "een_config": {
            "degree": 2, "rank": 2, "cp_variant": "CP",
            "activation_between_layers": "tanh", "final_activation": "tanh"
        }
    }
}


def count_parameters(params):
    """Count total parameters in a parameter structure (handles Flax structure)."""
    if isinstance(params, dict):
        total = 0
        for key, value in params.items():
            if key == 'net_vars':  # Neural network parameters
                # Flax stores parameters under 'params' key
                if isinstance(value, dict) and 'params' in value:
                    total += count_parameters(value['params'])
                else:
                    total += count_parameters(value)
            elif key == 'U':  # CP U matrices
                total += sum(u.size for u in value)
            elif key == 'masks':  # Skip masks - they're not trainable parameters
                continue
            elif hasattr(value, 'size'):  # JAX array
                total += value.size
            elif isinstance(value, (list, tuple)):
                total += sum(count_parameters(v) for v in value)
            elif isinstance(value, dict):
                total += count_parameters(value)
        return total
    elif hasattr(params, 'size'):  # JAX array
        return params.size
    elif isinstance(params, (list, tuple)):
        return sum(count_parameters(p) for p in params)
    else:
        return 0


def get_parameter_info(params, jastrow_instance=None, path=""):
    """Extract detailed parameter information for analysis.

    Args:
        params: Parameter structure (dict or other)
        jastrow_instance: Optional Jastrow instance for mask information
        path: Current path in parameter tree (for debugging)

    Returns:
        dict: {
            'total_count': int,
            'elements': [
                {
                    'path': str,
                    'shape': tuple,
                    'total_elements': int,
                    'nonzero_elements': int,
                    'effective_elements': int,
                    'mask_info': dict or None
                }
            ]
        }
    """
    elements = []

    if isinstance(params, dict):
        for key, value in params.items():
            current_path = f"{path}.{key}" if path else key

            if key == 'net_vars':  # Neural network parameters
                if isinstance(value, dict) and 'params' in value:
                    sub_info = get_parameter_info(value['params'], jastrow_instance, f"{current_path}.params")
                else:
                    sub_info = get_parameter_info(value, jastrow_instance, current_path)
                elements.extend(sub_info['elements'])

            elif key == 'U':  # CP U matrices
                masks = None
                if jastrow_instance is not None and hasattr(jastrow_instance, 'masks') and jastrow_instance.masks is not None:
                    masks = jastrow_instance.masks

                for i, u in enumerate(value):
                    u_path = f"{current_path}[{i}]"
                    total_elements = u.size
                    nonzero_elements = int(jnp.sum(u != 0))

                    # Calculate effective elements (accounting for masks)
                    if masks is not None and i < len(masks):
                        mask = masks[i]
                        mask_allows = int(jnp.sum(mask != 0))
                        effective_nonzero = int(jnp.sum((u != 0) & (mask != 0)))
                        effective_elements = mask_allows  # Computational complexity

                        mask_info = {
                            'has_mask': True,
                            'mask_allows': mask_allows,
                            'mask_sparsity': 1.0 - (mask_allows / total_elements),
                            'effective_nonzero': effective_nonzero
                        }
                    else:
                        effective_elements = nonzero_elements
                        mask_info = {'has_mask': False}

                    elements.append({
                        'path': u_path,
                        'shape': u.shape,
                        'total_elements': total_elements,
                        'nonzero_elements': nonzero_elements,
                        'effective_elements': effective_elements,
                        'mask_info': mask_info
                    })

            elif key == 'masks':
                # Skip masks - they're not trainable parameters
                continue

            elif hasattr(value, 'size'):  # JAX array
                total_elements = value.size
                nonzero_elements = int(jnp.sum(value != 0))
                elements.append({
                    'path': current_path,
                    'shape': value.shape,
                    'total_elements': total_elements,
                    'nonzero_elements': nonzero_elements,
                    'effective_elements': nonzero_elements,  # No masking for non-U parameters
                    'mask_info': {'has_mask': False}
                })

            elif isinstance(value, (list, tuple)):
                for i, v in enumerate(value):
                    sub_info = get_parameter_info(v, jastrow_instance, f"{current_path}[{i}]")
                    elements.extend(sub_info['elements'])

            elif isinstance(value, dict):
                sub_info = get_parameter_info(value, jastrow_instance, current_path)
                elements.extend(sub_info['elements'])

    elif hasattr(params, 'size'):  # JAX array
        total_elements = params.size
        nonzero_elements = int(jnp.sum(params != 0))
        elements.append({
            'path': path or 'root',
            'shape': params.shape,
            'total_elements': total_elements,
            'nonzero_elements': nonzero_elements,
            'effective_elements': nonzero_elements,
            'mask_info': {'has_mask': False}
        })

    elif isinstance(params, (list, tuple)):
        for i, p in enumerate(params):
            sub_info = get_parameter_info(p, jastrow_instance, f"{path}[{i}]" if path else f"[{i}]")
            elements.extend(sub_info['elements'])

    total_count = sum(elem['total_elements'] for elem in elements)

    return {
        'total_count': total_count,
        'elements': elements
    }


def count_nonzero_parameters(params, jastrow_instance=None):
    """Count non-zero parameters - built on get_parameter_info."""
    info = get_parameter_info(params, jastrow_instance)
    return sum(elem['nonzero_elements'] for elem in info['elements'])


def count_effective_parameters(params, jastrow_instance=None):
    """Count effective parameters - built on get_parameter_info."""
    info = get_parameter_info(params, jastrow_instance)
    total = sum(elem['total_elements'] for elem in info['elements'])
    effective = sum(elem['effective_elements'] for elem in info['elements'])
    return {
        'total_parameters': total,
        'effective_parameters': effective,
        'sparsity_ratio': 1.0 - (effective / total) if total > 0 else 0.0
    }


def analyze_full_ansatz_parameters(combined_params, jastrow_instances=None):
    """Analyze parameters for complete Slater-Jastrow ansatz.

    Args:
        combined_params: (composite_params, coeffs) from create_full_ansatz
        jastrow_instances: dict with component instances for mask access

    Returns:
        dict: Comprehensive analysis of full ansatz
    """
    composite_params, coeffs = combined_params

    # Analyze each component
    components = ['ncusp', 'en', 'ee', 'een']
    analysis = {
        'total_ansatz_parameters': count_parameters(combined_params),
        'components': {},
        'summary': {}
    }

    for i, (component_name, params) in enumerate(zip(components, composite_params)):
        instance = jastrow_instances.get(component_name) if jastrow_instances else None

        component_analysis = {
            'total': count_parameters(params),
            'nonzero': count_nonzero_parameters(params, instance),
            'effective_analysis': count_effective_parameters(params, instance),
            'sparsity_info': analyze_parameter_sparsity(params, instance, component_name)
        }

        analysis['components'][component_name] = component_analysis

    # Add coefficients
    analysis['components']['coefficients'] = {
        'total': count_parameters(coeffs),
        'nonzero': count_nonzero_parameters(coeffs),
        'effective_analysis': count_effective_parameters(coeffs)
    }

    # Summary statistics
    total_effective = sum(comp['effective_analysis']['effective_parameters']
                         for comp in analysis['components'].values())

    analysis['summary'] = {
        'total_parameters': analysis['total_ansatz_parameters'],
        'total_effective': total_effective,
        'overall_sparsity': 1.0 - (total_effective / analysis['total_ansatz_parameters']) if analysis['total_ansatz_parameters'] > 0 else 0.0,
        'component_breakdown': {name: comp['total']
                               for name, comp in analysis['components'].items()}
    }

    return analysis


def analyze_parameter_sparsity(params, jastrow_instance=None, model_name="Model"):
    """Comprehensive parameter analysis for sparse and non-sparse models.

    Args:
        params: Parameter structure
        jastrow_instance: Optional Jastrow instance for mask information
        model_name: Name for display purposes

    Returns:
        dict: Comprehensive parameter analysis
    """
    # Basic counts
    total_stored = count_parameters(params)

    # For models with sparsity masks
    if jastrow_instance is not None and hasattr(jastrow_instance, 'masks') and jastrow_instance.masks is not None:
        effective_analysis = count_effective_parameters(params, jastrow_instance)
        computational_nonzero = count_nonzero_parameters(params, jastrow_instance)

        # Extract U matrix specific analysis
        u_analysis = {}
        if isinstance(params, dict) and 'U' in params:
            u_matrices = params['U']
            masks = jastrow_instance.masks

            for i, (u, mask) in enumerate(zip(u_matrices, masks)):
                u_total = u.size
                u_nonzero_stored = int(jnp.sum(u != 0))
                u_mask_allows = int(jnp.sum(mask != 0))
                u_effective_nonzero = int(jnp.sum((u != 0) & (mask != 0)))

                u_analysis[f'U{i}'] = {
                    'total_elements': u_total,
                    'stored_nonzero': u_nonzero_stored,
                    'mask_allows': u_mask_allows,
                    'effective_nonzero': u_effective_nonzero,
                    'mask_sparsity': 1.0 - (u_mask_allows / u_total),
                    'effective_sparsity': 1.0 - (u_effective_nonzero / u_total)
                }

        return {
            'model_name': model_name,
            'total_stored_parameters': total_stored,
            'effective_parameters': effective_analysis['effective_parameters'],
            'computational_nonzero': computational_nonzero,
            'overall_sparsity': effective_analysis['sparsity_ratio'],
            'parameter_reduction': (1 - effective_analysis['sparsity_ratio']) * 100,
            'has_sparsity': True,
            'u_matrix_analysis': u_analysis,
            'masks_count': len(jastrow_instance.masks) if jastrow_instance.masks else 0
        }
    else:
        # Non-sparse model
        stored_nonzero = count_nonzero_parameters(params)

        return {
            'model_name': model_name,
            'total_stored_parameters': total_stored,
            'effective_parameters': total_stored,
            'computational_nonzero': stored_nonzero,
            'overall_sparsity': 0.0,
            'parameter_reduction': 0.0,
            'has_sparsity': False,
            'u_matrix_analysis': {},
            'masks_count': 0
        }


def setup_molecule(molecule_name: str, basis: str = None):
    """Setup molecule with quantum chemistry calculations.
    
    Args:
        molecule_name: Name of the molecule ('H2', 'HeH+')
        basis: Basis set to use (e.g., 'sto-3g', 'cc-pvdz', 'cc-pvtz')
               If None, uses default basis for each molecule
    """
    if molecule_name == "H2":
        default_basis = 'cc-pvtz'
        mol = gto.M(atom='H 0 0 0; H 0 0 1.4', 
                   basis=basis or default_basis, 
                   unit='bohr', verbose=0)
    elif molecule_name == "HeH+":
        default_basis = 'sto-3g'
        mol = gto.M(atom='He 0 0 0; H 0 0 1.463', 
                   basis=basis or default_basis, 
                   unit='bohr', charge=1, verbose=0)
    else:
        raise ValueError(f"Unknown molecule: {molecule_name}")
    
    # Run HF calculation
    mf = scf.RHF(mol)
    mf.kernel()
    
    print(f"{molecule_name} molecule:")
    print(f"  Number of electrons: {mol.nelectron}")
    print(f"  Number of atoms: {mol.natm}")
    print(f"  HF energy: {mf.e_tot:.6f} Hartree")
    print(f"Basis set: {mol.basis}")
    
    return mol, mf


def calculate_exact_ground_state(mol, mf) -> Optional[float]:
    """Calculate exact ground state energy using FCI."""
    try:
        # Run Full Configuration Interaction
        cisolver = fci.FCI(mf)
        fci_energy, ci_vector = cisolver.kernel()
        print(f"  FCI energy: {fci_energy:.6f} Hartree")
        return fci_energy
    except Exception as e:
        print(f"  FCI calculation failed: {e}")
        return None


def create_full_ansatz(mol, mf, jastrow_type: str, **kwargs):
    """Create complete Slater-Jastrow ansatz with all components.

    Returns:
        Tuple of (sj_ansatz, combined_params, composite_params, jastrow_instances)
    """
    key = random.PRNGKey(42)

    # Always include nuclear cusp for stability
    ncusp = NuclearCusp(mol)
    params_ncusp = ncusp.init_params()

    if jastrow_type == "neural":
        # Neural Jastrow components
        layer_widths = kwargs.get('layer_widths', [8, 8])
        neural_en = NeuralEN(mol, layer_widths=layer_widths)
        neural_ee = NeuralEE(mol, layer_widths=layer_widths)
        neural_een = NeuralEEN(mol, layer_widths=layer_widths)

        params_en = neural_en.init_params(key=key)
        params_ee = neural_ee.init_params(key=key)
        params_een = neural_een.init_params(key=key)

        composite = CompositeJastrow([ncusp, neural_en, neural_ee, neural_een])
        composite_params = [params_ncusp, params_en, params_ee, params_een]

        # Return instances for mask access
        jastrow_instances = {
            'ncusp': ncusp,
            'en': neural_en,
            'ee': neural_ee,
            'een': neural_een
        }

    elif jastrow_type == "polynomial":
        # Check if using per-component configurations
        en_config = kwargs.get('en_config', {})
        ee_config = kwargs.get('ee_config', {})
        een_config = kwargs.get('een_config', {})

        # Check if using individual config presets
        preset = kwargs.get('individual_preset', None)
        if preset and preset in INDIVIDUAL_CONFIG_PRESETS:
            preset_configs = INDIVIDUAL_CONFIG_PRESETS[preset]
            en_config = {**preset_configs['en_config'], **en_config}
            ee_config = {**preset_configs['ee_config'], **ee_config}
            een_config = {**preset_configs['een_config'], **een_config}

        # Global defaults (backward compatibility)
        global_defaults = {
            'degree': kwargs.get('degree', 4),
            'rank': kwargs.get('rank', 8),
            'cp_variant': kwargs.get('cp_variant', 'CP'),
            'activation_scale': kwargs.get('activation_scale', 0.1),
            'use_final_activation': kwargs.get('use_final_activation', True),
            'use_kfac': kwargs.get('use_kfac', False),
            'sawtooth_l_offset': kwargs.get('sawtooth_l_offset', 0),
            'sawtooth_u_offset': kwargs.get('sawtooth_u_offset', 0),
            'hidden_dims': kwargs.get('hidden_dims', None),
            'use_activation_between_layers': kwargs.get('use_activation_between_layers', False),
            'activation_between_layers': get_activation_function(kwargs.get('activation_between_layers', 'tanh')),
            'final_activation': get_activation_function(kwargs.get('final_activation', 'tanh')),
            'final_activation_scale': kwargs.get('final_activation_scale', None)
        }

        # Determine if using individual configs or backward compatibility mode
        using_individual_configs = bool(en_config or ee_config or een_config or preset)

        if using_individual_configs:
            # Individual component defaults (when using new system)
            en_defaults = {**global_defaults, 'degree': 2, 'rank': 4, 'cp_variant': 'CP'}
            ee_defaults = {**global_defaults, 'degree': 3, 'rank': 6, 'cp_variant': 'CP'}
            een_defaults = {**global_defaults, 'degree': 4, 'rank': 8, 'cp_variant': 'CP'}
        else:
            # Backward compatibility: use same config for all components
            en_defaults = global_defaults
            ee_defaults = global_defaults
            een_defaults = global_defaults

        # Merge component-specific configurations
        def process_component_config(defaults, component_config):
            config = {**defaults, **component_config}
            # Convert activation function names to functions
            if 'activation_between_layers' in component_config:
                config['activation_between_layers'] = get_activation_function(component_config['activation_between_layers'])
            if 'final_activation' in component_config:
                config['final_activation'] = get_activation_function(component_config['final_activation'])
            return config

        en_params = process_component_config(en_defaults, en_config)
        ee_params = process_component_config(ee_defaults, ee_config)
        een_params = process_component_config(een_defaults, een_config)

        # Create components with individual configurations
        cp_en = CPJastrowEN(mol, **en_params)
        cp_ee = CPJastrowEE(mol, **ee_params)
        cp_een = CPJastrowEEN(mol, **een_params)

        params_en = cp_en.init_params(key=key)
        params_ee = cp_ee.init_params(key=key)
        params_een = cp_een.init_params(key=key)

        composite = CompositeJastrow([ncusp, cp_en, cp_ee, cp_een])
        composite_params = [params_ncusp, params_en, params_ee, params_een]

        # Return instances for mask access
        jastrow_instances = {
            'ncusp': ncusp,
            'en': cp_en,
            'ee': cp_ee,
            'een': cp_een
        }

    else:
        raise ValueError(f"Unknown jastrow_type: {jastrow_type}")

    # Create Slater-Jastrow ansatz
    det = SlaterDet(mol, mf.mo_coeff)
    sj_ansatz = SlaterJastrow(mol, composite, [det])

    # Prepare parameters for VMC
    combined_params = (composite_params, jnp.array([1.0]))

    return sj_ansatz, combined_params, composite_params, jastrow_instances


def analyze_parameter_counts(mol, molecule_name: str, neural_layers: list, poly_degree: int, poly_rank: int) -> Dict[str, int]:
    """Analyze parameter counts for different Jastrow configurations."""
    print(f"\n=== Parameter Count Analysis for {molecule_name} ===")
    
    key = random.PRNGKey(42)
    results = {}
    
    # Test configurations - individual components
    configs = [
        (f"Neural EN {neural_layers}", lambda: NeuralEN(mol, layer_widths=neural_layers)),
        (f"Neural EE {neural_layers}", lambda: NeuralEE(mol, layer_widths=neural_layers)),
        (f"Neural EEN {neural_layers}", lambda: NeuralEEN(mol, layer_widths=neural_layers)),
        (f"CP EN (deg={poly_degree}, rank={poly_rank})", lambda: CPJastrowEN(mol, degree=poly_degree, rank=poly_rank)),
        (f"CP EE (deg={poly_degree}, rank={poly_rank})", lambda: CPJastrowEE(mol, degree=poly_degree, rank=poly_rank)),
        (f"CP EEN (deg={poly_degree}, rank={poly_rank})", lambda: CPJastrowEEN(mol, degree=poly_degree, rank=poly_rank)),
        (f"CP EEN sparse_LU", lambda: CPJastrowEEN(mol, degree=poly_degree, rank=poly_rank, cp_variant='CP_sparse_LU')),
    ]
    
    for name, jastrow_fn in configs:
        jastrow = jastrow_fn()
        params = jastrow.init_params(key=key)
        param_count = count_parameters(params)
        results[name] = param_count
        print(f"  {name}: {param_count} parameters")
    
    return results


def extract_neural_weights(params):
    """Extract weights from neural Jastrow parameters."""
    weights = {}

    # Extract from composite parameters structure
    composite_params = params[0]  # First element contains Jastrow parameters

    for i, component_params in enumerate(composite_params):
        if i == 0:  # Nuclear cusp
            weights['nuclear_cusp'] = {
                'rc': component_params.get('rc', None),
                'X4': component_params.get('X4', None)
            }
        elif 'net_vars' in component_params:  # Neural components
            component_name = f'neural_component_{i}'
            net_params = component_params['net_vars']['params']

            weights[component_name] = {
                'decay_param': component_params.get('rc_en_raw', component_params.get('rc_ee_raw', None)),
                'layers': {}
            }

            # Extract layer weights
            for layer_name, layer_params in net_params.items():
                if 'kernel' in layer_params:
                    weights[component_name]['layers'][layer_name] = {
                        'kernel': layer_params['kernel'],
                        'bias': layer_params.get('bias', None),
                        'kernel_shape': layer_params['kernel'].shape,
                        'kernel_norm': jnp.linalg.norm(layer_params['kernel']),
                        'kernel_mean': jnp.mean(layer_params['kernel']),
                        'kernel_std': jnp.std(layer_params['kernel'])
                    }

    return weights


def extract_polynomial_weights(params):
    """Extract weights from polynomial Jastrow parameters."""
    weights = {}

    # Extract from composite parameters structure
    composite_params = params[0]  # First element contains Jastrow parameters

    for i, component_params in enumerate(composite_params):
        if i == 0:  # Nuclear cusp
            weights['nuclear_cusp'] = {
                'rc': component_params.get('rc', None),
                'X4': component_params.get('X4', None)
            }
        elif 'U' in component_params:  # Polynomial components
            component_name = f'polynomial_component_{i}'

            weights[component_name] = {
                'degree': component_params.get('degree', None),
                'decay_params': {
                    'rc_en_raw': component_params.get('rc_en_raw', None),
                    'rc_ee_raw': component_params.get('rc_ee_raw', None)
                },
                'U_matrices': {},
                'C_vector': {
                    'values': component_params['C'],
                    'shape': component_params['C'].shape,
                    'norm': jnp.linalg.norm(component_params['C']),
                    'mean': jnp.mean(component_params['C']),
                    'std': jnp.std(component_params['C'])
                },
                'bias': component_params.get('bias', None)
            }

            # Extract U matrices
            for j, U_matrix in enumerate(component_params['U']):
                weights[component_name]['U_matrices'][f'U{j+1}'] = {
                    'values': U_matrix,
                    'shape': U_matrix.shape,
                    'norm': jnp.linalg.norm(U_matrix),
                    'mean': jnp.mean(U_matrix),
                    'std': jnp.std(U_matrix),
                    'rank': U_matrix.shape[1] if len(U_matrix.shape) > 1 else 1
                }

    return weights


def analyze_weight_statistics(weights, model_type: str):
    """Analyze and print weight statistics."""
    print(f"\n=== {model_type.upper()} WEIGHT ANALYSIS ===")

    for component_name, component_data in weights.items():
        if component_name == 'nuclear_cusp':
            print(f"\n{component_name.upper()}:")
            print(f"  rc: {component_data['rc']}")
            print(f"  X4: {component_data['X4']}")

        elif 'neural' in component_name:
            print(f"\n{component_name.upper()}:")
            print(f"  Decay parameter: {component_data['decay_param']}")

            for layer_name, layer_data in component_data['layers'].items():
                print(f"  {layer_name}:")
                print(f"    Shape: {layer_data['kernel_shape']}")
                print(f"    Norm: {layer_data['kernel_norm']:.6f}")
                print(f"    Mean: {layer_data['kernel_mean']:.6f}")
                print(f"    Std:  {layer_data['kernel_std']:.6f}")

        elif 'polynomial' in component_name:
            print(f"\n{component_name.upper()}:")
            print(f"  Degree: {component_data['degree']}")
            print(f"  Decay params: rc_en={component_data['decay_params']['rc_en_raw']}, rc_ee={component_data['decay_params']['rc_ee_raw']}")
            print(f"  Bias: {component_data['bias']}")

            print(f"  C vector:")
            print(f"    Shape: {component_data['C_vector']['shape']}")
            print(f"    Norm: {component_data['C_vector']['norm']:.6f}")
            print(f"    Mean: {component_data['C_vector']['mean']:.6f}")
            print(f"    Std:  {component_data['C_vector']['std']:.6f}")

            print(f"  U matrices:")
            for u_name, u_data in component_data['U_matrices'].items():
                print(f"    {u_name}: shape={u_data['shape']}, norm={u_data['norm']:.6f}, mean={u_data['mean']:.6f}, std={u_data['std']:.6f}")


def compare_weight_magnitudes(neural_weights, poly_weights):
    """Compare weight magnitudes between neural and polynomial models."""
    print("\n=== WEIGHT MAGNITUDE COMPARISON ===")

    # Compare similar components
    neural_components = [k for k in neural_weights.keys() if 'neural' in k]
    poly_components = [k for k in poly_weights.keys() if 'polynomial' in k]

    print(f"\nNeural components: {len(neural_components)}")
    print(f"Polynomial components: {len(poly_components)}")

    # Calculate total parameter norms
    neural_total_norm = 0
    for comp_name in neural_components:
        for layer_data in neural_weights[comp_name]['layers'].values():
            neural_total_norm += layer_data['kernel_norm']**2
    neural_total_norm = jnp.sqrt(neural_total_norm)

    poly_total_norm = 0
    for comp_name in poly_components:
        poly_total_norm += poly_weights[comp_name]['C_vector']['norm']**2
        for u_data in poly_weights[comp_name]['U_matrices'].values():
            poly_total_norm += u_data['norm']**2
    poly_total_norm = jnp.sqrt(poly_total_norm)

    print(f"\nTotal parameter norms:")
    print(f"  Neural: {neural_total_norm:.6f}")
    print(f"  Polynomial: {poly_total_norm:.6f}")
    print(f"  Ratio (Poly/Neural): {poly_total_norm/neural_total_norm:.3f}")


def save_trained_model(params, model_info: Dict[str, Any], filename: str):
    """Save trained model parameters and metadata."""
    # Convert JAX arrays to numpy for serialization
    def jax_to_numpy(obj):
        if hasattr(obj, 'shape'):  # JAX array
            return np.array(obj)
        elif isinstance(obj, dict):
            return {k: jax_to_numpy(v) for k, v in obj.items()}
        elif isinstance(obj, (list, tuple)):
            return [jax_to_numpy(item) for item in obj]
        else:
            return obj

    save_data = {
        'parameters': jax_to_numpy(params),
        'model_info': model_info,
        'timestamp': time.time()
    }

    with open(filename, 'wb') as f:
        pickle.dump(save_data, f)

    print(f"Model saved to {filename}")


def load_trained_model(filename: str):
    """Load trained model parameters and metadata."""
    with open(filename, 'rb') as f:
        save_data = pickle.load(f)

    # Convert numpy arrays back to JAX
    def numpy_to_jax(obj):
        if isinstance(obj, np.ndarray):
            return jnp.array(obj)
        elif isinstance(obj, dict):
            return {k: numpy_to_jax(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [numpy_to_jax(item) for item in obj]
        else:
            return obj

    params = numpy_to_jax(save_data['parameters'])
    model_info = save_data['model_info']

    print(f"Model loaded from {filename}")
    print(f"Model info: {model_info}")

    return params, model_info


def save_weight_analysis(weights, filename: str):
    """Save weight analysis results as JSON."""
    # Convert JAX arrays to lists for JSON serialization
    def jax_to_json(obj):
        if hasattr(obj, 'tolist'):  # JAX/numpy array
            return obj.tolist()
        elif hasattr(obj, 'item'):  # Scalar
            return obj.item()
        elif isinstance(obj, dict):
            return {k: jax_to_json(v) for k, v in obj.items()}
        elif isinstance(obj, (list, tuple)):
            return [jax_to_json(item) for item in obj]
        else:
            return obj

    json_weights = jax_to_json(weights)

    with open(filename, 'w') as f:
        json.dump(json_weights, f, indent=2)

    print(f"Weight analysis saved to {filename}")


def save_training_checkpoint(params, step: int, energy: float, model_info: Dict[str, Any], base_filename: str):
    """Save training checkpoint with step and energy information."""
    checkpoint_info = model_info.copy()
    checkpoint_info.update({
        'step': step,
        'energy': energy,
        'checkpoint_time': time.time()
    })

    filename = f"{base_filename}_step_{step}.pkl"
    save_trained_model(params, checkpoint_info, filename)
    return filename


def get_latest_checkpoint(base_filename: str, max_step: int = None) -> Optional[str]:
    """Find the latest checkpoint file for a given base filename."""
    import glob

    pattern = f"{base_filename}_step_*.pkl"
    checkpoint_files = glob.glob(pattern)

    if not checkpoint_files:
        return None

    # Extract step numbers and find the latest
    step_files = []
    for file in checkpoint_files:
        try:
            step = int(file.split('_step_')[1].split('.pkl')[0])
            if max_step is None or step <= max_step:
                step_files.append((step, file))
        except (ValueError, IndexError):
            continue

    if not step_files:
        return None

    # Return the file with the highest step number
    latest_step, latest_file = max(step_files, key=lambda x: x[0])
    return latest_file


def create_model_summary(params, model_type: str, **kwargs) -> Dict[str, Any]:
    """Create a summary dictionary for a model."""
    return {
        'type': model_type,
        'parameter_count': count_parameters(params),
        'creation_time': time.time(),
        **kwargs
    }


def train_and_compare(mol, mf, fci_energy, molecule_name, configs,
                     n_opt_steps=200, learning_rate=0.0003,
                     optimizer_type="adam", opt_kwargs=None,
                     opt_n_walkers=500, opt_n_steps=1000, burn_in_steps=200,
                     eval_n_walkers=128, eval_n_steps=200, step_size=0.02, thinning=2):
    """Train and compare neural vs polynomial Jastrow factors with convergence analysis.

    Args:
        mol: PySCF molecule object
        mf: Mean field object from PySCF
        fci_energy: FCI reference energy (can be None)
        molecule_name: Name of the molecule for display
        configs: List of (name, jastrow_type, kwargs) tuples for different configurations
        n_opt_steps: Number of optimization steps
        learning_rate: Learning rate for optimization
        optimizer_type: Type of optimizer ("adam", "sgd", "rmsprop", "lion", "kfac")
        opt_kwargs: Additional optimizer parameters (dict)
        opt_n_walkers: Number of walkers for optimization
        opt_n_steps: MCMC steps per optimization iteration
        burn_in_steps: Burn-in steps for MCMC
        eval_n_walkers: Number of walkers for evaluation
        eval_n_steps: MCMC steps for evaluation
        step_size: MCMC step size
        thinning: Thinning factor for MCMC

    Returns:
        Dictionary with training results for each configuration
    """
    # Import PyTC modules here to avoid import errors if not available
    try:
        from pytc.autodiff.mcmc import optimize_ref_var, optimize, sample
        from pytc.autodiff.mcmc_utils import analyze_energies
        from pytc.autodiff.jastrow import (
            NeuralEN, NeuralEE, NeuralEEN,
            CPJastrowEN, CPJastrowEE, CPJastrowEEN,
            NuclearCusp, CompositeJastrow
        )
        from pytc.autodiff.ansatz.sj import SlaterJastrow
        from pytc.autodiff.ansatz.det import SlaterDet
    except ImportError as e:
        raise ImportError(f"PyTC modules not available: {e}")

    print(f"\n=== Training Comparison for {molecule_name} ===")
    if fci_energy is not None:
        print(f"Reference FCI energy: {fci_energy:.6f} Hartree")
        print(f"Reference HF energy:  {mf.e_tot:.6f} Hartree")

    print(f"Optimizer: {optimizer_type}")
    if opt_kwargs:
        print(f"Optimizer kwargs: {opt_kwargs}")

    results = {}

    for name, jastrow_type, kwargs in configs:
        print(f"\n{'='*60}")
        print(f"Training {name}...")
        print(f"{'='*60}")

        try:
            # Create ansatz
            sj_ansatz, combined_params, _, _ = create_full_ansatz(mol, mf, jastrow_type, **kwargs)

            # Count parameters
            param_count = count_nonzero_parameters(combined_params)
            print(f"Parameters: {param_count}")

            # Initial energy evaluation
            key = random.PRNGKey(123)
            key, subkey = random.split(key)

            print("\nInitial energy evaluation...")
            initial_results = sample(
                sj_ansatz,
                params=combined_params,
                key=subkey,
                n_walkers=eval_n_walkers,
                n_steps=100,  # Shorter for initial eval
                step_size=step_size,
                burn_in_steps=50,
                use_importance_sampling=True,
                thinning=thinning,
            )

            initial_stats = analyze_energies(initial_results)
            initial_energy = float(initial_stats['mean'])
            print(f"Initial energy: {initial_energy:.6f} Ha")

            # Clean parameters to ensure all are float for gradient computation
            def clean_params_recursive(params):
                """Recursively convert any integer parameters to float."""
                if isinstance(params, dict):
                    cleaned = {}
                    for key, value in params.items():
                        if isinstance(value, int):
                            cleaned[key] = float(value)
                        elif hasattr(value, 'dtype') and not jnp.issubdtype(value.dtype, jnp.inexact):
                            # Convert integer arrays to float
                            cleaned[key] = value.astype(jnp.float64)
                        elif isinstance(value, (dict, list, tuple)):
                            cleaned[key] = clean_params_recursive(value)
                        else:
                            cleaned[key] = value
                    return cleaned
                elif isinstance(params, (list, tuple)):
                    return type(params)([clean_params_recursive(p) for p in params])
                elif hasattr(params, 'dtype') and not jnp.issubdtype(params.dtype, jnp.inexact):
                    return params.astype(jnp.float64)
                else:
                    return params

            # Clean parameters before optimization
            combined_params = clean_params_recursive(combined_params)

            # Optimization - try for all methods
            print(f"\nStarting optimization ({n_opt_steps} steps)...")
            key, subkey = random.split(key)

            try:
                # Optimization using energy minimization
                opt_results = optimize(
                    sj_ansatz,
                    params=combined_params,
                    n_walkers=opt_n_walkers,
                    n_steps=opt_n_steps,
                    step_size=step_size,
                    burn_in_steps=burn_in_steps,
                    n_opt_steps=n_opt_steps,
                    learning_rate=learning_rate,
                    optimizer_type=optimizer_type,
                    opt_kwargs=opt_kwargs,
                    key=subkey
                )

                # Extract optimization history
                energy_history = opt_results["energies"]
                acceptance_history = opt_results["acceptance"]
                optimized_params = opt_results["params"][-1]
                can_optimize = True

                print(f"Optimization completed! ({len(energy_history)} steps)")
                if len(energy_history) >= 10:
                    initial_avg = jnp.mean(jnp.array(energy_history[:10]))
                    final_avg = jnp.mean(jnp.array(energy_history[-10:]))
                    print(f"Energy improvement: {initial_avg - final_avg:.6f} Ha")

            except Exception as opt_error:
                print(f"\nOptimization failed: {opt_error}")
                print("Using initial parameters for evaluation")
                optimized_params = combined_params
                energy_history = [initial_energy] * n_opt_steps  # Flat line for plotting
                acceptance_history = [0.85] * n_opt_steps  # Dummy acceptance rate
                can_optimize = False

            # Final evaluation with optimized parameters
            print("\nFinal energy evaluation...")
            key, subkey = random.split(key)

            final_results = sample(
                sj_ansatz,
                params=optimized_params,
                key=subkey,
                n_walkers=eval_n_walkers,
                n_steps=eval_n_steps,
                step_size=step_size,
                burn_in_steps=burn_in_steps//2,
                use_importance_sampling=True,
                thinning=thinning,
            )

            final_stats = analyze_energies(final_results)
            final_energy = float(final_stats['mean'])
            final_error = float(final_stats['error'])
            final_acceptance = float(jnp.mean(final_results['acceptance_rates']))

            # Use converged energy (average of last 10% of optimization) for better statistics
            if len(energy_history) > 10:
                final_portion = energy_history[int(0.9*len(energy_history)):]
                converged_energy = float(jnp.mean(jnp.array(final_portion)))
                converged_std = float(jnp.std(jnp.array(final_portion)))

                # Use converged energy if it's more stable and physically reasonable
                if converged_std < final_error and converged_energy > fci_energy - 0.001:  # Sanity check
                    final_energy = converged_energy
                    final_error = converged_std

            results[name] = {
                'initial_energy': initial_energy,
                'final_energy': final_energy,
                'final_error': final_error,
                'final_acceptance': final_acceptance,
                'param_count': param_count,
                'optimised_params': optimized_params,
                'energy_history': energy_history,
                'acceptance_history': acceptance_history,
                'energy_improvement': initial_energy - final_energy,
                'hf_improvement': final_energy - mf.e_tot,
                'fci_diff': final_energy - fci_energy if fci_energy is not None else None,
                'efficiency': (final_energy - mf.e_tot) / param_count * 1000,  # mHa per parameter
                'optimized': can_optimize  # Flag to indicate if optimization was performed
            }

            print(f"Final energy: {final_energy:.6f} ± {final_error:.6f} Ha")
            print(f"HF improvement: {final_energy - mf.e_tot:.6f} Ha")
            if fci_energy is not None:
                print(f"FCI difference: {final_energy - fci_energy:.6f} Ha")
            print(f"Final acceptance: {final_acceptance:.3f}")
            print(f"Efficiency: {(final_energy - mf.e_tot)/param_count*1000:.3f} mHa/param")

        except Exception as e:
            print(f"  Error: {e}")
            import traceback
            traceback.print_exc()
            results[name] = None

    return results


# =============================================================================
# VISUALIZATION FUNCTIONS
# =============================================================================

def plot_training_comparison(training_results, mf, fci_energy=None, molecule_name="", save_path=None):
    """
    Create comprehensive training comparison plots.

    Args:
        training_results: Dictionary from train_and_compare function
        mf: Mean field object from PySCF
        fci_energy: FCI reference energy (optional)
        molecule_name: Name of the molecule for plot titles
        save_path: Optional path to save the figure
    """
    # Filter successful results
    successful_results = {name: result for name, result in training_results.items() if result is not None}

    if not successful_results:
        print("❌ No successful results to plot")
        return

    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

    # 1. Energy Convergence (Most Important)
    ax1.set_title(f"{molecule_name}: Energy Convergence")
    for name, result in successful_results.items():
        if 'energy_history' in result and result['energy_history'] is not None:
            energy_hist = np.array(result['energy_history'])  # Convert to numpy array
            if len(energy_hist) > 0:  # Check if not empty
                steps = range(len(energy_hist))
                ax1.plot(steps, energy_hist,
                        label=f"{name} ({result['param_count']} params)",
                        linewidth=2, marker='o', markersize=3, alpha=0.8)

    # Add reference lines
    ax1.axhline(y=mf.e_tot, color='red', linestyle='--', alpha=0.7, label='HF Energy')
    if fci_energy:
        ax1.axhline(y=fci_energy, color='green', linestyle='--', alpha=0.7, label='FCI Energy')

    ax1.set_xlabel('Optimization Steps')
    ax1.set_ylabel('Energy (Hartree)')
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    # 2. Final Energy Comparison
    ax2.set_title("Final Energy Comparison")
    names = list(successful_results.keys())
    final_energies = [float(result['final_energy']) for result in successful_results.values()]
    final_errors = [float(result['final_error']) for result in successful_results.values()]
    colors = ['lightcoral' if 'Neural' in name else 'lightblue' for name in names]

    bars = ax2.bar(range(len(names)), final_energies, yerr=final_errors,
                   capsize=5, color=colors, alpha=0.7)
    ax2.axhline(y=mf.e_tot, color='red', linestyle='--', label='HF Energy')
    if fci_energy:
        ax2.axhline(y=fci_energy, color='green', linestyle='--', label='FCI Energy')

    ax2.set_xticks(range(len(names)))
    ax2.set_xticklabels([name.replace(' ', '\n') for name in names], rotation=45, ha='right')
    ax2.set_ylabel('Energy (Hartree)')
    ax2.legend()
    ax2.grid(True, alpha=0.3)

    # 3. Parameter Efficiency
    ax3.set_title("Parameter Efficiency (mHa improvement per parameter)")
    param_counts = [int(result['param_count']) for result in successful_results.values()]
    efficiencies = [float(result['efficiency']) for result in successful_results.values()]

    scatter_colors = ['red' if 'Neural' in name else 'blue' for name in names]
    scatter = ax3.scatter(param_counts, efficiencies,
                         c=scatter_colors, s=100, alpha=0.7)

    for i, name in enumerate(names):
        ax3.annotate(name.split()[0], (param_counts[i], efficiencies[i]),
                    xytext=(5, 5), textcoords='offset points', fontsize=9)

    ax3.set_xlabel('Number of Parameters')
    ax3.set_ylabel('Efficiency (mHa/param)')
    ax3.grid(True, alpha=0.3)

    # 4. Acceptance Rate History
    ax4.set_title("MCMC Acceptance Rate")
    for name, result in successful_results.items():
        if 'acceptance_history' in result and result['acceptance_history'] is not None:
            acceptance_hist = np.array(result['acceptance_history'])  # Convert to numpy array
            if len(acceptance_hist) > 0:  # Check if not empty
                steps = range(len(acceptance_hist))
                ax4.plot(steps, acceptance_hist,
                        label=name, linewidth=2, alpha=0.8)

    ax4.set_xlabel('Optimization Steps')
    ax4.set_ylabel('Acceptance Rate')
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    ax4.set_ylim(0, 1)

    plt.tight_layout()

    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Plot saved to {save_path}")

    plt.show()


def plot_error_convergence(training_results, exact_energy, save_path=None):
    """
    Plot energy error convergence on log scale.

    Args:
        training_results: Dictionary from train_and_compare function
        exact_energy: Exact ground state energy (FCI or analytical)
        save_path: Optional path to save the figure
    """
    successful_results = {name: result for name, result in training_results.items() if result is not None}

    if not successful_results:
        print("❌ No successful results to plot")
        return

    plt.figure(figsize=(10, 6))

    for name, result in successful_results.items():
        if 'energy_history' in result and result['energy_history'] is not None:
            energy_hist = np.array(result['energy_history'])  # Convert to numpy array
            if len(energy_hist) > 0:  # Check if not empty
                errors = [abs(float(e) - float(exact_energy)) for e in energy_hist]
                steps = range(len(errors))
                plt.semilogy(steps, errors, label=f"{name} ({result['param_count']} params)",
                            linewidth=2, marker='o', markersize=3, alpha=0.8)

    plt.xlabel('Optimization Steps')
    plt.ylabel('Energy Error (log scale)')
    plt.title('Error Convergence')
    plt.legend()
    plt.grid(True, alpha=0.3)

    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Error convergence plot saved to {save_path}")

    plt.show()


def plot_parameter_efficiency(training_results, save_path=None):
    """
    Plot parameter count vs final performance.

    Args:
        training_results: Dictionary from train_and_compare function
        save_path: Optional path to save the figure
    """
    successful_results = {name: result for name, result in training_results.items() if result is not None}

    if not successful_results:
        print("❌ No successful results to plot")
        return

    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 6))

    # Extract data
    names = list(successful_results.keys())
    param_counts = [int(result['param_count']) for result in successful_results.values()]
    final_errors = [abs(float(result['fci_diff'])) if result['fci_diff'] is not None else abs(float(result['hf_improvement']))
                   for result in successful_results.values()]
    efficiencies = [float(result['efficiency']) for result in successful_results.values()]

    # Plot 1: Parameters vs Error
    colors = ['red' if 'Neural' in name else 'blue' for name in names]
    ax1.scatter(param_counts, final_errors, c=colors, s=100, alpha=0.7)

    for i, name in enumerate(names):
        ax1.annotate(name.split()[0], (param_counts[i], final_errors[i]),
                    xytext=(5, 5), textcoords='offset points', fontsize=9)

    ax1.set_xlabel('Number of Parameters')
    ax1.set_ylabel('Final Energy Error (Ha)')
    ax1.set_title('Parameters vs Accuracy')
    ax1.grid(True, alpha=0.3)

    # Plot 2: Parameters vs Efficiency
    ax2.scatter(param_counts, efficiencies, c=colors, s=100, alpha=0.7)

    for i, name in enumerate(names):
        ax2.annotate(name.split()[0], (param_counts[i], efficiencies[i]),
                    xytext=(5, 5), textcoords='offset points', fontsize=9)

    ax2.set_xlabel('Number of Parameters')
    ax2.set_ylabel('Efficiency (mHa/param)')
    ax2.set_title('Parameter Efficiency')
    ax2.grid(True, alpha=0.3)

    plt.tight_layout()

    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Parameter efficiency plot saved to {save_path}")

    plt.show()


def plot_training_summary_table(training_results, mf, fci_energy=None):
    """
    Print a comprehensive summary table of training results.

    Args:
        training_results: Dictionary from train_and_compare function
        mf: Mean field object from PySCF
        fci_energy: FCI reference energy (optional)
    """
    successful_results = {name: result for name, result in training_results.items() if result is not None}

    if not successful_results:
        print("❌ No successful results to display")
        return

    print("\n" + "="*100)
    print("TRAINING RESULTS SUMMARY")
    print("="*100)

    # Header
    header = f"{'Model':<25} {'Parameters':<12} {'Final Energy':<15} {'HF Improve':<12} {'FCI Diff':<12} {'Efficiency':<12} {'Converged':<10}"
    print(header)
    print("-"*100)

    # Results
    for name, result in successful_results.items():
        param_count = result['param_count']
        final_energy = result['final_energy']
        hf_improve = result['hf_improvement']
        fci_diff = result['fci_diff'] if result['fci_diff'] is not None else "N/A"
        efficiency = result['efficiency']
        converged = "✅" if result['optimized'] else "❌"

        # Format numbers
        if isinstance(fci_diff, float):
            fci_diff_str = f"{fci_diff:.6f}"
        else:
            fci_diff_str = str(fci_diff)

        row = f"{name:<25} {param_count:<12} {final_energy:<15.6f} {hf_improve:<12.6f} {fci_diff_str:<12} {efficiency:<12.3f} {converged:<10}"
        print(row)

    print("-"*100)
    print(f"HF Energy: {mf.e_tot:.6f} Hartree")
    if fci_energy:
        print(f"FCI Energy: {fci_energy:.6f} Hartree")
    print("="*100)


def create_all_plots(training_results, mf, fci_energy=None, molecule_name="", save_dir=None):
    """
    Create all visualization plots for training results.

    Args:
        training_results: Dictionary from train_and_compare function
        mf: Mean field object from PySCF
        fci_energy: FCI reference energy (optional)
        molecule_name: Name of the molecule for plot titles
        save_dir: Optional directory to save all plots
    """
    if save_dir:
        os.makedirs(save_dir, exist_ok=True)

    print("📊 Creating comprehensive visualization plots...")

    # 1. Main training comparison plot
    save_path = os.path.join(save_dir, "training_comparison.png") if save_dir else None
    plot_training_comparison(training_results, mf, fci_energy, molecule_name, save_path)

    # 2. Error convergence plot (if FCI energy available)
    if fci_energy:
        save_path = os.path.join(save_dir, "error_convergence.png") if save_dir else None
        plot_error_convergence(training_results, fci_energy, save_path)

    # 3. Parameter efficiency plot
    save_path = os.path.join(save_dir, "parameter_efficiency.png") if save_dir else None
    plot_parameter_efficiency(training_results, save_path)

    # 4. Summary table
    plot_training_summary_table(training_results, mf, fci_energy)

    print("✅ All plots created successfully!")

    if save_dir:
        print(f"📁 Plots saved to: {save_dir}")


def quick_comparison_plot(training_results, reference_energy, title="Model Comparison"):
    """
    Create a quick comparison plot showing final energies vs reference.

    Args:
        training_results: Dictionary from train_and_compare function
        reference_energy: Reference energy (HF, FCI, or exact)
        title: Plot title
    """
    successful_results = {name: result for name, result in training_results.items() if result is not None}

    if not successful_results:
        print("❌ No successful results to plot")
        return

    plt.figure(figsize=(10, 6))

    names = list(successful_results.keys())
    final_energies = [float(result['final_energy']) for result in successful_results.values()]
    final_errors = [float(result['final_error']) for result in successful_results.values()]
    param_counts = [int(result['param_count']) for result in successful_results.values()]

    colors = ['lightcoral' if 'Neural' in name else 'lightblue' for name in names]

    # Create bar plot
    bars = plt.bar(range(len(names)), final_energies, yerr=final_errors,
                   capsize=5, color=colors, alpha=0.7)

    # Add reference line
    plt.axhline(y=reference_energy, color='red', linestyle='--',
                label=f'Reference: {reference_energy:.6f}', linewidth=2)

    # Add parameter counts as text
    for i, (bar, param_count) in enumerate(zip(bars, param_counts)):
        height = bar.get_height()
        plt.text(bar.get_x() + bar.get_width()/2., height + final_errors[i] + 0.001,
                f'{param_count} params', ha='center', va='bottom', fontsize=9)

    plt.xticks(range(len(names)), [name.replace(' ', '\n') for name in names], rotation=45, ha='right')
    plt.ylabel('Energy (Hartree)')
    plt.title(title)
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.show()


# Duplicate function removed - using the updated version above
