[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[project]
name = "qpolynets"
version = "0.1.0"
description = "Polynomial neural networks for quantum physics on top of NetKet (PolyGCNN, PolyGAT, PolyJastrow)."
authors = [{name="<PERSON><PERSON>", email=""}]
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
  "jax>=0.4",
  "flax>=0.7",  # for nnx
  "netket>=3.11",
  "numpy",
]

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools]
package-dir = {"" = "src"}

