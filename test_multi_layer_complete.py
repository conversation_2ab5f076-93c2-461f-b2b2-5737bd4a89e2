#!/usr/bin/env python3
"""
Complete test for multi-layer polynomial J<PERSON>row with all features.
"""

import sys
import os
import jax
import jax.numpy as jnp
from jax import random

# Enable float64 precision
jax.config.update("jax_enable_x64", True)

# Add PyTC to path
pytc_path = os.path.abspath('repos/pytc')
if pytc_path not in sys.path:
    sys.path.insert(0, pytc_path)

# PySCF for quantum chemistry
from pyscf import gto

# Import polynomial Jastrow components
from pytc.autodiff.jastrow.polynomial import CPJastrowEN, CPJastrowEE, CPJastrowEEN


def test_multi_layer_with_all_variants():
    """Test multi-layer with all CP variants."""
    print("=== Testing Multi-Layer with All CP Variants ===")
    
    mol = gto.M(atom='H 0 0 0; H 0 0 1.4', basis='sto-3g', unit='bohr', verbose=0)
    key = random.PRNGKey(42)
    r1 = jnp.array([0.0, 0.0, 0.5])
    r2 = jnp.array([0.0, 0.0, -0.5])
    
    # Test all 6 CP variants in multi-layer configuration
    variants = [
        'CP',
        'CP_sparse_LU',
        'CP_sparse_degree',
        'CP_sparse_sawtooth',
        'CP_sparse_degree_LU',
        'CP_sparse_degree_sawtooth'
    ]
    
    for variant in variants:
        print(f"\n  Testing {variant} in multi-layer:")
        
        # Single layer
        cp_single = CPJastrowEEN(mol, degree=3, rank=6, cp_variant=variant)
        params_single = cp_single.init_params(key=key)
        val_single = cp_single._compute(r1, r2, params_single)
        
        # Multi-layer
        cp_multi = CPJastrowEEN(mol, hidden_dims=[8], degree=[3, 2], rank=[6, 4], 
                               cp_variant=[variant, 'CP'])
        params_multi = cp_multi.init_params(key=key)
        val_multi = cp_multi._compute(r1, r2, params_multi)
        
        print(f"    Single: {val_single:.6f}, Multi: {val_multi:.6f}")
        
        assert jnp.isfinite(val_single), f"Single layer {variant} should be finite"
        assert jnp.isfinite(val_multi), f"Multi-layer {variant} should be finite"
        assert not cp_single.is_multi_layer, f"Single {variant} should not be multi-layer"
        assert cp_multi.is_multi_layer, f"Multi {variant} should be multi-layer"
        
        print(f"    ✓ {variant} working in both configurations")


def test_multi_layer_parameter_counting():
    """Test parameter counting for multi-layer networks."""
    print("\n=== Testing Multi-Layer Parameter Counting ===")
    
    mol = gto.M(atom='H 0 0 0; H 0 0 1.4', basis='sto-3g', unit='bohr', verbose=0)
    key = random.PRNGKey(123)
    
    # Compare parameter counts
    configs = [
        {
            'name': 'Single layer',
            'config': {'degree': 3, 'rank': 8},
            'expected_layers': 1
        },
        {
            'name': '2-layer',
            'config': {'hidden_dims': [8], 'degree': 3, 'rank': 6},
            'expected_layers': 2
        },
        {
            'name': '3-layer',
            'config': {'hidden_dims': [16, 8], 'degree': [4, 3, 2], 'rank': [12, 8, 4]},
            'expected_layers': 3
        }
    ]
    
    for config in configs:
        print(f"\n  {config['name']}:")
        
        cp = CPJastrowEEN(mol, **config['config'])
        params = cp.init_params(key=key)
        
        print(f"    Layers: {cp.num_layers} (expected: {config['expected_layers']})")
        print(f"    Is multi-layer: {cp.is_multi_layer}")
        
        if cp.is_multi_layer:
            print(f"    Hidden dims: {cp.hidden_dims}")
            print(f"    Degrees: {cp.degrees}")
            print(f"    Ranks: {cp.ranks}")
            
            # Count parameters per layer
            total_params = 0
            for i, layer_params in enumerate(params['layers']):
                layer_param_count = 0
                
                # U matrices
                for U in layer_params['U']:
                    layer_param_count += U.size
                
                # C matrix/vector
                layer_param_count += layer_params['C'].size
                
                # Bias
                layer_param_count += layer_params['bias'].size
                
                print(f"    Layer {i+1}: {layer_param_count} parameters")
                total_params += layer_param_count
                
            print(f"    Total: {total_params} parameters")
        else:
            # Single layer parameter count
            param_count = 0
            for U in params['U']:
                param_count += U.size
            param_count += params['C'].size
            param_count += params['bias'].size
            print(f"    Total: {param_count} parameters")
        
        assert cp.num_layers == config['expected_layers'], f"Layer count mismatch"
        print(f"    ✓ Configuration valid")


def test_multi_layer_gradients():
    """Test that gradients work correctly for multi-layer networks."""
    print("\n=== Testing Multi-Layer Gradients ===")
    
    mol = gto.M(atom='H 0 0 0; H 0 0 1.4', basis='sto-3g', unit='bohr', verbose=0)
    key = random.PRNGKey(456)
    r1 = jnp.array([0.0, 0.0, 0.5])
    r2 = jnp.array([0.0, 0.0, -0.5])
    
    # Test gradient computation for multi-layer
    cp = CPJastrowEEN(mol, hidden_dims=[8], degree=[3, 2], rank=[6, 4])
    params = cp.init_params(key=key)
    
    def loss_fn(params):
        return cp._compute(r1, r2, params) ** 2
    
    # Compute gradients (allow integer parameters)
    grad_fn = jax.grad(loss_fn, allow_int=True)
    grads = grad_fn(params)
    
    print(f"  Loss value: {loss_fn(params):.6f}")
    print(f"  Gradient structure: {list(grads.keys())}")
    
    if 'layers' in grads:
        print(f"  Number of layer gradients: {len(grads['layers'])}")
        for i, layer_grad in enumerate(grads['layers']):
            print(f"    Layer {i+1} grad keys: {list(layer_grad.keys())}")
            
            # Check that gradients are finite
            for key, grad in layer_grad.items():
                if isinstance(grad, list):
                    for j, g in enumerate(grad):
                        assert jnp.all(jnp.isfinite(g)), f"Layer {i+1} {key}[{j}] gradient should be finite"
                else:
                    assert jnp.all(jnp.isfinite(grad)), f"Layer {i+1} {key} gradient should be finite"
    
    print("  ✓ All gradients are finite")


def test_multi_layer_with_kfac_and_sparsity():
    """Test multi-layer with KFAC and sparsity combinations."""
    print("\n=== Testing Multi-Layer with KFAC and Sparsity ===")
    
    mol = gto.M(atom='H 0 0 0; H 0 0 1.4', basis='sto-3g', unit='bohr', verbose=0)
    key = random.PRNGKey(789)
    r1 = jnp.array([0.0, 0.0, 0.5])
    r2 = jnp.array([0.0, 0.0, -0.5])
    
    # Test complex configuration
    cp = CPJastrowEEN(mol, 
                     hidden_dims=[12, 6],
                     degree=[4, 3, 2],
                     rank=[8, 6, 4],
                     cp_variant=['CP_sparse_degree_sawtooth', 'CP_sparse_LU', 'CP'],
                     use_kfac=True,
                     use_activation_between_layers=True,
                     sawtooth_l_offset=-1,
                     sawtooth_u_offset=1)
    
    params = cp.init_params(key=key)
    val = cp._compute(r1, r2, params)
    
    print(f"  Complex multi-layer value: {val:.6f}")
    print(f"  Configuration:")
    print(f"    Layers: {cp.num_layers}")
    print(f"    Hidden dims: {cp.hidden_dims}")
    print(f"    Degrees: {cp.degrees}")
    print(f"    Ranks: {cp.ranks}")
    print(f"    CP variants: {cp.cp_variants}")
    print(f"    KFAC enabled: {cp.use_kfac}")
    print(f"    Activations between layers: {cp.use_activation_between_layers}")
    print(f"    Sawtooth offsets: l={cp.sawtooth_l_offset}, u={cp.sawtooth_u_offset}")
    
    assert jnp.isfinite(val), "Complex configuration should produce finite value"
    assert cp.is_multi_layer, "Should be multi-layer"
    assert cp.num_layers == 3, "Should have 3 layers"
    
    print("  ✓ Complex configuration working correctly")


def main():
    """Run all comprehensive tests."""
    print("Comprehensive Multi-Layer Polynomial Jastrow Tests")
    print("=" * 60)
    
    try:
        test_multi_layer_with_all_variants()
        test_multi_layer_parameter_counting()
        test_multi_layer_gradients()
        test_multi_layer_with_kfac_and_sparsity()
        
        print("\n🎉 All comprehensive tests passed!")
        print("\nMulti-layer polynomial implementation is fully functional:")
        print("  ✓ All 6 CP variants work in multi-layer mode")
        print("  ✓ Parameter counting and structure correct")
        print("  ✓ Gradient computation working")
        print("  ✓ KFAC + sparsity + multi-layer integration complete")
        print("  ✓ Complex configurations supported")
        
        print("\n🚀 Ready for production use!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
