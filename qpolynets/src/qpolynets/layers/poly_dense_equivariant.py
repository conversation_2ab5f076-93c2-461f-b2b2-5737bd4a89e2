"""
Polynomial Dense Equivariant Layers (migrated from qgat/core/polygcnn)
"""
import jax.numpy as jnp
from flax import linen as nn
from typing import Any, Optional
from jax.nn.initializers import zeros, lecun_normal

from netket.utils import HashableArray
from netket.utils.types import NNInitFunc, Array, DType
from netket.nn.symmetric_linear import DenseEquivariantFFT, DenseEquivariantIrrep

# Default initializer (NetKet style)
default_poly_equivariant_initializer = lecun_normal(in_axis=1, out_axis=0)


class PolyDenseEquivariantFFT(nn.Module):
    """
    Polynomial Dense Equivariant layer using FFT implementation.

    Supports two modes:
    - Direct polynomial recursion (legacy): feature dimension is `features`.
    - CP-weighted mode: project to `rank` first for recursion, then apply a
      learned projection C (with bias) mapping `rank -> features`.
    """

    product_table: HashableArray
    features: int
    shape: tuple
    degree: int = 2
    use_bias: bool = True
    mask: HashableArray | None = None
    param_dtype: DType = jnp.float64
    precision: Any = None
    kernel_init: NNInitFunc = default_poly_equivariant_initializer
    bias_init: NNInitFunc = zeros

    # CP-style controls
    use_cp_weighted: bool = False
    rank: Optional[int] = None
    c_use_bias: bool = True

    def setup(self):
        out_channels = self.rank if self.use_cp_weighted and (self.rank is not None) else self.features
        self.equivariant_layers = [
            DenseEquivariantFFT(
                product_table=self.product_table,
                shape=self.shape,
                features=out_channels,
                use_bias=self.use_bias,
                param_dtype=self.param_dtype,
                precision=self.precision,
                kernel_init=self.kernel_init,
                bias_init=self.bias_init,
                mask=self.mask,
            )
            for _ in range(self.degree)
        ]
        if self.use_cp_weighted:
            # C projects from rank -> features, applied per symmetry element
            proj_features = self.rank if self.rank is not None else self.features
            self.C = nn.Dense(
                features=self.features,
                use_bias=self.c_use_bias,
                dtype=self.param_dtype,
                kernel_init=self.kernel_init,
                bias_init=self.bias_init,
            )
        else:
            self.C = None

    @nn.compact
    def __call__(self, x: Array) -> Array:
        out = self.equivariant_layers[0](x)
        for i in range(1, self.degree):
            ui_out = self.equivariant_layers[i](x)
            out = ui_out * out + out
        if self.use_cp_weighted and self.C is not None:
            # out: [..., rank, n_symm] -> apply Dense on last dim, so swap axes
            out_t = jnp.swapaxes(out, -2, -1)  # [..., n_symm, rank]
            out_t = self.C(out_t)              # [..., n_symm, features]
            out = jnp.swapaxes(out_t, -2, -1)  # [..., features, n_symm]
        return out


class PolyDenseEquivariantIrrep(nn.Module):
    """
    Polynomial Dense Equivariant layer using Irrep implementation.

    Supports CP-weighted mode analogous to FFT version.
    """

    irreps: tuple[HashableArray, ...]
    features: int
    degree: int = 2
    use_bias: bool = True
    mask: HashableArray | None = None
    param_dtype: DType = jnp.float64
    precision: Any = None
    kernel_init: NNInitFunc = default_poly_equivariant_initializer
    bias_init: NNInitFunc = zeros

    # CP-style controls
    use_cp_weighted: bool = False
    rank: Optional[int] = None
    c_use_bias: bool = True

    def setup(self):
        out_channels = self.rank if self.use_cp_weighted and (self.rank is not None) else self.features
        self.equivariant_layers = [
            DenseEquivariantIrrep(
                irreps=self.irreps,
                features=out_channels,
                use_bias=self.use_bias,
                param_dtype=self.param_dtype,
                precision=self.precision,
                kernel_init=self.kernel_init,
                bias_init=self.bias_init,
                mask=self.mask,
            )
            for _ in range(self.degree)
        ]
        if self.use_cp_weighted:
            self.C = nn.Dense(
                features=self.features,
                use_bias=self.c_use_bias,
                dtype=self.param_dtype,
                kernel_init=self.kernel_init,
                bias_init=self.bias_init,
            )
        else:
            self.C = None

    @nn.compact
    def __call__(self, x: Array) -> Array:
        out = self.equivariant_layers[0](x)
        for i in range(1, self.degree):
            ui_out = self.equivariant_layers[i](x)
            out = ui_out * out + out
        if self.use_cp_weighted and self.C is not None:
            out_t = jnp.swapaxes(out, -2, -1)
            out_t = self.C(out_t)
            out = jnp.swapaxes(out_t, -2, -1)
        return out

