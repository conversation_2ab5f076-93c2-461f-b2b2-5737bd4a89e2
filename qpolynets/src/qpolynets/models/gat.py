"""
GAT core and minimal physics wrappers (ported from qgat)
"""
from typing import Optional, Sequence, Callable
import jax
import jax.numpy as jnp
from flax import nnx
from netket.utils.types import Array, DType


class GraphAttentionLayer(nnx.Module):
    def __init__(
        self,
        in_features: int,
        out_features: int,
        dropout_rate: float = 0.1,
        activation: Callable = nnx.elu,
        param_dtype: DType = jnp.float64,
        *,
        rngs: nnx.Rngs,
    ):
        self.in_features = in_features
        self.out_features = out_features
        self.dropout_rate = dropout_rate
        self.activation = activation
        self.param_dtype = param_dtype

        key1, key2, key3 = jax.random.split(rngs.params(), 3)
        self.linear = nnx.Linear(
            in_features=in_features,
            out_features=out_features,
            dtype=param_dtype,
            rngs=nnx.Rngs(key1),
        )
        # Ensure parameters are in the requested dtype (match NetKet default float64)
        self.linear.kernel = nnx.Param(self.linear.kernel.value.astype(param_dtype))
        self.linear.bias = nnx.Param(self.linear.bias.value.astype(param_dtype))
        self.attention_self = nnx.Param(jax.random.normal(key2, (out_features, 1), dtype=param_dtype))
        self.attention_neighbor = nnx.Param(jax.random.normal(key3, (out_features, 1), dtype=param_dtype))

    def __call__(self, node_features: Array, adjacency_matrix: Array, training: bool = False) -> Array:
        h = self.linear(node_features)
        self_att = jnp.dot(h, self.attention_self.value)
        neigh_att = jnp.dot(h, self.attention_neighbor.value)
        logits = self_att + neigh_att.T
        logits = nnx.leaky_relu(logits, negative_slope=0.2)
        masked = jnp.where(adjacency_matrix > 0, logits, -1e9)
        weights = nnx.softmax(masked, axis=1)
        out = weights @ h
        return self.activation(out)


class MultiHeadGraphAttention(nnx.Module):
    def __init__(
        self,
        in_features: int,
        n_heads: int,
        out_features: int,
        dropout_rate: float = 0.1,
        is_output_layer: bool = False,
        activation: Callable = nnx.elu,
        param_dtype: DType = jnp.float64,
        *,
        rngs: nnx.Rngs,
    ):
        self.in_features = in_features
        self.n_heads = n_heads
        self.out_features = out_features
        self.dropout_rate = dropout_rate
        self.is_output_layer = is_output_layer
        self.activation = activation
        self.param_dtype = param_dtype

        self.heads = []
        keys = jax.random.split(rngs.params(), n_heads)
        for i in range(n_heads):
            head = GraphAttentionLayer(
                in_features=in_features,
                out_features=out_features,
                dropout_rate=dropout_rate,
                activation=activation,
                param_dtype=param_dtype,
                rngs=nnx.Rngs(keys[i]),
            )
            self.heads.append(head)

        if is_output_layer and n_heads > 1:
            self.output_projection = nnx.Linear(
                in_features=out_features,
                out_features=out_features,
                dtype=param_dtype,
                rngs=rngs,
            )
        else:
            self.output_projection = None

    def __call__(self, node_features: Array, adjacency_matrix: Array, training: bool = False, **kwargs) -> Array:
        head_outputs = [head(node_features, adjacency_matrix, training=training) for head in self.heads]
        if self.is_output_layer:
            output = jnp.mean(jnp.stack(head_outputs, axis=0), axis=0)
            if self.output_projection is not None:
                output = self.output_projection(output)
        else:
            output = jnp.concatenate(head_outputs, axis=-1)
        return output


# Minimal physics wrappers
from ..core.base_gat import BaseGraphAttentionNetwork


class SpinGAT(BaseGraphAttentionNetwork):
    def __init__(
        self,
        n_sites: int,
        lattice_type: str = "chain",
        lattice_adjacency: Optional[Array] = None,
        lattice_params: Optional[dict] = None,
        hidden_features: Sequence[int] = (16, 8),
        n_heads: Sequence[int] = (4, 1),
        **kwargs,
    ):
        self.lattice_type = lattice_type
        self.lattice_adjacency = lattice_adjacency
        self.lattice_params = lattice_params or {}
        super().__init__(
            n_nodes=n_sites,
            hidden_features=hidden_features,
            n_heads=n_heads,
            **kwargs,
        )

    def get_input_size(self) -> int:
        return 1

    def input_preprocessing(self, spin_configuration: Array) -> Array:
        return spin_configuration.reshape(-1, 1)

    def create_adjacency(self) -> Array:
        if self.lattice_adjacency is not None:
            return self.lattice_adjacency
        # simple nearest-neighbor chain adjacency as a default
        n = self.n_nodes
        adj = jnp.zeros((n, n))
        idx = jnp.arange(n)
        adj = adj.at[idx, (idx + 1) % n].set(1)
        adj = adj.at[idx, (idx - 1) % n].set(1)
        return adj


class MolecularGAT(BaseGraphAttentionNetwork):
    def __init__(
        self,
        n_atoms: int,
        n_orbitals: int,
        molecular_adjacency: Optional[Array] = None,
        hidden_features: Sequence[int] = (32, 16),
        n_heads: Sequence[int] = (4, 1),
        **kwargs,
    ):
        self.n_orbitals = n_orbitals
        self.molecular_adjacency = molecular_adjacency
        super().__init__(
            n_nodes=n_atoms,
            hidden_features=hidden_features,
            n_heads=n_heads,
            **kwargs,
        )

    def get_input_size(self) -> int:
        # Simple example: one scalar per atom (total occupation fraction)
        return 1

    def input_preprocessing(self, occupation_vector: Array) -> Array:
        # Map orbital occupations to per-atom simple features by averaging per atom
        # Expect occupation_vector length is n_orbitals; user may customize later
        # For minimal wrapper, just broadcast a scalar feature to atoms
        avg_occ = jnp.mean(occupation_vector)
        return jnp.ones((self.n_nodes, 1)) * avg_occ

    def create_adjacency(self) -> Array:
        if self.molecular_adjacency is not None:
            return self.molecular_adjacency
        # fallback: fully connected (excluding self)
        n = self.n_nodes
        adj = jnp.ones((n, n)) - jnp.eye(n)
        return adj

