import jax.numpy as jnp
from flax import nnx

from qpolynets.models import <PERSON><PERSON><PERSON>, PolyJastrow


def test_jastrow_forward_scalar():
    m = Jastrow(n_sites=6, rngs=nnx.Rngs(0))
    x = jnp.ones(6)
    y = m(x)
    assert y.shape == ()


def test_poly_jastrow_forward_scalar():
    m = PolyJastrow(n_sites=6, degree=3, rank=4, output_dim=1, rngs=nnx.Rngs(1))
    x = jnp.ones(6)
    y = m(x)
    assert y.shape == ()

