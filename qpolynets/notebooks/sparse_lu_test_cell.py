# Cell to add to neural_vs_polynomial_comparison_fixed.ipynb
# This tests the fixed CP sparse_LU parameter counting

# Import the enhanced parameter counting functions
from model_analysis import count_nonzero_parameters, count_effective_parameters

print("=== CP sparse_LU Parameter Analysis ===")

# Create a CP sparse_LU Jastrow instance for analysis
cp_sparse_lu = CPJastrowEEN(mol_h2, degree=POLY_DEGREE, rank=POLY_RANK, cp_variant='CP_sparse_LU')
sparse_lu_params = cp_sparse_lu.init_params(key=random.PRNGKey(42))

# Count total parameters (should exclude masks now)
total_params = count_parameters(sparse_lu_params)
print(f"Total trainable parameters: {total_params}")

# Count non-zero parameters (accounting for sparsity)
nonzero_params = count_nonzero_parameters(sparse_lu_params, cp_sparse_lu)
print(f"Non-zero parameters (with sparsity): {nonzero_params}")

# Get effective parameter analysis
effective_analysis = count_effective_parameters(sparse_lu_params, cp_sparse_lu)
print(f"Effective parameters analysis:")
print(f"  Total parameters: {effective_analysis['total_parameters']}")
print(f"  Effective parameters: {effective_analysis['effective_parameters']}")
print(f"  Sparsity ratio: {effective_analysis['sparsity_ratio']:.3f}")
print(f"  Parameter reduction: {(1 - effective_analysis['sparsity_ratio']) * 100:.1f}%")

# Check if masks are stored as instance variables (not in params)
print(f"\nMasks stored in instance: {hasattr(cp_sparse_lu, 'masks') and cp_sparse_lu.masks is not None}")
print(f"Masks in parameters: {'masks' in sparse_lu_params}")

if hasattr(cp_sparse_lu, 'masks') and cp_sparse_lu.masks is not None:
    print(f"Number of mask matrices: {len(cp_sparse_lu.masks)}")
    for i, mask in enumerate(cp_sparse_lu.masks):
        nonzero_mask_elements = jnp.sum(mask != 0)
        total_mask_elements = mask.size
        print(f"  Mask {i}: {nonzero_mask_elements}/{total_mask_elements} non-zero elements ({nonzero_mask_elements/total_mask_elements:.3f})")

# Compare with standard CP
cp_standard = CPJastrowEEN(mol_h2, degree=POLY_DEGREE, rank=POLY_RANK, cp_variant='CP')
standard_params = cp_standard.init_params(key=random.PRNGKey(42))
standard_total = count_parameters(standard_params)

print(f"\nComparison:")
print(f"  Standard CP: {standard_total} parameters")
print(f"  Sparse LU: {total_params} total, {effective_analysis['effective_parameters']} effective")
print(f"  Effective reduction: {(1 - effective_analysis['effective_parameters']/standard_total) * 100:.1f}%")

# Access trained results if available
try:
    if 'h2_training_results' in globals() and 'CP sparse_LU' in h2_training_results:
        trained_params = h2_training_results['CP sparse_LU']['optimised_params']
        print(f"\n=== Trained Model Analysis ===")
        print(f"Trained parameters available: Yes")
        
        # Analyze trained parameters
        trained_nonzero = count_nonzero_parameters(trained_params, cp_sparse_lu)
        trained_effective = count_effective_parameters(trained_params, cp_sparse_lu)
        
        print(f"Trained non-zero parameters: {trained_nonzero}")
        print(f"Trained effective parameters: {trained_effective['effective_parameters']}")
        print(f"Trained sparsity ratio: {trained_effective['sparsity_ratio']:.3f}")
    else:
        print(f"\nTraining results not yet available - run training first to see optimized parameters")
except NameError:
    print(f"\nTraining results not yet available - run training first to see optimized parameters")
