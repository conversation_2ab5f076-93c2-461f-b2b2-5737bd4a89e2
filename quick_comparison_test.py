#!/usr/bin/env python3
"""
Quick comparison test between neural and polynomial Jastrow factors.
This script runs a simplified version of the notebook comparison.
"""
import sys
import os
sys.path.insert(0, os.path.abspath('../../repos/pytc'))

import jax
import jax.numpy as jnp
from jax import random
import matplotlib.pyplot as plt
from pyscf import gto, scf

# Enable float64 for quantum chemistry
jax.config.update("jax_enable_x64", True)

# Import PyTC components
from pytc.autodiff.jastrow import (
    CPJastrowEN, CPJastrowEE, CPJastrowEEN, 
    NeuralEN, NeuralEE, NeuralEEN,
    NuclearCusp, CompositeJastrow
)
from pytc.autodiff.ansatz.sj import SlaterJastrow
from pytc.autodiff.ansatz.det import SlaterDet
from pytc.autodiff.mcmc import sample
from pytc.autodiff.mcmc_utils import analyze_energies

def count_parameters(params):
    """Count total parameters in a parameter structure."""
    if isinstance(params, dict):
        total = 0
        for key, value in params.items():
            if key == 'net_vars':  # Neural network parameters
                # Flax stores parameters under 'params' key
                if isinstance(value, dict) and 'params' in value:
                    total += count_parameters(value['params'])
                else:
                    total += count_parameters(value)
            elif key == 'U':  # CP U matrices
                total += sum(u.size for u in value)
            elif hasattr(value, 'size'):  # JAX array
                total += value.size
            elif isinstance(value, (list, tuple)):
                total += sum(count_parameters(v) for v in value)
            elif isinstance(value, dict):
                total += count_parameters(value)
        return total
    elif hasattr(params, 'size'):  # JAX array
        return params.size
    elif isinstance(params, (list, tuple)):
        return sum(count_parameters(p) for p in params)
    else:
        return 0

def create_ansatz(mol, mf, jastrow_type, **kwargs):
    """Create a complete Slater-Jastrow ansatz."""
    key = random.PRNGKey(42)
    
    # Always include nuclear cusp for stability
    ncusp = NuclearCusp(mol)
    params_ncusp = ncusp.init_params()
    
    if jastrow_type == "neural":
        # Neural Jastrow components
        layer_widths = kwargs.get('layer_widths', [8, 8])
        neural_en = NeuralEN(mol, layer_widths=layer_widths)
        neural_ee = NeuralEE(mol, layer_widths=layer_widths)
        neural_een = NeuralEEN(mol, layer_widths=layer_widths)
        
        params_en = neural_en.init_params(key=key)
        params_ee = neural_ee.init_params(key=key)
        params_een = neural_een.init_params(key=key)
        
        composite = CompositeJastrow([ncusp, neural_en, neural_ee, neural_een])
        composite_params = [params_ncusp, params_en, params_ee, params_een]
        
    elif jastrow_type == "polynomial":
        # Polynomial Jastrow components
        degree = kwargs.get('degree', 3)
        rank = kwargs.get('rank', 6)
        activation_scale = kwargs.get('activation_scale', 0.05)
        
        cp_en = CPJastrowEN(mol, degree=degree, rank=rank, activation_scale=activation_scale)
        cp_ee = CPJastrowEE(mol, degree=degree, rank=rank, activation_scale=activation_scale)
        cp_een = CPJastrowEEN(mol, degree=degree, rank=rank, activation_scale=activation_scale)
        
        params_en = cp_en.init_params(key=key)
        params_ee = cp_ee.init_params(key=key)
        params_een = cp_een.init_params(key=key)
        
        composite = CompositeJastrow([ncusp, cp_en, cp_ee, cp_een])
        composite_params = [params_ncusp, params_en, params_ee, params_een]
        
    else:
        raise ValueError(f"Unknown jastrow_type: {jastrow_type}")
    
    # Create Slater-Jastrow ansatz
    det = SlaterDet(mol, mf.mo_coeff)
    sj_ansatz = SlaterJastrow(mol, composite, [det])
    
    # Prepare parameters for VMC
    combined_params = (composite_params, jnp.array([1.0]))
    
    return sj_ansatz, combined_params

def run_comparison():
    """Run quick comparison between neural and polynomial Jastrow factors."""
    print("=== Neural vs Polynomial Jastrow Comparison ===\n")
    
    # Setup H2 molecule
    mol = gto.M(atom='H 0 0 0; H 0 0 1.4', basis='sto-3g', unit='bohr', verbose=0)
    mf = scf.RHF(mol)
    mf.kernel()
    
    print(f"H2 molecule (STO-3G basis)")
    print(f"HF energy: {mf.e_tot:.6f} Hartree\n")
    
    # Test configurations
    configs = [
        ("Neural (8,8)", "neural", {'layer_widths': [8, 8]}),
        ("CP (deg=3, rank=6)", "polynomial", {'degree': 3, 'rank': 6}),
        ("CP sparse_LU", "polynomial", {'degree': 3, 'rank': 6, 'cp_variant': 'CP_sparse_LU'}),
    ]
    
    results = {}
    
    # VMC parameters
    vmc_params = {
        'n_walkers': 64,
        'n_steps': 100,
        'step_size': 0.02,
        'burn_in_steps': 20,
        'use_importance_sampling': True,
        'thinning': 2,
    }
    
    for name, jastrow_type, kwargs in configs:
        print(f"Testing {name}...")
        
        try:
            # Create ansatz
            sj_ansatz, combined_params = create_ansatz(mol, mf, jastrow_type, **kwargs)
            
            # Count parameters
            param_count = count_parameters(combined_params)
            
            # Run VMC sampling
            key = random.PRNGKey(123)
            vmc_results = sample(
                sj_ansatz,
                params=combined_params,
                key=key,
                **vmc_params
            )
            
            # Analyze results
            stats = analyze_energies(vmc_results)
            energy = float(stats['mean'])
            error = float(stats['error'])
            acceptance = float(jnp.mean(vmc_results['acceptance_rates']))
            
            results[name] = {
                'energy': energy,
                'error': error,
                'acceptance': acceptance,
                'param_count': param_count,
                'energy_diff': energy - mf.e_tot
            }
            
            print(f"  Energy: {energy:.6f} ± {error:.6f} Ha")
            print(f"  HF diff: {energy - mf.e_tot:.6f} Ha")
            print(f"  Acceptance: {acceptance:.3f}")
            print(f"  Parameters: {param_count}")
            print()
            
        except Exception as e:
            print(f"  Error: {e}\n")
            results[name] = None
    
    # Create comparison plot
    plt.figure(figsize=(12, 4))
    
    # Filter valid results
    valid_results = {k: v for k, v in results.items() if v is not None}
    
    if valid_results:
        names = list(valid_results.keys())
        energies = [valid_results[name]['energy'] for name in names]
        errors = [valid_results[name]['error'] for name in names]
        param_counts = [valid_results[name]['param_count'] for name in names]
        acceptances = [valid_results[name]['acceptance'] for name in names]
        
        # Energy comparison
        plt.subplot(1, 3, 1)
        colors = ['lightcoral' if 'Neural' in name else 'lightblue' for name in names]
        plt.bar(range(len(names)), energies, yerr=errors, capsize=5, color=colors, alpha=0.7)
        plt.axhline(y=mf.e_tot, color='red', linestyle='--', label='HF Energy')
        plt.xticks(range(len(names)), [name.replace(' ', '\n') for name in names])
        plt.ylabel('Energy (Hartree)')
        plt.title('VMC Energies')
        plt.legend()
        plt.grid(alpha=0.3)
        
        # Parameter count comparison
        plt.subplot(1, 3, 2)
        plt.bar(range(len(names)), param_counts, color=colors, alpha=0.7)
        plt.xticks(range(len(names)), [name.replace(' ', '\n') for name in names])
        plt.ylabel('Number of Parameters')
        plt.title('Parameter Counts')
        plt.grid(alpha=0.3)
        
        # Acceptance rate comparison
        plt.subplot(1, 3, 3)
        plt.bar(range(len(names)), acceptances, color=colors, alpha=0.7)
        plt.xticks(range(len(names)), [name.replace(' ', '\n') for name in names])
        plt.ylabel('Acceptance Rate')
        plt.title('Sampling Efficiency')
        plt.ylim(0, 1)
        plt.grid(alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('neural_vs_polynomial_comparison.png', dpi=150, bbox_inches='tight')
        plt.show()
        
        print("=== Summary ===")
        for name in names:
            r = valid_results[name]
            print(f"{name}:")
            print(f"  Energy improvement: {r['energy_diff']:.6f} Ha")
            print(f"  Parameters: {r['param_count']}")
            print(f"  Efficiency: {r['energy_diff']/r['param_count']*1000:.3f} mHa/param")
            print()
    
    return results

if __name__ == "__main__":
    results = run_comparison()
    print("Comparison complete! Check 'neural_vs_polynomial_comparison.png' for plots.")
