"""CP-based Jastrow factors compatible with PyTC's Jastrow interface.

This implements a simple Canonical-Polyadic polynomial over hand-crafted
EEN-like features used by NeuralEEN: [r12, min(r1n), max(r1n)].

Parameters are kept in plain JAX arrays to be compatible with JIT & pytree.
Optionally, we can initialize from qpolynets CP module weights if available,
otherwise random initialization.
"""
from typing import Dict, Any, Optional
import jax
import jax.numpy as jnp

from .jastrow import J<PERSON>row

try:
    # Optional import to seed parameters similarly to qpolynets CP
    from flax import nnx
    from qpolynets.layers.polynomial_layers import CP as QPolyCP  # type: ignore
    _HAS_QPOLYNETS = True
except Exception:
    _HAS_QPOLYNETS = False
    nnx = None  # type: ignore
    QPolyCP = None  # type: ignore


class CPJastrowEEN(Jastrow):
    """CP polynomial Jastrow on EEN features (r12, min r_en, max r_en).

    u(r1, r2) = 0.1 * tanh(C^T out),
    out = U1 x; for k=2..D: out = (Uk x) * out + out, where * is elementwise

    Params dict structure:
      {
        'degree': int,
        'U': list of length degree, each U[k] of shape (input_dim, rank), k=0..D-1,
        'C': array of shape (rank,),
        'bias': array of shape (), optional
      }
    """

    def __init__(self, mol, degree: int = 3, rank: int = 8, name: Optional[str] = None):
        super().__init__(name=name)
        self.mol = mol
        self.degree = degree
        self.rank = rank
        self.nuc_pos = jnp.array(mol.atom_coords())
        self.input_dim = 1 + 2 * self.nuc_pos.shape[0]

    def _safe_norm(self, x):
        return jnp.sqrt(jnp.sum(x * x, axis=-1) + 1e-12)

    def _features(self, r1: jnp.ndarray, r2: jnp.ndarray) -> jnp.ndarray:
        r12 = self._safe_norm(r1 - r2)[None]
        r1n = self._safe_norm(r1[None, :] - self.nuc_pos)
        r2n = self._safe_norm(r2[None, :] - self.nuc_pos)
        min_d = jnp.minimum(r1n, r2n)
        max_d = jnp.maximum(r1n, r2n)
        x = jnp.concatenate([r12, min_d, max_d], axis=0)  # (input_dim,)
        return x

    def _cp_forward(self, x: jnp.ndarray, params: Dict[str, Any]) -> jnp.ndarray:
        # x: (input_dim,), returns scalar
        U = params['U']
        C = params['C']  # (rank,)
        bias = params.get('bias', jnp.array(0.0))
        out = x @ U[0]  # (rank,)
        for k in range(1, len(U)):
            ukx = x @ U[k]
            out = ukx * out + out
        raw = jnp.dot(out, C) + bias
        # Bound and scale to stabilize
        return 0.1 * jnp.tanh(raw)

    def _compute(self, r1, r2, params):
        x = self._features(r1, r2)
        return self._cp_forward(x, params)

    def init_params(self, key: Optional[jax.Array] = None, **kwargs) -> Dict[str, Any]:
        D = self.degree
        R = self.rank
        M = self.input_dim
        if key is None:
            key = jax.random.PRNGKey(0)
        if _HAS_QPOLYNETS:
            # Initialize from qpolynets CP module to match its distribution
            cp = QPolyCP(degree=D, input_dim=M, rank=R, output_dim=1, rngs=nnx.Rngs(0))
            st = nnx.state(cp)
            U = []
            for i in range(1, D + 1):
                U.append(jnp.array(st[f'U{i}']['kernel'].value))  # (M, R)
            Ck = jnp.array(st['layer_C']['kernel'].value).reshape(R)
            b = jnp.array(st['layer_C']['bias'].value).reshape(())
            return {'degree': D, 'U': U, 'C': Ck, 'bias': b}
        # Otherwise random normal init
        keys = jax.random.split(key, D + 1)
        U = [jax.random.normal(keys[i], (M, R)) * 0.1 for i in range(D)]
        Ck = jax.random.normal(keys[-1], (R,)) * 0.1
        return {'degree': D, 'U': U, 'C': Ck, 'bias': jnp.array(0.0)}

