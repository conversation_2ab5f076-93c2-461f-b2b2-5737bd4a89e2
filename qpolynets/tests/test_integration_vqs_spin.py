import netket as nk
import jax
import jax.numpy as jnp
import numpy as np
from flax import nnx

from qpolynets.models import PolyGCNN, SpinGAT


def test_vqs_spin_polygcnn_and_gat_run_one_iter():
    L = 6
    g = nk.graph.Hypercube(length=L, n_dim=1, pbc=True)
    hi = nk.hilbert.Spin(s=0.5, total_sz=0, N=g.n_nodes)
    H = nk.operator.<PERSON><PERSON><PERSON>(hilbert=hi, graph=g)

    sampler = nk.sampler.MetropolisExchange(hilbert=hi, graph=g, n_chains=4)

    # PolyGCNN
    polygcnn = PolyGCNN(symmetries=g, layers=2, features=(8, 8), degree=2, mode="fft")
    vs1 = nk.vqs.MCState(sampler, polygcnn, n_samples=128)
    opt = nk.optimizer.Sgd(learning_rate=0.02)
    sr = nk.optimizer.SR(diag_shift=0.1)
    vmc1 = nk.VMC(H, opt, variational_state=vs1, preconditioner=sr)
    vmc1.run(n_iter=1)

    # SpinGAT
    gat = SpinGAT(n_sites=L, hidden_features=(8, 4), n_heads=(2, 1), rngs=nnx.Rngs(0))
    vs2 = nk.vqs.MCState(sampler, gat, n_samples=128)
    vmc2 = nk.VMC(H, opt, variational_state=vs2, preconditioner=sr)
    vmc2.run(n_iter=1)

    # Basic energy expectations don't crash
    e1 = vs1.expect(H)
    e2 = vs2.expect(H)
    assert np.isfinite(np.asarray(e1.mean))
    assert np.isfinite(np.asarray(e2.mean))

