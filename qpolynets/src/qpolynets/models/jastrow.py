"""
Standard Jastrow factor implementations (nnx, NetKet-compatible)
"""
from typing import Callable
import jax.numpy as jnp
from flax import nnx
from netket.utils.types import Array, DType


class Jastrow(nnx.Module):
    def __init__(
        self,
        n_sites: int,
        embed_dim: int = 1,
        pair_hidden: int = 0,
        activation: Callable = nnx.tanh,
        param_dtype: DType = jnp.float64,
        *,
        rngs: nnx.Rngs,
    ):
        self.n_sites = n_sites
        self.embed = nnx.Linear(embed_dim, 1, dtype=param_dtype, rngs=rngs)
        # Pairwise symmetric matrix parameterization
        self.theta = nnx.Param(jnp.zeros((n_sites, n_sites), dtype=param_dtype))
        self.bias = nnx.Param(jnp.zeros((), dtype=param_dtype))
        self.activation = activation

    def __call__(self, x: Array) -> Array:
        # x: [n_sites] spin/occupation vector
        x = x.reshape(self.n_sites, 1)
        phi = self.embed(x).squeeze(-1)  # [n_sites]
        # Symmetric pairwise term without self-interaction
        theta = (self.theta.value + self.theta.value.T) / 2.0
        theta = theta - jnp.diag(jnp.diag(theta))
        pair = 0.5 * jnp.sum(theta * (phi[:, None] * phi[None, :]))
        out = pair + self.bias.value
        return out

