{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Polynomial Neural Networks for Lattice Fermions\n", "\n", "This notebook explores polynomial neural networks for fermionic lattice systems, including the Hubbard model and t-J model.\n", "\n", "## Features:\n", "- **Fermionic Systems**: Handle particle number conservation and antisymmetry\n", "- **Lattice Models**: Hubbard model, t-J model, and extended Hubbard model\n", "- **Correlation Analysis**: Study how polynomial networks capture fermionic correlations\n", "- **Phase Diagrams**: Explore different parameter regimes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import sys\n", "import os\n", "sys.path.append('..')\n", "\n", "import jax\n", "import jax.numpy as jnp\n", "import netket as nk\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "from typing import Dict, List, Any\n", "\n", "# Import QGAT components\n", "from models import ModelFactory\n", "from physics.fermion_systems import FermionSystemFactory\n", "from ground_state.exact_solver import ExactGroundStateSolver\n", "\n", "print(\"✅ Imports successful!\")\n", "print(f\"JAX devices: {jax.devices()}\")\n", "print(f\"NetKet version: {nk.__version__}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Configure Fermionic Lattice System\n", "\n", "Set up your fermionic lattice model and parameters:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# =============================================================================\n", "# FERMIONIC LATTICE SYSTEM CONFIGURATION\n", "# =============================================================================\n", "\n", "# Lattice system configuration\n", "FERMION_CONFIG = {\n", "    'model': 'hubbard',         # 'hubbard', 't_j', 'extended_hubbard'\n", "    'lattice': '1d_chain',      # '1d_chain', '2d_square', 'triangular'\n", "    'L': 6,                     # <PERSON><PERSON><PERSON> size\n", "    't': 1.0,                   # Hopping parameter\n", "    'U': 2.0,                   # On-site interaction (Hubbard U)\n", "    'n_particles': 3,           # Number of fermions\n", "    'pbc': True                 # Periodic boundary conditions\n", "}\n", "\n", "# Models optimized for fermionic systems\n", "FERMION_MODELS = [\n", "    {\n", "        'name': 'Standard_RBM_Fermion',\n", "        'type': 'rbm',\n", "        'config': {\n", "            'n_hidden': 12,  # Larger for fermionic correlations\n", "            'param_dtype': 'float64'\n", "        }\n", "    },\n", "    {\n", "        'name': 'Polynomial_RBM_Fermion_CP',\n", "        'type': 'poly_rbm',\n", "        'config': {\n", "            'n_hidden': 10,\n", "            'degree': 3,     # Higher degree for fermionic correlations\n", "            'poly_class': 'CP',\n", "            'param_dtype': 'float64'\n", "        }\n", "    },\n", "    {\n", "        'name': 'Polynomial_RBM_Fermion_Sparse',\n", "        'type': 'poly_rbm',\n", "        'config': {\n", "            'n_hidden': 10,\n", "            'degree': 4,     # Even higher degree with sparsity\n", "            'poly_class': 'CP_sparse_degree',\n", "            'param_dtype': 'float64'\n", "        }\n", "    },\n", "    {\n", "        'name': 'Polynomial_GCNN_Fermion',\n", "        'type': 'poly_gcnn',\n", "        'config': {\n", "            'layers': 3,\n", "            'features': 10,\n", "            'degree': 3,\n", "            'poly_class': 'CP_sparse_LU',\n", "            'param_dtype': 'float64'\n", "        }\n", "    }\n", "]\n", "\n", "# Training configuration for fermionic systems\n", "FERMION_TRAINING = {\n", "    'n_optimization_steps': 800,\n", "    'learning_rate': 0.001,     # Moderate learning rate\n", "    'n_chains': 24,             # More chains for fermionic sampling\n", "    'n_samples': 1536           # More samples for better statistics\n", "}\n", "\n", "print(f\"🔬 Configured fermionic system: {FERMION_CONFIG['model']}\")\n", "print(f\"   Lattice: {FERMION_CONFIG['lattice']} with L={FERMION_CONFIG['L']}\")\n", "print(f\"   Parameters: t={FERMION_CONFIG['t']}, U={FERMION_CONFIG['U']}\")\n", "print(f\"   Particles: {FERMION_CONFIG['n_particles']}\")\n", "print(f\"   Models to test: {len(FERMION_MODELS)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Create Fermionic Lattice System\n", "\n", "Build the fermionic Hamiltonian and compute exact solution:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create fermionic lattice system\n", "try:\n", "    fermion_system = FermionSystemFactory.create(\n", "        model=FERMION_CONFIG['model'],\n", "        lattice=FERMION_CONFIG['lattice'],\n", "        L=FERMION_CONFIG['L'],\n", "        t=FERMION_CONFIG['t'],\n", "        U=FERMION_CONFIG['U'],\n", "        n_particles=FERMION_CONFIG['n_particles'],\n", "        pbc=FERMION_CONFIG['pbc']\n", "    )\n", "    \n", "    print(f\"✅ Created {FERMION_CONFIG['model']} fermionic system:\")\n", "    print(f\"   Hilbert space: {fermion_system['hilbert']}\")\n", "    print(f\"   Lattice sites: {fermion_system['n_sites']}\")\n", "    print(f\"   Particles: {fermion_system['n_particles']}\")\n", "    print(f\"   Filling: {fermion_system['n_particles']/fermion_system['n_sites']:.2f}\")\n", "    \n", "except Exception as e:\n", "    print(f\"❌ Failed to create fermionic system: {e}\")\n", "    print(\"   Falling back to simple fermionic model...\")\n", "    \n", "    # Fallback to simple fermionic model\n", "    fermion_system = {\n", "        'hilbert': nk.hilbert.Fock(n_max=1, N=FERMION_CONFIG['L']),\n", "        'hamiltonian': nk.operator.Bose<PERSON><PERSON>bard(\n", "            hilbert=nk.hilbert.Fock(n_max=1, N=FERMION_CONFIG['L']),\n", "            graph=nk.graph.Chain(FERMION_CONFIG['L'], pbc=FERMION_CONFIG['pbc']),\n", "            U=FERMION_CONFIG['U'], t=FERMION_CONFIG['t']\n", "        ),\n", "        'problem_name': 'fermion_simple',\n", "        'n_sites': FERMION_CONFIG['L'],\n", "        'n_particles': FERMION_CONFIG['n_particles']\n", "    }\n", "\n", "# Compute exact ground state\n", "print(\"\\n🔍 Computing exact fermionic ground state...\")\n", "exact_solver = ExactGroundStateSolver()\n", "exact_result = exact_solver.solve(fermion_system['hamiltonian'])\n", "\n", "print(f\"✅ Exact ground state energy: {exact_result['energy']:.8f}\")\n", "print(f\"   Hilbert space size: {exact_result['hilbert_size']}\")\n", "\n", "# Calculate energy per particle\n", "energy_per_particle = exact_result['energy'] / fermion_system['n_particles']\n", "print(f\"   Energy per particle: {energy_per_particle:.6f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Create Fermionic Models\n", "\n", "Build models designed for fermionic lattice systems:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create fermionic models\n", "fermion_models_info = []\n", "system_size = fermion_system['n_sites']\n", "\n", "print(\"🔬 Creating Fermionic Models:\")\n", "print(\"=\" * 50)\n", "\n", "for model_config in FERMION_MODELS:\n", "    try:\n", "        # Create model\n", "        model, param_count = ModelFactory.create(\n", "            type_name=model_config['type'],\n", "            config=model_config['config'],\n", "            physics_type='fermion',\n", "            system_size=system_size,\n", "            problem_name=fermion_system['problem_name'],\n", "            hilbert=fermion_system['hilbert'],\n", "            random_seed=42\n", "        )\n", "        \n", "        fermion_models_info.append({\n", "            'name': model_config['name'],\n", "            'type': model_config['type'],\n", "            'model': model,\n", "            'param_count': param_count,\n", "            'config': model_config['config']\n", "        })\n", "        \n", "        print(f\"✅ {model_config['name']}: {param_count:,} parameters\")\n", "        \n", "    except Exception as e:\n", "        print(f\"❌ Failed to create {model_config['name']}: {e}\")\n", "\n", "print(f\"\\n📊 Successfully created {len(fermion_models_info)} fermionic models\")\n", "\n", "# Fermionic model comparison\n", "if fermion_models_info:\n", "    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))\n", "    \n", "    names = [info['name'] for info in fermion_models_info]\n", "    param_counts = [info['param_count'] for info in fermion_models_info]\n", "    colors = ['blue' if 'Standard' in name else 'red' for name in names]\n", "    \n", "    # Parameter comparison\n", "    bars = ax1.bar(range(len(names)), param_counts, color=colors, alpha=0.7)\n", "    ax1.set_xlabel('Fermionic Model')\n", "    ax1.set_ylabel('Parameter Count')\n", "    ax1.set_title(f'Fermionic Model Comparison - {FERMION_CONFIG[\"model\"].upper()}')\n", "    ax1.set_xticks(range(len(names)))\n", "    ax1.set_xticklabels([name.replace('_', '\\n') for name in names], rotation=45, ha='right')\n", "    \n", "    # Add value labels\n", "    for bar, count in zip(bars, param_counts):\n", "        ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(param_counts)*0.01,\n", "                f'{count}', ha='center', va='bottom', fontweight='bold')\n", "    \n", "    # Model architecture breakdown\n", "    model_types = {}\n", "    for info in fermion_models_info:\n", "        model_type = info['type']\n", "        if model_type not in model_types:\n", "            model_types[model_type] = []\n", "        model_types[model_type].append(info['param_count'])\n", "    \n", "    # Box plot of parameter counts by type\n", "    type_names = list(model_types.keys())\n", "    type_params = [model_types[t] for t in type_names]\n", "    \n", "    ax2.boxplot(type_params, labels=type_names)\n", "    ax2.set_ylabel('Parameter Count')\n", "    ax2.set_title('Parameter Distribution by Model Type')\n", "    ax2.grid(True, alpha=0.3)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Fermionic Training Function\n", "\n", "Specialized training for fermionic systems with particle number conservation:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def train_fermionic_model(model_info, fermion_system, exact_energy, training_config):\n", "    \"\"\"Train a fermionic model with particle number conservation.\"\"\"\n", "    \n", "    print(f\"\\n🔬 Training {model_info['name']} on fermionic system...\")\n", "    \n", "    # Create sampler for fermionic systems\n", "    # Use exchange sampler for better fermionic sampling\n", "    try:\n", "        sampler = nk.sampler.MetropolisExchange(\n", "            hilbert=fermion_system['hilbert'],\n", "            n_chains=training_config['n_chains'],\n", "            graph=nk.graph.Chain(fermion_system['n_sites'], pbc=True)\n", "        )\n", "    except:\n", "        # Fallback to local sampler\n", "        sampler = nk.sampler.MetropolisLocal(\n", "            hilbert=fermion_system['hilbert'],\n", "            n_chains=training_config['n_chains']\n", "        )\n", "    \n", "    # Create variational state\n", "    vs = nk.vqs.MCState(\n", "        sampler=sampler,\n", "        model=model_info['model'],\n", "        n_samples=training_config['n_samples']\n", "    )\n", "    \n", "    # Create optimizer with fermionic-specific settings\n", "    optimizer = nk.optimizer.<PERSON>(\n", "        learning_rate=training_config['learning_rate'],\n", "        b1=0.9,\n", "        b2=0.999\n", "    )\n", "    \n", "    # Create VMC solver\n", "    gs = nk.VMC(\n", "        hamiltonian=fermion_system['hamiltonian'],\n", "        optimizer=optimizer,\n", "        variational_state=vs\n", "    )\n", "    \n", "    # Training history\n", "    history = {\n", "        'steps': [],\n", "        'energies': [],\n", "        'errors': [],\n", "        'variances': [],\n", "        'energy_per_particle': []\n", "    }\n", "    \n", "    # Training loop\n", "    try:\n", "        for step in range(training_config['n_optimization_steps']):\n", "            # Run optimization step\n", "            gs.run(n_iter=1)\n", "            \n", "            # Get current energy\n", "            energy = gs.energy.mean.real\n", "            variance = gs.energy.variance.real\n", "            error = abs(energy - exact_energy)\n", "            energy_pp = energy / fermion_system['n_particles']\n", "            \n", "            # Store history\n", "            history['steps'].append(step)\n", "            history['energies'].append(energy)\n", "            history['errors'].append(error)\n", "            history['variances'].append(variance)\n", "            history['energy_per_particle'].append(energy_pp)\n", "            \n", "            # Print progress\n", "            if step % 100 == 0 or step == training_config['n_optimization_steps'] - 1:\n", "                print(f\"   🔄 Step {step:4d}: E = {energy:8.6f}, E/N = {energy_pp:6.4f}, Error = {error:8.6f}\")\n", "    \n", "    except Exception as e:\n", "        print(f\"   ❌ Training failed: {e}\")\n", "        return None\n", "    \n", "    final_error = history['errors'][-1]\n", "    best_error = min(history['errors'])\n", "    final_energy_pp = history['energy_per_particle'][-1]\n", "    \n", "    print(f\"   📊 Final error: {final_error:.6f}\")\n", "    print(f\"   🏆 Best error: {best_error:.6f}\")\n", "    print(f\"   ⚛️ Final energy per particle: {final_energy_pp:.6f}\")\n", "    \n", "    return history\n", "\n", "print(\"✅ Fermionic training function defined\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Train Fermionic Models\n", "\n", "Run training for all fermionic models:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Train all fermionic models\n", "fermion_results = {}\n", "exact_energy = exact_result['energy']\n", "exact_energy_pp = exact_energy / fermion_system['n_particles']\n", "\n", "print(f\"🔬 Training {len(fermion_models_info)} models on {FERMION_CONFIG['model']} system\")\n", "print(f\"   Target energy: {exact_energy:.8f}\")\n", "print(f\"   Target energy per particle: {exact_energy_pp:.6f}\")\n", "print(f\"   System: L={FERMION_CONFIG['L']}, N={fermion_system['n_particles']}, U/t={FERMION_CONFIG['U']/FERMION_CONFIG['t']:.1f}\")\n", "print(\"=\" * 80)\n", "\n", "for model_info in fermion_models_info:\n", "    history = train_fermionic_model(model_info, fermion_system, exact_energy, FERMION_TRAINING)\n", "    if history is not None:\n", "        fermion_results[model_info['name']] = {\n", "            'history': history,\n", "            'model_info': model_info\n", "        }\n", "\n", "print(f\"\\n✅ Fermionic training complete! {len(fermion_results)} models trained successfully.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Fermionic Results Analysis\n", "\n", "Comprehensive analysis of fermionic training results:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Fermionic results visualization\n", "if fermion_results:\n", "    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))\n", "    \n", "    # 1. Energy convergence\n", "    for name, result in fermion_results.items():\n", "        history = result['history']\n", "        color = 'blue' if 'Standard' in name else 'red'\n", "        linestyle = '-' if 'RBM' in name else '--'\n", "        ax1.plot(history['steps'], history['energies'], \n", "                label=name.replace('_', ' '), color=color, linestyle=linestyle, alpha=0.8)\n", "    \n", "    ax1.axhline(y=exact_energy, color='black', linestyle=':', alpha=0.7, label='Exact Energy')\n", "    ax1.set_xlabel('Training Step')\n", "    ax1.set_ylabel('Total Energy')\n", "    ax1.set_title(f'{FERMION_CONFIG[\"model\"].upper()} Energy Convergence')\n", "    ax1.legend()\n", "    ax1.grid(True, alpha=0.3)\n", "    \n", "    # 2. Energy per particle convergence\n", "    for name, result in fermion_results.items():\n", "        history = result['history']\n", "        color = 'blue' if 'Standard' in name else 'red'\n", "        linestyle = '-' if 'RBM' in name else '--'\n", "        ax2.plot(history['steps'], history['energy_per_particle'], \n", "                label=name.replace('_', ' '), color=color, linestyle=linestyle, alpha=0.8)\n", "    \n", "    ax2.axhline(y=exact_energy_pp, color='black', linestyle=':', alpha=0.7, label='Exact E/N')\n", "    ax2.set_xlabel('Training Step')\n", "    ax2.set_ylabel('Energy per Particle')\n", "    ax2.set_title('Energy per Particle Convergence')\n", "    ax2.legend()\n", "    ax2.grid(True, alpha=0.3)\n", "    \n", "    # 3. Error convergence (log scale)\n", "    for name, result in fermion_results.items():\n", "        history = result['history']\n", "        color = 'blue' if 'Standard' in name else 'red'\n", "        linestyle = '-' if 'RBM' in name else '--'\n", "        ax3.semilogy(history['steps'], history['errors'], \n", "                    label=name.replace('_', ' '), color=color, linestyle=linestyle, alpha=0.8)\n", "    \n", "    ax3.set_xlabel('Training Step')\n", "    ax3.set_ylabel('Energy Error (log scale)')\n", "    ax3.set_title('Error Convergence')\n", "    ax3.legend()\n", "    ax3.grid(True, alpha=0.3)\n", "    \n", "    # 4. Final performance comparison\n", "    names = list(fermion_results.keys())\n", "    best_errors = [min(fermion_results[name]['history']['errors']) for name in names]\n", "    param_counts = [fermion_results[name]['model_info']['param_count'] for name in names]\n", "    \n", "    # Scatter plot: parameters vs best error\n", "    for i, name in enumerate(names):\n", "        color = 'blue' if 'Standard' in name else 'red'\n", "        marker = 'o' if 'RBM' in name else 's'\n", "        ax4.loglog(param_counts[i], best_errors[i], \n", "                  marker=marker, color=color, markersize=10, alpha=0.8, label=name.replace('_', ' '))\n", "    \n", "    ax4.set_xlabel('Parameter Count')\n", "    ax4.set_ylabel('Best Energy Error')\n", "    ax4.set_title('Parameter Efficiency')\n", "    ax4.legend()\n", "    ax4.grid(True, alpha=0.3)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # Summary table\n", "    print(f\"\\n📊 FERMIONIC RESULTS SUMMARY - {FERMION_CONFIG['model'].upper()}\")\n", "    print(\"=\" * 90)\n", "    print(f\"{'Model':<30} {'Parameters':<12} {'Best Error':<15} {'Final E/N':<15} {'Quality':<10}\")\n", "    print(\"-\" * 90)\n", "    \n", "    for name, result in fermion_results.items():\n", "        history = result['history']\n", "        param_count = result['model_info']['param_count']\n", "        best_error = min(history['errors'])\n", "        final_energy_pp = history['energy_per_particle'][-1]\n", "        \n", "        if best_error < 0.001:\n", "            quality = \"Excellent\"\n", "        elif best_error < 0.01:\n", "            quality = \"Good\"\n", "        elif best_error < 0.1:\n", "            quality = \"Fair\"\n", "        else:\n", "            quality = \"Poor\"\n", "        \n", "        print(f\"{name:<30} {param_count:<12,} {best_error:<15.6f} {final_energy_pp:<15.6f} {quality:<10}\")\n", "    \n", "    # Find best model\n", "    best_model = min(fermion_results.items(), key=lambda x: min(x[1]['history']['errors']))\n", "    print(f\"\\n🏆 Best Model: {best_model[0]}\")\n", "    print(f\"   Best error: {min(best_model[1]['history']['errors']):.6f}\")\n", "    print(f\"   Parameters: {best_model[1]['model_info']['param_count']:,}\")\n", "\n", "else:\n", "    print(\"❌ No successful fermionic training results to display\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Fermionic Correlation Analysis\n", "\n", "Analyze how polynomial networks capture fermionic correlations:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Fermionic correlation and polynomial enhancement analysis\n", "if fermion_results:\n", "    standard_fermion = {name: result for name, result in fermion_results.items() if 'Standard' in name}\n", "    polynomial_fermion = {name: result for name, result in fermion_results.items() if 'Polynomial' in name}\n", "    \n", "    print(\"\\n🔬 FERMIONIC CORRELATION ANALYSIS\")\n", "    print(\"=\" * 60)\n", "    \n", "    # System parameters\n", "    filling = fermion_system['n_particles'] / fermion_system['n_sites']\n", "    interaction_strength = FERMION_CONFIG['U'] / FERMION_CONFIG['t']\n", "    \n", "    print(f\"System Parameters:\")\n", "    print(f\"  Lattice sites: {fermion_system['n_sites']}\")\n", "    print(f\"  Particles: {fermion_system['n_particles']}\")\n", "    print(f\"  Filling: {filling:.2f}\")\n", "    print(f\"  U/t ratio: {interaction_strength:.1f}\")\n", "    \n", "    # Analyze correlation regime\n", "    if interaction_strength < 1.0:\n", "        regime = \"Weakly Correlated (Metallic)\"\n", "    elif interaction_strength < 4.0:\n", "        regime = \"Moderately Correlated\"\n", "    else:\n", "        regime = \"Strongly Correlated (<PERSON><PERSON> Insulator)\"\n", "    \n", "    print(f\"  Correlation regime: {regime}\")\n", "    \n", "    if standard_fermion and polynomial_fermion:\n", "        # Compare performance in this correlation regime\n", "        standard_best = [min(result['history']['errors']) for result in standard_fermion.values()]\n", "        polynomial_best = [min(result['history']['errors']) for result in polynomial_fermion.values()]\n", "        \n", "        avg_standard = np.mean(standard_best)\n", "        avg_polynomial = np.mean(polynomial_best)\n", "        \n", "        improvement = (avg_standard - avg_polynomial) / avg_standard * 100\n", "        \n", "        print(f\"\\nPerformance in {regime}:\")\n", "        print(f\"  Standard models: {avg_standard:.6f} average error\")\n", "        print(f\"  Polynomial models: {avg_polynomial:.6f} average error\")\n", "        print(f\"  Improvement: {improvement:+.1f}%\")\n", "        \n", "        # Analyze polynomial degree effects\n", "        print(f\"\\nPolynomial Degree Analysis:\")\n", "        for name, result in polynomial_fermion.items():\n", "            config = result['model_info']['config']\n", "            degree = config.get('degree', 'N/A')\n", "            poly_class = config.get('poly_class', 'N/A')\n", "            best_error = min(result['history']['errors'])\n", "            \n", "            print(f\"  {name}: degree={degree}, class={poly_class}, error={best_error:.6f}\")\n", "        \n", "        # Recommendations based on correlation regime\n", "        print(f\"\\n💡 Recommendations for {regime}:\")\n", "        \n", "        if interaction_strength < 1.0:\n", "            print(\"  - Lower polynomial degrees (2-3) may be sufficient\")\n", "            print(\"  - Standard models might perform well\")\n", "        elif interaction_strength < 4.0:\n", "            print(\"  - Moderate polynomial degrees (3-4) recommended\")\n", "            print(\"  - Sparse polynomial classes may help with efficiency\")\n", "        else:\n", "            print(\"  - Higher polynomial degrees (4+) may be needed\")\n", "            print(\"  - Polynomial enhancements likely crucial\")\n", "        \n", "        if improvement > 10:\n", "            print(\"  ✅ Polynomial networks show significant advantage!\")\n", "        elif improvement > 0:\n", "            print(\"  ✅ Polynomial networks show modest improvement\")\n", "        else:\n", "            print(\"  ⚠️ Standard networks performed better in this case\")\n", "    \n", "    else:\n", "        print(\"⚠️ Need both standard and polynomial models for comparison\")\n", "\n", "print(\"\\n🔬 Fermionic Experiment Complete!\")\n", "print(\"\\nTo explore different fermionic systems:\")\n", "print(\"1. Change FERMION_CONFIG['model'] to 't_j' or 'extended_hubbard'\")\n", "print(\"2. Vary U/t ratio to explore different correlation regimes\")\n", "print(\"3. Try different fillings by changing n_particles\")\n", "print(\"4. Experiment with 2D lattices for more complex physics\")\n", "print(\"5. Use higher polynomial degrees for strongly correlated systems\")"]}], "metadata": {"kernelspec": {"display_name": "quantum", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 4}