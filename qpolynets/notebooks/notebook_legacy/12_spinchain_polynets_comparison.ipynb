{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Spin Chain: PolyGCNN vs GAT (Quick Setup)\n", "Small-L comparison to verify models and plotting.\n"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([-28.56918544])"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["import jax, jax.numpy as jnp\n", "import netket as nk\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from flax import nnx\n", "\n", "import sys, os\n", "sys.path.append('/Users/<USER>/Projects/Quantum/qpolynets/src')\n", "\n", "from qpolynets.models import PolyGCNN, SpinGAT\n", "\n", "L = 16\n", "g = nk.graph.Hypercube(length=L, n_dim=1, pbc=True)\n", "hi = nk.hilbert.Spin(s=0.5, total_sz=0, N=g.n_nodes)\n", "H = nk.operator.Heisenberg(hilbert=hi, graph=g)\n", "\n", "# Exact ground state energy (small L)\n", "exact_energy = nk.exact.lanczos_ed(H)\n", "exact_energy\n"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAArIAAAGGCAYAAACHemKmAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjAsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvlHJYcgAAAAlwSFlzAAAPYQAAD2EBqD+naQAAg51JREFUeJzt3XdYU9f/B/B3QthT9hRQXEwHap1I3VtrnXUg2mnbb/XXaq17W7WtrbZWLYqzarXubdXWLVhFwAmCIFNE2Tv39wclGvYOgffrefJI7j2555MQ8M3JueeKBEEQQERERESkZMSKLoCIiIiIqDIYZImIiIhIKTHIEhEREZFSYpAlIiIiIqXEIEtERERESolBloiIiIiUEoMsERERESklBlkiIiIiUkoMskRERESklBhkiRTI19cXIpEI/v7+xe4fNGgQ7OzsKnVsLy+vSj+2Oi1cuBAikQgJCQmKLqVM4eHhEIlEWLNmTY0cf/v27RgzZgxatGgBsVhc5vfn8uXLGDBgABo1agRNTU00a9YMS5YsqXT/PXr0gEgkkt00NTXh5uaGtWvXQiqVVuhYBa+Vr69vpeu5fPkyxo4di8aNG0NdXR3a2tpwcnLC//3f/+HBgwfFPubo0aMYPHgwzMzMoKamBkNDQ/Ts2RO7du1CTk6OrF3Bc1y5cmWRYxT3c1fwPjU1NUVKSkqRx9jZ2WHQoEGVfq7VKSsrCz///DM8PDxgZGQEVVVVGBkZoUePHti4cWOx9QNAQkIC1NXVS3zuZd169OhRS8+QqPwYZInqqXnz5uHgwYOKLoPesGPHDgQHB6NDhw5o2rRpqW13794NDw8P6OvrY/v27Thx4gRmzZqFql5VvEmTJrh27RquXbuGvXv3wsrKCtOnT8fs2bOrdNyKmjt3Lrp164anT59i7ty5OHXqFA4dOgRvb2+cPXsWrVq1Ql5enqy9IAiYPHkyhgwZAqlUiu+//x7nzp3Dtm3b4Obmhk8++QS//PJLkX5WrlyJxMTEctf1/PlzrFq1qlqeY014/vw5OnfujBkzZqBFixbYtGkTzp8/Dx8fH7i6umLmzJn45JNPin3sjh07kJ2dDQDw8fGRbZ86darsPXHt2jX8+eefAIDPPvtMbntxry+RwglEpDBbt24VAAh+fn7F7h84cKBga2tbu0VVswULFggAhOfPn9doP2lpaVU+RlhYmABAWL16dTVUVFReXp7s69K+t8+ePRO0tbWFjz/+uFr79/DwEJycnOS2ZWdnC02aNBG0tLSE7Ozsch+r4LXaunVrhevYvXu3AED46KOPBKlUWmS/VCoV1q9fL+Tm5sq2ffvttwIAYdGiRcUeMyYmRrh06ZLsPgChV69egkQiEWbMmCHXtrifu4L3ab9+/QRtbW0hJiZG7jG2trbCwIEDK/xcq1ufPn0EVVVV4e+//y52f0JCgrBjx45i9zk7OwumpqZC+/btBX19fSE9Pb3YdjX9c0BUnTgiS6RkBEHAL7/8gtatW0NTUxONGjXCu+++iydPnsi1K25qwR9//IGOHTtCX18fWlpaaNKkCby9veXaJCcn48svv4S9vT3U1NRgZWWFL774AmlpaXLtRCIRPv30U+zYsQOtWrWClpYW3NzccOzYsWLrjoyMxDvvvAM9PT3o6+tj/PjxeP78eZF2e/fuRadOnaCtrQ0dHR307dsXt2/fLvLcdHR0EBgYiD59+kBXVxc9e/YEALx69QpTpkyBoaEhdHR0MHDgQDx58gQikQgLFy4sz0tcY8Ti8v3K/e2335CWloZZs2bVcEWAqqoq2rVrh/T0dNn3IygoCEOHDkWjRo2goaGB1q1bY9u2baUe59KlSxCJRPj999+L7Nu+fTtEIhH8/PwAAEuXLoWxsTF++OEHiESiIu1FIhGmTZsGFRUVAEBOTg6+/fZbtGzZEvPmzSu2f3Nzc3Tt2lVuW4sWLTBlyhT8/PPPePr0adkvxn+15ebmVuq9MmzYMNja2hY7TaNjx45o27at7H55fhYL8/Pzw5kzZ/DBBx+ge/fuxbYxMjLC+PHji2y/ceMGgoKCMGHCBLz//vtISkrCgQMHKvgMieoeBlmiOiAvLw+5ublFbkIxHyN/+OGH+OKLL9CrVy8cOnQIv/zyC4KDg9G5c2fExcWV2Me1a9cwevRoNGnSBHv27MHx48cxf/585Obmytqkp6fDw8MD27Ztw+eff46TJ09i1qxZ8PX1xZAhQ4rUc/z4caxfvx6LFy/GgQMHYGhoiOHDhxcJ1QAwfPhwODg4YP/+/Vi4cCEOHTqEvn37ys1rXL58OcaOHQtHR0fs27cPO3bsQEpKCrp164Z79+7JHS87OxtDhgzB22+/jcOHD2PRokWQSqUYPHgwdu/ejVmzZuHgwYPo2LEj+vXrV+7vRXmU9P0qfKvovNMC//zzDwwNDfHgwQO0bt0aEokEpqam+Oijj5CcnCzXtmC+Z1XmqoaGhkIikaBRo0Z4+PAhOnfujODgYPz000/4888/4ejoCC8vr1I/cu/WrRvatGmDn3/+uci+9evXo3379mjfvj2io6Nx79499O7dGxoaGuWqz9/fH4mJiRg6dGixwbc0CxcuhIqKSokBuDBbW1t88skn8PHxwaNHjyrUl7e3NyIiInD+/Hm57Q8ePMDNmzcxefJkAOX7WSzO2bNnAQBDhgypUF3A66kE3t7eGDNmDLS0tOSmFxApLQWPCBM1aAUfcZZ2e/Pj52vXrgkAhO+++07uOJGRkYKmpqYwc+ZM2bZJkybJPXbNmjUCAOHVq1cl1rNixQpBLBYXmeqwf/9+AYBw4sQJ2TYAgpmZmZCcnCzbFhsbK4jFYmHFihWybQUf2U6fPl3umLt27RIACDt37hQEQRAiIiIEiUQifPbZZ3LtUlJSBHNzc2HUqFFyzw2AsGXLFrm2x48fFwAIGzZsKPK8AAgLFiwo8bkLQvk/UvXw8Cjz+wZAmDRpUonHKG1qQYsWLQQNDQ1BV1dXWL58uXDhwgVh1apVgqamptClSxe5j+O3bdsmqKioCNu2bSu15oK6nZychJycHCEnJ0eIjo4Wvv76awGAMHLkSEEQBGHMmDGCurq6EBERIffY/v37C1paWrL3T3FTCwrez7dv35Ztu3nzpgBAVt/169cFAMLXX39dpL7c3FxZbTk5ObLnuWfPHgGA8Ouvv5b5HAsAEKZNmyYIgiDMmTNHEIvFQkBAgFydxU0teP78uZCQkCDo6+sLI0aMkO0vz9SCnJwcwczMTBg3bpzc9pkzZwpqampCQkKCIAjl+1kszkcffSQAEB48eCC3XSqVyr1ub07JEIT8aTd6enrCW2+9Jds2adIkQSQSCSEhIUX64dQCUiYckSWqA7Zv3w4/P78it8IflR47dgwikQjjx4+XG/kzNzeHm5sbLl68WGIf7du3BwCMGjUK+/btQ1RUVJE2x44dg7OzM1q3bi13/L59+0IkEhU5vqenJ3R1dWX3zczMYGpqWuzHuO+9957c/VGjRkEikeDChQsAgNOnTyM3NxcTJ06U61tDQwMeHh7FPrcRI0bI3f/7779lx37T2LFjS3hVKmfjxo3Ffr8K3yo7lUEqlSIzMxPffPMNZs+ejR49euCrr77CihUrcOXKFfz111+ytgWv18SJE8t17ODgYKiqqkJVVRWWlpb47rvv8N5772Hz5s0AgPPnz6Nnz56wsbGRe5yXlxfS09Nx7dq1Eo89duxYmJqayo3Krlu3DiYmJhg9enSZtRWcgV9wq66PvmfOnAlDQ8NyT9UwMjLCrFmzcODAAdy4caPc/UgkEowfPx5//vknkpKSAOSP3u/YsQNDhw6FkZERgPL9LFbE4cOH5V43fX19uf379u1DcnKy3NQFb29vCIKArVu3VqlvIkVjkCWqA1q1agV3d/cit8L/IcXFxUEQBJiZmcn9x6Wqqorr16+XusRV9+7dcejQIVnosba2hrOzs9ycxri4ONy9e7fIsXV1dSEIQpHjF/zH/CZ1dXVkZGQU2W5ubi53XyKRwMjICC9evJD1DeT/J1+4/7179xbpW0tLC3p6enLbXrx4AYlEAkNDQ7ntZmZmJb4uleHg4IDWrVuXeWvcuHGljl/wuvbt21due//+/QEA//77b6Vrb9q0Kfz8/ODv74+goCC8evUKO3fulL3XXrx4AQsLiyKPs7S0lO0vibq6Oj788EPs3r0br169wvPnz7Fv3z5MnToV6urqACALyMX9sXPx4kX4+fnh119/ldte8DqGhYVV4hkDenp6spURCv5wKssXX3wBS0tLzJw5s0J9eXt7IzMzE3v27AGQ/wdaTEyMbFoBUL6fxeIUvA6FX7sePXrI/ngqbokwHx8faGhooF+/fnj16hVevXoFV1dX2NnZwdfXV251CCJlI1F0AURUfsbGxhCJRLh06ZIsGLypuG1vGjp0KIYOHYqsrCxcv34dK1aswLhx42BnZ4dOnTrB2NgYmpqa2LJlS4n9V1ZsbCysrKxk93Nzc/HixQtZaCs49v79+2Fra1vm8YqbK2lkZITc3FwkJibKhdnY2NhK112cnj17ykZ/SzNp0qRKzV11dXXF9evXi2wX/pujXN6TxoqjoaEBd3f3EvcbGRkhJiamyPbo6GgAZb8HPv74Y6xcuRJbtmxBZmYmcnNz8dFHH8n2W1pawsnJCWfPnkVmZqbcPNnWrVsDAFJTU+WO6e7uDkNDQxw+fBgrVqyo8DzZgrp+/PFHzJo1Cx9//HGZ7TU1NbFw4UJ88MEHOH78eLn7cXR0RIcOHbB161Z8+OGH2Lp1KywtLdGnTx+5dmX9LBand+/e+Oabb3DkyBG54xkYGMi+p4X/uHz06BEuX74MACX+YXX69GkMGDCg3M+RqC5hkCVSIoMGDcLKlSsRFRVV5OPzilBXV4eHhwcMDAxw+vRp3L59G506dcKgQYOwfPlyGBkZwd7evhorB3bt2oV27drJ7u/btw+5ubmyRdb79u0LiUSC0NDQIlMGysvDwwOrVq3C3r175cJKwehYdSlt0fk3VTb4jxgxAps2bcLJkyfRpk0b2fYTJ04AAN56661KHbc8evbsiYMHDyI6Olo2CgvkT3/R0tIqs28LCwuMHDkSv/zyC7KzszF48OAiAWrOnDkYN24cZsyYgZ9//rnMYKqqqopZs2Zh1qxZWLJkCebPn1+kTXx8PB4/fowuXboUeww1NTUsXboU7733Xrm/L97e3vjhhx/w9ddfV+jEvcmTJ+Pjjz/G5cuXcfToUcyYMUO2AkNhJf0sFsfd3R19+vTB5s2bMXr0aHTr1q3MWgpO6Nq8eTMcHBzk9mVkZGDo0KHYsmULgywpLQZZIiXSpUsXfPDBB5g8eTL8/f3RvXt3aGtrIyYmBpcvX4aLi0uJo03z58/Hs2fP0LNnT1hbW+PVq1f48ccfoaqqCg8PDwD5H6ceOHAA3bt3x/Tp0+Hq6gqpVIqIiAicOXMG//d//4eOHTtWqvY///wTEokEvXv3RnBwMObNmwc3NzdZILezs8PixYsxZ84cPHnyBP369UOjRo0QFxeHmzdvQltbG4sWLSq1j379+qFLly74v//7PyQnJ6Ndu3a4du0atm/fDqD8I5mBgYHYv39/ke3t27eHra0tWrRoUcFnn+/evXuy1RdiY2ORnp4u68fR0RGOjo4AgD59+mDw4MFYvHgxpFIp3nrrLfj7+2PRokUYNGiQ3Nzp7du3w9vbG1u2bCn3PNnSLFiwAMeOHYOnpyfmz58PQ0ND7Nq1C8ePH8eqVauKTHcpzv/+9z/Z+6S4OZhjx45FcHAwli1bhoCAAHh5eaFZs2aQSqWIjIzEjh07AEBu/vVXX32F+/fvY8GCBbh58ybGjRsHGxsbJCUl4Z9//sGmTZuwaNGiEoNsQb9r1qzByZMny/VaqKioYPny5Rg+fDiA/JHy8hg7dixmzJiBsWPHIisrC15eXnL7y/OzWJKdO3eib9++6NWrF7y8vNC3b1+YmpoiOTkZd+/exblz52RTbnJzc7F9+3a0atUKU6dOLfZ4gwcPxpEjR/D8+XOYmJiU6/kR1SmKPdeMqGGr7AURtmzZInTs2FHQ1tYWNDU1haZNmwoTJ04U/P39ZW0Kr1pw7NgxoX///oKVlZWgpqYmmJqaCgMGDJBbRF4QBCE1NVWYO3eu0KJFC0FNTU3Q19cXXFxchOnTpwuxsbGydnjjrPA32drayp2tX3A2+K1bt4TBgwcLOjo6gq6urjB27FghLi6uyOMPHTokeHp6Cnp6eoK6urpga2srvPvuu8K5c+fknpu2tnaxr1liYqIwefJkwcDAQNDS0hJ69+4tO1P+xx9/LPYxBQrO1i7pVpnF/99U8FoUdyu8okJ6erowa9YswcbGRpBIJELjxo2F2bNnC5mZmXLtCt5D5amtuAsiFCcwMFAYPHiwoK+vL6ipqQlubm5Fjl/WBRHs7OyEVq1aldrPP//8I4wePVqwtrYWVFVVBS0tLcHR0VH4+OOP5d7Lbzp8+LAwcOBAwcTERJBIJEKjRo0ET09P4ddffxWysrJk7Up6f545c0b2mpe0akFhnTt3FgBU6III48aNEwAIXbp0KbKvvD+LJcnMzBTWrVsndO3aVTAwMBAkEolgaGgodOvWTfj222+FFy9eCIKQ/7MEQFi7dm2Jxzp16lSRlVC4agEpE5EgVPF6h0REddzu3bvx3nvv4cqVK+jcubOiy6n37t69Czc3N/z8888lXi6ViKg6MMgSUb3y+++/IyoqCi4uLhCLxbh+/TpWr16NNm3alOsELaq80NBQPH36FN988w0iIiIQEhICLS0tRZdFRPUY58gSUb2iq6uLPXv2YOnSpUhLS4OFhQW8vLywdOlSRZdW7y1ZskR2yeI//viDIZaIahxHZImIiIhIKfGCCERERESklBhkiYiIiEgpMcgSERERkVLiyV6FSKVSREdHQ1dXt1KXQSQiIiKiyhMEASkpKbC0tCzzQjYMsoVER0fDxsZG0WUQERERNWiRkZGwtrYutQ2DbCEFl0SMjIyUXeaPiIiIiGpHcnIybGxs5C5TXRIG2UIKphPo6ekxyBIREREpSHmmePJkLyIiIiJSSgyyRERERKSUGGSJiIiISCkxyBIRERGRUmKQJSIiIiKlxCBLREREREqJQZaIiIiIlBKDLBEREREpJQZZIiIiIlJKDLJElZSWlavoEoiIiBo0BlmiSrgXnYzuqy7gaEC0okshIiJqsBhkiSrh95sReJGWjc9+v40tl8MUXQ4REVGDxCBLVAkLhzhhUidbAMDiY/ew4sR9SKWCgqsiIiJqWBhkiSpBRSzCwiFOmNmvBQBg4z9PMGPfHWTnShVcGRERUcPBIEtUSSKRCJ/0cMB3I90gEYtw6E40pmzzQypPAiMiIqoVDLJEVTSinTV+m+QOLTUVXHqcgDGbruF5SpaiyyIiIqr3GGSJqkGPFqb4/f23YKSthqCoZIzYcBVhCWmKLouIiKheY5AlqiZuNgY48HFnNDbUQkRiOkZsuIo7ka8UXRYREVG9pTRBdtmyZejcuTO0tLRgYGBQbBs/Pz/07NkTBgYGaNSoEfr06YM7d+7Uap3UsNkZa+PAx53hYqWPxLRsjN10HRcexiu6LCIionpJaYJsdnY2Ro4ciY8//rjY/SkpKejbty8aN26MGzdu4PLly9DT00Pfvn2Rk5NTy9VSQ2aiq449H7yFbs2MkZGTh6nb/PGHf6SiyyIiIqp3RIIgKNXil76+vvjiiy/w6tUrue3+/v5o3749IiIiYGNjAwAIDAyEq6srQkJC0LRp03IdPzk5Gfr6+khKSoKenl51l08NSHauFF8fuIs/b0cBAL7q2wKf9GgKkUik4MoasMwk4OVTIOkZoKoJ6FsDelaAmpaiKyMiov9UJItJaqmmGteiRQsYGxvDx8cH33zzDfLy8uDj4wMnJyfY2toqujxqgNQkYnw3yg2mehr49e9QrD79ELFJmVg4xAkqYobZGpGbDSRFAi/D8gPrq6fAy/DXX2e8LP5xGgavQ62eJaBvBehZ//f1f/+qatbmMyEionKoN0FWV1cXFy9exNChQ7FkyRIAQPPmzXH69GlIJCU/zaysLGRlvV4qKTk5ucZrpYZDJBLh6/4tYa6njkXH7mHH9ad4npKFtWNaQ0NVRdHlKR+pFEiNzQ+mL8P/C6pvBNbkaABlfMikZQTo2wA5GUByFJCdCmS+yr/FBZX8OE3DQgG38NdWgES92p4qERGVTaFBduHChVi0aFGpbfz8/ODu7l7msTIyMuDt7Y0uXbrg999/R15eHtasWYMBAwbAz88PmprFj6asWLGizBqIqsqriz1MdDUwfe8dnAqOxUSfm9g80R36WqqKLq3uyXj5RjgtHFgjgLwy1uhV1QIMbIFGdkAj2/++/u++QWNAXfd1W0HIn26QHJ0fapOeFf91TjqQkZh/iw0suW8t49LDrq4lIFGrhheJiIgABc+RTUhIQEJCQqlt7OzsoKGhIbtf0hzZgikFMTExEIvzz2HLzs5Go0aN4OPjgzFjxhR7/OJGZG1sbDhHlmrEtdAX+GC7P1KyctHcTAe+kzvA0qCBfWSdk/nfx//hb4TU8NfhNTOp9MeLVPI/7peFVLv/Qup/X2sbA9U5D1kQ8sN1WWE3N7N8x9M2fT2CWzBt4c2vdS0AFf6BQ0QNl9LMkTU2NoaxsXG1HCs9PR1isVjuRJqC+1KptMTHqaurQ12dHwdS7ejU1Aj7PuoEr6038SguFSM2XMU27w5obqZb9oOVhTQPSIkpOppa8HVKTNnH0DZ5I5zayn+tZw2o1OKvLpEI0DLMv5k7F99GEID0xPxAWyTs/rctOTp/NDktPv8WfbukDgFd86IB982vdcxr9zUgIqqjlGbVgoiICCQmJuLIkSNYvXo1Ll26BABwcHCAjo4OHjx4gNatW8Pb2xufffYZpFIpVq5ciaNHj+L+/fuwsLAoVz9ctYBqQ9SrDEzachMh8anQ05Dgt0nt0cHeUNFllU/BCGVxo6kFH/9Ly1jyTk2n+JBa8PG/mnbNP4/aJghAWsIbYTeqmK+jy37tAEAkzh+5LTXsmgFizsMmIuVTkSymNEHWy8sL27ZtK7L9woUL6NGjBwDg7NmzWLRoEYKCgiAWi9GmTRssW7YMb731Vrn7YZCl2vIqPRtTtvnj1tOXUJOI8dOY1ujnXL4/uGpcTkZ+IJULqW98nVXGSZFiSf4JVXIf/9sCBnb5X2sZVu/H//WFVAqkPS8l7EYDKdGANLfsY4klhcLuf9MZ3vxa2xQQK81y4kRUWdK8/OlPOZlAbgaQm5X/ez43843tBV+Xsr3TNMCofMuZVkW9DLK1hUGWalNmTh4++/02zt6Lg0gELBrihImd7Gq+Y2lefjgq6eP/1Liyj6FjVvLH/7qW/Oi7pkjzgNT4/6YuPCsh7MYAQl7ZxxKrAnoWRQPum19rGTPsElUXQcgPkXJh8r/7RcJkBbYXCamFAmt5Pukpj0lHAfvu1XOsUjDIVgGDLNW23Dwp5h8Jxu4bEQCAaZ5N8WWfFlW7cIIgAOkv/htBDS/68X9SZNmjeup6pX/8z3VV66683Pw/RuTCbqGvU2MBoeTzB2RU1F6P6pYYdo04wk7KJy+nhNHIqgTIwqOZxWxXNLFq/u9viTog0QRUNQp9/d+tSBsNwG0MYNikxktkkK0CBllSBEEQsO58CL4/+wgAMLKdNZa/4wJVlVJGwrLTCs1NLfTxf3Zq6Z2KVfMDaZGP///7WrMRw0l9lpcDpMSWEXbjUOa6vED+f3BFwq5l/ol5BWGX7ycqiVRaKBhWV5gsI1iW51OLGiX6LywWCo0S9dfbJRr/hciStr8ZOsvZXgnmzjPIVgGDLCnSnpsRmHMoCHlSAW83N8TPg0yhmfqs+I//056XfUBdi1I+/rdQil9opEC52fnTFEpbeiwtvnzHUtX6L9y+GXALfa1hwLBbFkHIH0nPy8n/VEWakz/dRJr7xrbcQvfz/mtXsK24+7lvbMuVvy/NK+XYhbeVcezc7EIff2eWvTZ0bVBRLyYcFg6E5Ry1LO92FVW+30vAIFsFDLJUawQhP4zKRlPzL6uaGPUY6XGhMMcLSERlfPSroV/MaKp9/tf6Nvm/QIlqUm7Wf+G2lLCbXvp64TKq2sVPXXjza41Sfi9LpSUEsjcCX4UCYAVDWuEAmJdbwnHKCpulBclqmutYV4lUyhECqzKCWcx2iQbngdcxSrOOLFG9l5VS+sf/OelFHmIIwPC/P9KzBFXEiU1hYtMcmqZNil6tSrNRbT4boqIk6oChff6tJDmZr5cXK2md3YxEICcNSHiUfyuJmi6gplV8kCzPnN/6SiTOX6lCrJr/r4rkv/tv3FRUS7ivUszj3tgna6da6H5ljq1a/MfpBYGVFwOhCmKQJaqKvJz/rlJVwtn/6S/KOIAo/yPVYj7+D5caY/yep3iWlAXjaDX49u0AZyv9Gn9KRNVOVSN/yZ7Slu3JTn8dbktaZzfzFZCdkn+rCLFqDYS2N49TcL+4IFkTgbBQ/2IJRxSpweLUgkI4tYDKxe834PLa/P9cyxoF0mxU/MlUjezyF6+XlHxlufjkTEza6of7McnQVlPBrxPaoVszk2p8IkRKJCv19RXSyh0IOQ+cSNlwagFRTROE/JFYIP/jsOJOpir4V6Pyo6imehrY9+Fb+GjnLVwJeYHJW/2weqQrhrexrp7nQaRM1HUAk+aKroKI6hCOyBbCEVkql+Ro4FVkflDVMavxM0+zc6X48o8AHAmIBgDM7t8SH3RvUrW1ZomIiOqgimQxTqohqgw9S6BxR0DXvFaWT1GTiLF2dGtM7Zp/Qs2Kkw+w5Nh9SKX8O5SIiBouBlmiSkjNTsXmu5uRlJVUa32KxSLMHeSIOQNaAQC2XAnD53tuIytX0Yt6ExERKQaDLFEl7Hu0Dz/d/gl99vfBd/7fIT69nIvCV4P3uzfBj2NaQ1VFhGN3Y+C1xQ/JmfV8bUkiIqJiMMgSVYK9nj2aN2qO9Nx0+Ab7ot+Bflh4dSEikiNqpf+hra3gO7kDdNQluPbkBUb9eg1xyXXgGt5ERES1iCd7FcKTvai8BEHApahL+C3wN9yOvw0AEIvE6GPbB1NcpqClYcsaryEoKgleW/2QkJoFKwNNbPNuDwdT3Rrvl4iIqKbwErVVwCBLlXEr7hZ8An1wKeqSbFsXqy6Y6jwV7cza1ejqApGJ6Zi45SbCEtJgoKUKn0nt0c6WV/wiIiLlxCBbBQyyVBUPEx/CJ9AHp5+ehvS/CyW0NmmNqS5T0d26e40F2sS0bHj7+uFO5CtoqIqxbmxb9HY0q5G+iIiIahKDbBUwyFJ1iEiOwNbgrTgcchg50vwTsRwMHDDFZQr62fWDRFz91yJJz87Fp7tv4/yDeIhFwLLhLhjboXG190NERFSTGGSrgEGWqtPz9OfYcW8H9j7ci/********************************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", "text/plain": ["<Figure size 700x400 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Build models\n", "n_samples = 2024\n", "polygcnn = PolyGCNN(symmetries=g, layers=2, features=(8, 8), degree=2, mode=\"fft\", use_cp_weighted=True)\n", "gat = SpinGAT(n_sites=L, hidden_features=(8,4), n_heads=(2,1), rngs=nnx.Rngs(0))\n", "baseline = nk.models.GCNN(symmetries=g, layers=2, features=(8, 8), param_dtype=np.float64, complex_output=True)\n", "\n", "sampler = nk.sampler.MetropolisExchange(hilbert=hi, graph=g, n_chains=8)\n", "opt = nk.optimizer.Sgd(learning_rate=0.02)\n", "sr = nk.optimizer.SR(diag_shift=0.1)\n", "sampler_base = nk.sampler.MetropolisExchange(hilbert=hi, graph=g, n_chains=8)\n", "opt_base = nk.optimizer.Sgd(learning_rate=0.02)\n", "sr_base = nk.optimizer.SR(diag_shift=0.1)\n", "\n", "# PolyGCNN VMC (few steps)\n", "vs_gcnn = nk.vqs.MCState(sampler, polygcnn, n_samples=n_samples)\n", "vmc_gcnn = nk.VMC(H, opt, variational_state=vs_gcnn, preconditioner=sr)\n", "\n", "# GAT VMC (few steps)\n", "vs_gat = nk.vqs.MCState(sampler, gat, n_samples=n_samples)\n", "vmc_gat = nk.VMC(H, opt, variational_state=vs_gat, preconditioner=sr)\n", "\n", "def run_collect(vmc, vs, H, n_iter):\n", "    import numpy as _np\n", "    iters, energies = [], []\n", "    for k, _ in enumerate(vmc.iter(n_iter), start=1):\n", "        s = vs.expect(H)\n", "        e = s.mean if hasattr(s, 'mean') else s[0]\n", "        iters.append(k)\n", "        energies.append(_np.asarray(e))\n", "    return _np.array(iters), _np.array(energies)\n", "\n", "iters_gcnn, ener_gcnn = run_collect(vmc_gcnn, vs_gcnn, H, 5)\n", "iters_gat,  ener_gat  = run_collect(vmc_gat,  vs_gat,  H, 5)\n", "# Baseline GCNN\n", "vs_base = nk.vqs.MCState(sampler_base, baseline, n_samples=n_samples)\n", "vmc_base = nk.VMC(H, opt_base, variational_state=vs_base, preconditioner=sr_base)\n", "iters_base, ener_base = run_collect(vmc_base, vs_base, H, 5)\n", "\n", "ener_exact = np.full_like(iters_gcnn, fill_value=np.asarray(exact_energy))\n", "\n", "plt.figure(figsize=(7,4))\n", "plt.plot(iters_gcnn, ener_gcnn, label=\"PolyGCNN\")\n", "plt.plot(iters_gat, ener_gat, label=\"GAT\")\n", "plt.plot(iters_base, ener_base, label=\"GCNN (baseline)\")\n", "# plot exact only once\n", "plt.hlines(np.asarray(exact_energy), xmin=0, xmax=max(iters_gcnn.max(), iters_gat.max()), colors=\"k\", linestyles=\"--\", label=\"Exact\")\n", "plt.xlabel(\"Iteration\")\n", "plt.ylabel(\"Energy\")\n", "plt.legend()\n", "plt.title(f\"<PERSON><PERSON><PERSON> L={L}: PolyGCNN vs GAT\")\n", "plt.tight_layout()\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "quantum", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 2}