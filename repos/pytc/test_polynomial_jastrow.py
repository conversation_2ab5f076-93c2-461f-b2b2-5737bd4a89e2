#!/usr/bin/env python3
"""
Test script for polynomial Jastrow implementations.
Verifies basic functionality, parameter initialization, and integration with PyTC.
"""
import sys
import os
sys.path.insert(0, os.path.abspath('.'))

import jax
import jax.numpy as jnp
from jax import random
from pyscf import gto, scf

# Enable float64 for quantum chemistry
jax.config.update("jax_enable_x64", True)

# Import PyTC components
from pytc.autodiff.jastrow import (
    CPJastrowEN, CPJastrowEE, CPJastrowEEN, 
    NuclearCusp, CompositeJastrow
)
from pytc.autodiff.ansatz.sj import SlaterJastrow
from pytc.autodiff.ansatz.det import SlaterDet
from pytc.autodiff.mcmc import sample
from pytc.autodiff.mcmc_utils import analyze_energies


def test_basic_functionality():
    """Test basic polynomial Jastrow functionality."""
    print("=== Testing Basic Polynomial Jastrow Functionality ===")
    
    # Create simple H2 molecule
    mol = gto.M(atom='H 0 0 0; H 0 0 1.4', basis='sto-3g', unit='bohr', verbose=0)
    
    # Test each polynomial Jastrow type
    key = random.PRNGKey(42)
    
    # Test CPJastrowEN
    print("Testing CPJastrowEN...")
    cp_en = CPJastrowEN(mol, degree=3, rank=4)
    params_en = cp_en.init_params(key=key)
    
    # Test evaluation
    r1 = jnp.array([0.0, 0.0, 0.5])
    r2 = jnp.array([0.0, 0.0, -0.5])
    
    val_en = cp_en._compute(r1, r2, params_en)
    print(f"  CPJastrowEN value: {val_en:.6f}")
    
    # Test gradients
    grad_en, lap_en = cp_en.get_log_grads_r1(r1, r2, params_en)
    print(f"  CPJastrowEN gradient norm: {jnp.linalg.norm(grad_en):.6f}")
    print(f"  CPJastrowEN laplacian: {lap_en:.6f}")
    
    # Test CPJastrowEE
    print("Testing CPJastrowEE...")
    cp_ee = CPJastrowEE(mol, degree=2, rank=4)
    params_ee = cp_ee.init_params(key=key)
    
    val_ee = cp_ee._compute(r1, r2, params_ee)
    print(f"  CPJastrowEE value: {val_ee:.6f}")
    
    grad_ee, lap_ee = cp_ee.get_log_grads_r1(r1, r2, params_ee)
    print(f"  CPJastrowEE gradient norm: {jnp.linalg.norm(grad_ee):.6f}")
    print(f"  CPJastrowEE laplacian: {lap_ee:.6f}")
    
    # Test CPJastrowEEN
    print("Testing CPJastrowEEN...")
    cp_een = CPJastrowEEN(mol, degree=3, rank=6)
    params_een = cp_een.init_params(key=key)
    
    val_een = cp_een._compute(r1, r2, params_een)
    print(f"  CPJastrowEEN value: {val_een:.6f}")
    
    grad_een, lap_een = cp_een.get_log_grads_r1(r1, r2, params_een)
    print(f"  CPJastrowEEN gradient norm: {jnp.linalg.norm(grad_een):.6f}")
    print(f"  CPJastrowEEN laplacian: {lap_een:.6f}")
    
    print("✓ Basic functionality tests passed!\n")


def test_sparse_variants():
    """Test sparse CP variants."""
    print("=== Testing Sparse CP Variants ===")
    
    mol = gto.M(atom='H 0 0 0; H 0 0 1.4', basis='sto-3g', unit='bohr', verbose=0)
    key = random.PRNGKey(123)
    
    r1 = jnp.array([0.0, 0.0, 0.5])
    r2 = jnp.array([0.0, 0.0, -0.5])
    
    # Test CP_sparse_LU
    print("Testing CP_sparse_LU...")
    cp_sparse_lu = CPJastrowEEN(mol, degree=3, rank=6, cp_variant='CP_sparse_LU')
    params_sparse_lu = cp_sparse_lu.init_params(key=key)
    
    val_sparse_lu = cp_sparse_lu._compute(r1, r2, params_sparse_lu)
    print(f"  CP_sparse_LU value: {val_sparse_lu:.6f}")
    
    # Test CP_sparse_degree
    print("Testing CP_sparse_degree...")
    cp_sparse_deg = CPJastrowEEN(mol, degree=3, rank=6, cp_variant='CP_sparse_degree')
    params_sparse_deg = cp_sparse_deg.init_params(key=key)
    
    val_sparse_deg = cp_sparse_deg._compute(r1, r2, params_sparse_deg)
    print(f"  CP_sparse_degree value: {val_sparse_deg:.6f}")
    
    # Test without final activation (pure polynomial)
    print("Testing pure polynomial (no activation)...")
    cp_pure = CPJastrowEEN(mol, degree=3, rank=6, use_final_activation=False)
    params_pure = cp_pure.init_params(key=key)
    
    val_pure = cp_pure._compute(r1, r2, params_pure)
    print(f"  Pure polynomial value: {val_pure:.6f}")
    
    print("✓ Sparse variant tests passed!\n")


def test_composite_integration():
    """Test integration with CompositeJastrow."""
    print("=== Testing CompositeJastrow Integration ===")
    
    mol = gto.M(atom='H 0 0 0; H 0 0 1.4', basis='sto-3g', unit='bohr', verbose=0)
    key = random.PRNGKey(456)
    
    # Create components
    ncusp = NuclearCusp(mol)
    cp_en = CPJastrowEN(mol, degree=2, rank=4)
    cp_ee = CPJastrowEE(mol, degree=2, rank=4)
    cp_een = CPJastrowEEN(mol, degree=3, rank=6)
    
    # Initialize parameters
    params_ncusp = ncusp.init_params()
    params_en = cp_en.init_params(key=key)
    params_ee = cp_ee.init_params(key=key)
    params_een = cp_een.init_params(key=key)
    
    # Create composite
    composite = CompositeJastrow([ncusp, cp_en, cp_ee, cp_een])
    composite_params = [params_ncusp, params_en, params_ee, params_een]
    
    # Test evaluation
    r1 = jnp.array([0.0, 0.0, 0.5])
    r2 = jnp.array([0.0, 0.0, -0.5])
    
    composite_val = composite._compute(r1, r2, composite_params)
    print(f"Composite Jastrow value: {composite_val:.6f}")
    
    # Test gradients
    grad_comp, lap_comp = composite.get_log_grads_r1(r1, r2, composite_params)
    print(f"Composite gradient norm: {jnp.linalg.norm(grad_comp):.6f}")
    print(f"Composite laplacian: {lap_comp:.6f}")
    
    print("✓ CompositeJastrow integration tests passed!\n")


def test_vmc_sampling():
    """Test VMC sampling with polynomial Jastrow."""
    print("=== Testing VMC Sampling ===")
    
    # Create H2 molecule
    mol = gto.M(atom='H 0 0 0; H 0 0 1.4', basis='sto-3g', unit='bohr', verbose=0)
    mf = scf.RHF(mol)
    mf.kernel()
    
    # Create polynomial Jastrow ansatz
    key = random.PRNGKey(789)
    
    ncusp = NuclearCusp(mol)
    cp_en = CPJastrowEN(mol, degree=2, rank=4, activation_scale=0.05)
    cp_ee = CPJastrowEE(mol, degree=2, rank=4, activation_scale=0.05)
    cp_een = CPJastrowEEN(mol, degree=3, rank=6, activation_scale=0.05)
    
    composite = CompositeJastrow([ncusp, cp_en, cp_ee, cp_een])
    
    # Initialize parameters
    params_ncusp = ncusp.init_params()
    params_en = cp_en.init_params(key=key)
    params_ee = cp_ee.init_params(key=key)
    params_een = cp_een.init_params(key=key)
    
    composite_params = [params_ncusp, params_en, params_ee, params_een]
    
    # Create Slater-Jastrow ansatz
    det = SlaterDet(mol, mf.mo_coeff)
    sj_ansatz = SlaterJastrow(mol, composite, [det])
    
    # Prepare parameters for sampling
    combined_params = (composite_params, jnp.array([1.0]))
    
    # Run short sampling test
    print("Running VMC sampling...")
    results = sample(
        sj_ansatz,
        n_walkers=64,
        n_steps=100,
        step_size=0.02,
        burn_in_steps=20,
        use_importance_sampling=True,
        thinning=2,
        params=combined_params,
        key=key,
    )
    
    # Analyze results
    stats = analyze_energies(results)
    energy = stats['mean']
    error = stats['error']
    acceptance = jnp.mean(results['acceptance_rates'])
    
    print(f"HF energy: {mf.e_tot:.6f} Ha")
    print(f"VMC energy: {energy:.6f} ± {error:.6f} Ha")
    print(f"Energy difference: {energy - mf.e_tot:.6f} Ha")
    print(f"Acceptance rate: {acceptance:.3f}")
    
    # Basic sanity checks
    assert 0.3 < acceptance < 0.9, f"Acceptance rate {acceptance:.3f} outside reasonable range"
    assert abs(energy - mf.e_tot) < 0.5, f"Energy difference {abs(energy - mf.e_tot):.3f} too large"
    
    print("✓ VMC sampling tests passed!\n")


def main():
    """Run all tests."""
    print("Testing Polynomial Jastrow Implementation")
    print("=" * 50)
    
    try:
        test_basic_functionality()
        test_sparse_variants()
        test_composite_integration()
        test_vmc_sampling()
        
        print("🎉 All tests passed successfully!")
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
