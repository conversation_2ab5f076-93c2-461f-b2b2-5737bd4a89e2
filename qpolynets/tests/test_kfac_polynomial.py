#!/usr/bin/env python3
"""
Test script to verify KFAC compatibility with polynomial Jastrow factors.
"""

import sys
import os
import jax
import jax.numpy as jnp
from jax import random

# Enable float64 precision
jax.config.update("jax_enable_x64", True)

# Add PyTC to path
pytc_path = os.path.abspath('repos/pytc')
if pytc_path not in sys.path:
    sys.path.insert(0, pytc_path)

# PySCF for quantum chemistry
from pyscf import gto, scf

# Import polynomial Jastrow components
from pytc.autodiff.jastrow.polynomial import CPJastrowEN, CPJastrowEE, CPJastrowEEN


def test_kfac_basic_functionality():
    """Test basic KFAC functionality with polynomial Jastrow."""
    print("=== Testing KFAC Basic Functionality ===")
    
    # Create simple H2 molecule
    mol = gto.M(atom='H 0 0 0; H 0 0 1.4', basis='sto-3g', unit='bohr', verbose=0)
    
    # Test positions
    r1 = jnp.array([0.0, 0.0, 0.5])
    r2 = jnp.array([0.0, 0.0, -0.5])
    key = random.PRNGKey(42)
    
    print("Testing CPJastrowEEN with and without KFAC...")
    
    # Test without KFAC
    print("  Without KFAC:")
    cp_een_no_kfac = CPJastrowEEN(mol, degree=3, rank=4, use_kfac=False)
    params_no_kfac = cp_een_no_kfac.init_params(key=key)
    val_no_kfac = cp_een_no_kfac._compute(r1, r2, params_no_kfac)
    print(f"    Value: {val_no_kfac:.6f}")
    
    # Test with KFAC
    print("  With KFAC:")
    cp_een_kfac = CPJastrowEEN(mol, degree=3, rank=4, use_kfac=True)
    params_kfac = cp_een_kfac.init_params(key=key)
    val_kfac = cp_een_kfac._compute(r1, r2, params_kfac)
    print(f"    Value: {val_kfac:.6f}")
    
    # Values should be identical (KFAC only affects optimization, not forward pass)
    assert jnp.allclose(val_no_kfac, val_kfac, atol=1e-10), "KFAC should not change forward pass values"
    print("  ✓ Values match - KFAC doesn't affect forward pass")
    
    print("✓ Basic KFAC functionality test passed!\n")


def test_kfac_with_sparsity():
    """Test KFAC with different sparsity patterns."""
    print("=== Testing KFAC with Sparsity Patterns ===")
    
    mol = gto.M(atom='H 0 0 0; H 0 0 1.4', basis='sto-3g', unit='bohr', verbose=0)
    r1 = jnp.array([0.0, 0.0, 0.5])
    r2 = jnp.array([0.0, 0.0, -0.5])
    key = random.PRNGKey(123)
    
    # Test different CP variants with KFAC
    variants = ['CP', 'CP_sparse_LU', 'CP_sparse_degree']
    
    for variant in variants:
        print(f"  Testing {variant} with KFAC:")
        
        # Without KFAC
        cp_no_kfac = CPJastrowEEN(mol, degree=3, rank=6, cp_variant=variant, use_kfac=False)
        params_no_kfac = cp_no_kfac.init_params(key=key)
        val_no_kfac = cp_no_kfac._compute(r1, r2, params_no_kfac)
        
        # With KFAC
        cp_kfac = CPJastrowEEN(mol, degree=3, rank=6, cp_variant=variant, use_kfac=True)
        params_kfac = cp_kfac.init_params(key=key)
        val_kfac = cp_kfac._compute(r1, r2, params_kfac)
        
        print(f"    No KFAC: {val_no_kfac:.6f}")
        print(f"    With KFAC: {val_kfac:.6f}")
        
        # Values should be identical
        assert jnp.allclose(val_no_kfac, val_kfac, atol=1e-10), f"KFAC should not change values for {variant}"
        print(f"    ✓ {variant} values match")
    
    print("✓ KFAC with sparsity patterns test passed!\n")


def test_kfac_all_components():
    """Test KFAC with all Jastrow components."""
    print("=== Testing KFAC with All Components ===")
    
    mol = gto.M(atom='H 0 0 0; H 0 0 1.4', basis='sto-3g', unit='bohr', verbose=0)
    r1 = jnp.array([0.0, 0.0, 0.5])
    r2 = jnp.array([0.0, 0.0, -0.5])
    key = random.PRNGKey(456)
    
    components = [
        ("CPJastrowEN", CPJastrowEN),
        ("CPJastrowEE", CPJastrowEE),
        ("CPJastrowEEN", CPJastrowEEN)
    ]
    
    for name, cls in components:
        print(f"  Testing {name}:")
        
        # Without KFAC
        comp_no_kfac = cls(mol, degree=2, rank=4, use_kfac=False)
        params_no_kfac = comp_no_kfac.init_params(key=key)
        val_no_kfac = comp_no_kfac._compute(r1, r2, params_no_kfac)
        
        # With KFAC
        comp_kfac = cls(mol, degree=2, rank=4, use_kfac=True)
        params_kfac = comp_kfac.init_params(key=key)
        val_kfac = comp_kfac._compute(r1, r2, params_kfac)
        
        print(f"    No KFAC: {val_no_kfac:.6f}")
        print(f"    With KFAC: {val_kfac:.6f}")
        
        # Values should be identical
        assert jnp.allclose(val_no_kfac, val_kfac, atol=1e-10), f"KFAC should not change values for {name}"
        print(f"    ✓ {name} values match")
    
    print("✓ KFAC with all components test passed!\n")


def test_kfac_parameter_compatibility():
    """Test that KFAC parameters are properly passed through."""
    print("=== Testing KFAC Parameter Compatibility ===")
    
    mol = gto.M(atom='H 0 0 0; H 0 0 1.4', basis='sto-3g', unit='bohr', verbose=0)
    
    # Test that use_kfac parameter is properly stored
    cp_kfac_true = CPJastrowEEN(mol, use_kfac=True)
    cp_kfac_false = CPJastrowEEN(mol, use_kfac=False)
    
    assert cp_kfac_true.use_kfac == True, "use_kfac=True should be stored"
    assert cp_kfac_false.use_kfac == False, "use_kfac=False should be stored"
    
    print("  ✓ KFAC parameter properly stored")
    
    # Test that other parameters still work with KFAC
    cp_custom = CPJastrowEEN(mol, degree=4, rank=10, cp_variant='CP_sparse_LU', 
                            activation_scale=0.05, use_final_activation=False, use_kfac=True)
    
    assert cp_custom.degree == 4, "degree parameter should work with KFAC"
    assert cp_custom.rank == 10, "rank parameter should work with KFAC"
    assert cp_custom.cp_variant == 'CP_sparse_LU', "cp_variant should work with KFAC"
    assert cp_custom.activation_scale == 0.05, "activation_scale should work with KFAC"
    assert cp_custom.use_final_activation == False, "use_final_activation should work with KFAC"
    assert cp_custom.use_kfac == True, "use_kfac should be set"
    
    print("  ✓ All parameters compatible with KFAC")
    print("✓ KFAC parameter compatibility test passed!\n")


def main():
    """Run all KFAC tests."""
    print("Testing KFAC Implementation for Polynomial Jastrow Factors")
    print("=" * 60)
    
    try:
        test_kfac_basic_functionality()
        test_kfac_with_sparsity()
        test_kfac_all_components()
        test_kfac_parameter_compatibility()
        
        print("🎉 All KFAC tests passed!")
        print("\nKFAC implementation is working correctly:")
        print("  ✓ KFAC registration doesn't affect forward pass values")
        print("  ✓ KFAC works with all sparsity patterns")
        print("  ✓ KFAC works with all Jastrow components")
        print("  ✓ KFAC parameter is properly handled")
        print("\nYou can now use KFAC optimization by setting use_kfac=True!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
