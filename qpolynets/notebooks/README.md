# qpolynets notebooks

This folder contains notebooks for polynomial neural networks and quantum chemistry applications.

## Neural vs Polynomial Jastrow Comparison

### Core Files
- **`model_analysis.py`** - Comprehensive utility module for model analysis
- **`neural_vs_polynomial_comparison_clean.ipynb`** - Clean, modular comparison notebook
- **`neural_vs_polynomial_comparison_fixed.ipynb`** - Original notebook with auto-saving

### Key Features
- **Modular Design**: All utilities in separate module
- **Automatic Saving**: Models saved during training with checkpoints
- **Weight Analysis**: Comprehensive parameter inspection
- **Easy Loading**: Simple checkpoint management

### Usage
```python
from model_analysis import create_full_ansatz, extract_neural_weights
# Create and analyze models with clean API
```

## Other Notebooks

Planned migration from qgat/notebooks:
- test_polynomial_gcnn.ipynb
- working_chain_polygcnn_comparison.ipynb
- comprehensive_polygcnn_comparison.ipynb
- honeycomb_heisenberg_polygcnn_comparison.ipynb

Included now:
- 01_polygcnn_basic.ipynb (smoke example)
- 10_explore_polynomial_layers.ipynb (simple layer smoke)

