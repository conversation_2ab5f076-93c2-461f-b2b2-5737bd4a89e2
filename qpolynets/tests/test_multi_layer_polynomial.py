#!/usr/bin/env python3
"""
Test script to verify multi-layer polynomial Jastrow implementation.
"""

import sys
import os
import jax
import jax.numpy as jnp
from jax import random

# Enable float64 precision
jax.config.update("jax_enable_x64", True)

# Add PyTC to path
pytc_path = os.path.abspath('../../repos/pytc')
if pytc_path not in sys.path:
    sys.path.insert(0, pytc_path)

# PySCF for quantum chemistry
from pyscf import gto

# Import polynomial Jastrow components
from pytc.autodiff.jastrow.polynomial import CPJastrowEN, CPJastrowEE, CPJastrowEEN


def test_backward_compatibility():
    """Test that single layer behavior is unchanged."""
    print("=== Testing Backward Compatibility ===")
    
    mol = gto.M(atom='H 0 0 0; H 0 0 1.4', basis='sto-3g', unit='bohr', verbose=0)
    key = random.PRNGKey(42)
    r1 = jnp.array([0.0, 0.0, 0.5])
    r2 = jnp.array([0.0, 0.0, -0.5])
    
    # Single layer (current behavior)
    cp_single = CPJastrowEEN(mol, degree=3, rank=8)
    params_single = cp_single.init_params(key=key)
    val_single = cp_single._compute(r1, r2, params_single)
    
    print(f"  Single layer value: {val_single:.6f}")
    print(f"  Is multi-layer: {cp_single.is_multi_layer}")
    print(f"  Number of layers: {cp_single.num_layers}")
    
    # Check parameter structure
    assert 'U' in params_single, "Single layer should have U parameters"
    assert 'C' in params_single, "Single layer should have C parameters"
    assert 'layers' not in params_single, "Single layer should not have layers structure"
    
    # Check that it's not multi-layer
    assert not cp_single.is_multi_layer, "Single layer should not be multi-layer"
    assert cp_single.num_layers == 1, "Single layer should have 1 layer"
    
    print("  ✓ Backward compatibility verified")


def test_multi_layer_creation():
    """Test multi-layer creation with different configurations."""
    print("\n=== Testing Multi-Layer Creation ===")
    
    mol = gto.M(atom='H 0 0 0; H 0 0 1.4', basis='sto-3g', unit='bohr', verbose=0)
    
    # Test different multi-layer configurations
    configs = [
        {
            'name': 'Simple 2-layer',
            'hidden_dims': [8],
            'degree': 3,
            'rank': 6
        },
        {
            'name': '3-layer with lists',
            'hidden_dims': [16, 8],
            'degree': [4, 3, 2],
            'rank': [12, 8, 4]
        },
        {
            'name': 'Different variants per layer',
            'hidden_dims': [12, 6],
            'degree': [3, 3, 2],
            'rank': [8, 6, 4],
            'cp_variant': ['CP_sparse_degree', 'CP_sparse_LU', 'CP']
        }
    ]
    
    for config in configs:
        print(f"\n  Testing {config['name']}:")
        
        # Remove name from config for constructor
        test_config = {k: v for k, v in config.items() if k != 'name'}
        
        cp = CPJastrowEEN(mol, **test_config)
        
        print(f"    Is multi-layer: {cp.is_multi_layer}")
        print(f"    Number of layers: {cp.num_layers}")
        print(f"    Hidden dims: {cp.hidden_dims}")
        print(f"    Degrees: {cp.degrees}")
        print(f"    Ranks: {cp.ranks}")
        print(f"    CP variants: {cp.cp_variants}")
        
        # Check layer dimensions
        expected_layers = len(config['hidden_dims']) + 1
        assert cp.num_layers == expected_layers, f"Should have {expected_layers} layers"
        assert cp.is_multi_layer, "Should be multi-layer"
        
        # Test parameter initialization
        key = random.PRNGKey(123)
        params = cp.init_params(key=key)
        
        assert 'layers' in params, "Multi-layer should have layers structure"
        assert len(params['layers']) == expected_layers, f"Should have {expected_layers} layer parameters"
        
        # Check each layer structure
        for i, layer_params in enumerate(params['layers']):
            assert 'U' in layer_params, f"Layer {i} should have U parameters"
            assert 'C' in layer_params, f"Layer {i} should have C parameters"
            assert 'bias' in layer_params, f"Layer {i} should have bias parameters"
            
            expected_degree = cp.degrees[i]
            assert len(layer_params['U']) == expected_degree, f"Layer {i} should have {expected_degree} U matrices"
        
        print(f"    ✓ Configuration valid")


def test_multi_layer_forward_pass():
    """Test multi-layer forward pass functionality."""
    print("\n=== Testing Multi-Layer Forward Pass ===")
    
    mol = gto.M(atom='H 0 0 0; H 0 0 1.4', basis='sto-3g', unit='bohr', verbose=0)
    key = random.PRNGKey(456)
    r1 = jnp.array([0.0, 0.0, 0.5])
    r2 = jnp.array([0.0, 0.0, -0.5])
    
    # Test 2-layer network
    cp_2layer = CPJastrowEEN(mol, hidden_dims=[8], degree=3, rank=6)
    params_2layer = cp_2layer.init_params(key=key)
    val_2layer = cp_2layer._compute(r1, r2, params_2layer)
    
    print(f"  2-layer value: {val_2layer:.6f}")
    assert jnp.isfinite(val_2layer), "2-layer value should be finite"
    
    # Test 3-layer network
    cp_3layer = CPJastrowEEN(mol, hidden_dims=[16, 8], degree=[4, 3, 2], rank=[12, 8, 4])
    params_3layer = cp_3layer.init_params(key=key)
    val_3layer = cp_3layer._compute(r1, r2, params_3layer)
    
    print(f"  3-layer value: {val_3layer:.6f}")
    assert jnp.isfinite(val_3layer), "3-layer value should be finite"
    
    # Test with activations between layers
    cp_with_act = CPJastrowEEN(mol, hidden_dims=[8], degree=3, rank=6, 
                              use_activation_between_layers=True)
    params_with_act = cp_with_act.init_params(key=key)
    val_with_act = cp_with_act._compute(r1, r2, params_with_act)
    
    print(f"  With activations: {val_with_act:.6f}")
    assert jnp.isfinite(val_with_act), "Value with activations should be finite"
    
    # Values should be different (due to different architectures)
    assert not jnp.allclose(val_2layer, val_3layer), "Different architectures should give different values"
    
    print("  ✓ Forward pass functionality verified")


def test_multi_layer_with_sparsity():
    """Test multi-layer with different sparsity patterns."""
    print("\n=== Testing Multi-Layer with Sparsity ===")
    
    mol = gto.M(atom='H 0 0 0; H 0 0 1.4', basis='sto-3g', unit='bohr', verbose=0)
    key = random.PRNGKey(789)
    r1 = jnp.array([0.0, 0.0, 0.5])
    r2 = jnp.array([0.0, 0.0, -0.5])
    
    # Test different sparsity patterns per layer
    sparsity_configs = [
        ['CP', 'CP'],  # Dense layers
        ['CP_sparse_LU', 'CP_sparse_degree'],  # Different sparsity per layer
        ['CP_sparse_degree_sawtooth', 'CP_sparse_LU'],  # Complex sparsity
    ]
    
    for i, variants in enumerate(sparsity_configs):
        print(f"\n  Testing sparsity config {i+1}: {variants}")
        
        cp = CPJastrowEEN(mol, hidden_dims=[8], degree=[3, 2], rank=[6, 4], 
                         cp_variant=variants)
        params = cp.init_params(key=key)
        val = cp._compute(r1, r2, params)
        
        print(f"    Value: {val:.6f}")
        assert jnp.isfinite(val), f"Value for config {i+1} should be finite"
        
        # Check that different variants are stored
        assert cp.cp_variants == variants, f"CP variants should match input"
        
        print(f"    ✓ Sparsity config {i+1} working")


def test_multi_layer_kfac_compatibility():
    """Test multi-layer KFAC compatibility."""
    print("\n=== Testing Multi-Layer KFAC Compatibility ===")
    
    mol = gto.M(atom='H 0 0 0; H 0 0 1.4', basis='sto-3g', unit='bohr', verbose=0)
    key = random.PRNGKey(101112)
    r1 = jnp.array([0.0, 0.0, 0.5])
    r2 = jnp.array([0.0, 0.0, -0.5])
    
    # Test without KFAC
    cp_no_kfac = CPJastrowEEN(mol, hidden_dims=[8], degree=3, rank=6, use_kfac=False)
    params_no_kfac = cp_no_kfac.init_params(key=key)
    val_no_kfac = cp_no_kfac._compute(r1, r2, params_no_kfac)
    
    # Test with KFAC
    cp_kfac = CPJastrowEEN(mol, hidden_dims=[8], degree=3, rank=6, use_kfac=True)
    params_kfac = cp_kfac.init_params(key=key)
    val_kfac = cp_kfac._compute(r1, r2, params_kfac)
    
    print(f"  No KFAC: {val_no_kfac:.6f}")
    print(f"  With KFAC: {val_kfac:.6f}")
    
    # Values should be identical (KFAC doesn't change forward pass)
    assert jnp.allclose(val_no_kfac, val_kfac, atol=1e-10), "KFAC should not change forward pass values"
    
    print("  ✓ KFAC compatibility verified")


def test_all_jastrow_types():
    """Test multi-layer functionality for all Jastrow types."""
    print("\n=== Testing All Jastrow Types ===")
    
    mol = gto.M(atom='H 0 0 0; H 0 0 1.4', basis='sto-3g', unit='bohr', verbose=0)
    key = random.PRNGKey(131415)
    r1 = jnp.array([0.0, 0.0, 0.5])
    r2 = jnp.array([0.0, 0.0, -0.5])
    
    jastrow_types = [
        ('EN', CPJastrowEN),
        ('EE', CPJastrowEE), 
        ('EEN', CPJastrowEEN)
    ]
    
    for name, jastrow_class in jastrow_types:
        print(f"\n  Testing {name}:")
        
        # Single layer
        cp_single = jastrow_class(mol, degree=3, rank=6)
        params_single = cp_single.init_params(key=key)
        val_single = cp_single._compute(r1, r2, params_single)
        
        # Multi-layer
        cp_multi = jastrow_class(mol, hidden_dims=[8], degree=[3, 2], rank=[6, 4])
        params_multi = cp_multi.init_params(key=key)
        val_multi = cp_multi._compute(r1, r2, params_multi)
        
        print(f"    Single layer: {val_single:.6f}")
        print(f"    Multi-layer: {val_multi:.6f}")
        
        assert jnp.isfinite(val_single), f"{name} single layer should be finite"
        assert jnp.isfinite(val_multi), f"{name} multi-layer should be finite"
        assert not cp_single.is_multi_layer, f"{name} single should not be multi-layer"
        assert cp_multi.is_multi_layer, f"{name} multi should be multi-layer"
        
        print(f"    ✓ {name} working correctly")


def main():
    """Run all multi-layer tests."""
    print("Testing Multi-Layer Polynomial Jastrow Implementation")
    print("=" * 60)
    
    try:
        test_backward_compatibility()
        test_multi_layer_creation()
        test_multi_layer_forward_pass()
        test_multi_layer_with_sparsity()
        test_multi_layer_kfac_compatibility()
        test_all_jastrow_types()
        
        print("\n🎉 All multi-layer tests passed!")
        print("\nMulti-layer polynomial implementation is working correctly:")
        print("  ✓ Backward compatibility maintained")
        print("  ✓ Multi-layer creation and configuration")
        print("  ✓ Forward pass functionality")
        print("  ✓ Sparsity pattern support per layer")
        print("  ✓ KFAC compatibility")
        print("  ✓ All Jastrow types (EN, EE, EEN) supported")
        print("\nUsage examples:")
        print("  # Single layer (unchanged)")
        print("  cp = CPJastrowEEN(mol, degree=3, rank=8)")
        print("  # Multi-layer")
        print("  cp = CPJastrowEEN(mol, hidden_dims=[16, 8], degree=[4, 3, 2], rank=[12, 8, 4])")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
