#!/usr/bin/env python3
"""
Example showing how to use the new cleaned-up parameter counting system.
This replaces the old manual parameter counting code in notebooks.
"""

import sys
import os
import jax
import jax.numpy as jnp

# Enable float64 precision
jax.config.update("jax_enable_x64", True)

# Add PyTC to path
pytc_path = os.path.abspath('../../repos/pytc')
if pytc_path not in sys.path:
    sys.path.insert(0, pytc_path)

# PySCF for quantum chemistry
from pyscf import gto, scf

# Import the new parameter counting functions
from model_analysis import (
    count_parameters, count_nonzero_parameters, count_effective_parameters,
    analyze_full_ansatz_parameters, create_full_ansatz
)

def example_new_parameter_counting():
    """Example of using the new parameter counting system."""
    print("=== New Parameter Counting System Example ===")
    
    # Setup molecule
    mol = gto.M(atom='H 0 0 0; H 0 0 1.4', basis='cc-pvtz', unit='bohr', verbose=0)
    mf = scf.RHF(mol)
    mf.kernel()
    
    # Configuration
    POLY_DEGREE = 4
    POLY_RANK = 8
    NEURAL_LAYERS = [8, 8]
    
    print(f"Molecule: H2, basis: cc-pvtz")
    print(f"Configuration: degree={POLY_DEGREE}, rank={POLY_RANK}")
    
    # =============================================================================
    # NEW WAY: Clean and Simple
    # =============================================================================
    
    print(f"\n=== Creating Ansätze (New Way) ===")
    
    # Create ansätze with instance access (fixes the mask issue!)
    neural_ansatz, neural_combined, _, neural_instances = create_full_ansatz(
        mol, mf, "neural", layer_widths=NEURAL_LAYERS
    )
    
    cp_ansatz, cp_combined, _, cp_instances = create_full_ansatz(
        mol, mf, "polynomial", degree=POLY_DEGREE, rank=POLY_RANK, cp_variant='CP'
    )
    
    sparse_ansatz, sparse_combined, _, sparse_instances = create_full_ansatz(
        mol, mf, "polynomial", degree=POLY_DEGREE, rank=POLY_RANK, cp_variant='CP_sparse_LU'
    )
    
    # =============================================================================
    # COMPREHENSIVE ANALYSIS: One Function Call
    # =============================================================================
    
    print(f"\n=== Full Ansatz Analysis (New Way) ===")
    
    models = [
        ("Neural Network", neural_combined, neural_instances),
        ("Polynomial CP", cp_combined, cp_instances),
        ("CP sparse_LU", sparse_combined, sparse_instances)
    ]
    
    for name, combined_params, instances in models:
        # Single function call for complete analysis!
        analysis = analyze_full_ansatz_parameters(combined_params, instances)
        summary = analysis['summary']
        
        print(f"\n{name}:")
        print(f"  Total parameters:     {summary['total_parameters']:4d}")
        print(f"  Effective parameters: {summary['total_effective']:4d}")
        print(f"  Overall sparsity:     {summary['overall_sparsity']:.3f}")
        print(f"  Parameter reduction:  {(1 - summary['overall_sparsity']) * 100:.1f}%")
        
        # Component breakdown
        print(f"  Component breakdown:")
        for comp_name, count in summary['component_breakdown'].items():
            comp_analysis = analysis['components'][comp_name]
            if 'sparsity_info' in comp_analysis and comp_analysis['sparsity_info']['has_sparsity']:
                effective = comp_analysis['effective_analysis']['effective_parameters']
                sparsity = comp_analysis['effective_analysis']['sparsity_ratio']
                print(f"    {comp_name}: {count:3d} total, {effective:3d} effective (sparsity: {sparsity:.3f})")
            else:
                print(f"    {comp_name}: {count:3d} parameters")
    
    # =============================================================================
    # DETAILED COMPONENT ANALYSIS
    # =============================================================================
    
    print(f"\n=== Detailed EEN Component Analysis ===")
    
    # Extract EEN component for detailed analysis
    sparse_een_params = sparse_combined[0][3]  # [composite_params][EEN_component]
    sparse_een_instance = sparse_instances['een']
    
    # Basic counts
    total = count_parameters(sparse_een_params)
    nonzero = count_nonzero_parameters(sparse_een_params, sparse_een_instance)
    effective_analysis = count_effective_parameters(sparse_een_params, sparse_een_instance)
    
    print(f"CP sparse_LU EEN component:")
    print(f"  Total stored:         {total}")
    print(f"  Non-zero stored:      {nonzero}")
    print(f"  Effective (compute):  {effective_analysis['effective_parameters']}")
    print(f"  Sparsity ratio:       {effective_analysis['sparsity_ratio']:.3f}")
    print(f"  Computational saving: {(1 - effective_analysis['sparsity_ratio']) * 100:.1f}%")
    
    # Mask verification
    has_masks = hasattr(sparse_een_instance, 'masks') and sparse_een_instance.masks is not None
    print(f"  Has masks:            {has_masks}")
    if has_masks:
        print(f"  Number of masks:      {len(sparse_een_instance.masks)}")
    
    # =============================================================================
    # COMPARISON TABLE
    # =============================================================================
    
    print(f"\n=== Comparison Table ===")
    print(f"{'Model':<20} {'Total':<8} {'Effective':<10} {'Sparsity':<10} {'Reduction':<10}")
    print(f"{'-'*20} {'-'*8} {'-'*10} {'-'*10} {'-'*10}")
    
    for name, combined_params, instances in models:
        analysis = analyze_full_ansatz_parameters(combined_params, instances)
        summary = analysis['summary']
        
        total_params = summary['total_parameters']
        effective_params = summary['total_effective']
        sparsity = summary['overall_sparsity']
        reduction = (1 - sparsity) * 100
        
        print(f"{name:<20} {total_params:<8} {effective_params:<10} {sparsity:<10.3f} {reduction:<10.1f}%")
    
    # =============================================================================
    # EFFICIENCY METRICS
    # =============================================================================
    
    print(f"\n=== Efficiency Metrics ===")
    n_electrons = mol.nelectron
    
    for name, combined_params, instances in models:
        analysis = analyze_full_ansatz_parameters(combined_params, instances)
        summary = analysis['summary']
        
        params_per_electron = summary['total_effective'] / n_electrons
        print(f"{name}: {params_per_electron:.1f} effective params/electron")
    
    print(f"\n=== Key Benefits of New System ===")
    print(f"✅ Single function call for complete analysis")
    print(f"✅ Automatic mask detection and handling")
    print(f"✅ Consistent counting across all functions")
    print(f"✅ Detailed component breakdown")
    print(f"✅ No more manual parameter extraction")
    print(f"✅ No more separate instance creation")
    print(f"✅ Clean, modular architecture")

if __name__ == "__main__":
    example_new_parameter_counting()
