# Standard imports
import sys
import os
import time
import numpy as np
import jax
import jax.numpy as jnp
from jax import random
import matplotlib.pyplot as plt
import seaborn as sns
from tqdm import tqdm

# Enable float64 precision for quantum chemistry calculations
jax.config.update("jax_enable_x64", True)

# Add PyTC to path
pytc_path = os.path.abspath('../../repos/pytc')
if pytc_path not in sys.path:
    sys.path.insert(0, pytc_path)

# Import our utility functions
from model_analysis import (
    count_parameters, setup_molecule, calculate_exact_ground_state,
    create_full_ansatz, analyze_parameter_counts,
    extract_neural_weights, extract_polynomial_weights,
    analyze_weight_statistics, compare_weight_magnitudes,
    save_trained_model, load_trained_model, save_weight_analysis,
    save_training_checkpoint, get_latest_checkpoint, create_model_summary,
    train_and_compare, count_effective_parameters,  # Import the main training function
    # Import visualization functions
    plot_training_comparison, plot_error_convergence, plot_parameter_efficiency,
    plot_training_summary_table, create_all_plots, quick_comparison_plot
)

# PyTC imports
from pytc.autodiff.mcmc import sample
from pytc.autodiff.mcmc_utils import analyze_energies

# Plotting setup
plt.style.use('default')
plt.rcParams['figure.dpi'] = 100
plt.rcParams['savefig.dpi'] = 150

print("Libraries imported successfully!")
print(f"JAX version: {jax.__version__}")
print(f"JAX devices: {jax.devices()}")

# =============================================================================
# CONFIGURATION PARAMETERS
# =============================================================================

# Polynomial Jastrow Parameters
POLY_DEGREE = 4      # Polynomial degree for CP decomposition
POLY_RANK = 8        # Rank for CP decomposition

# Neural Network Architecture
NEURAL_LAYERS = [32, 32]  # Hidden layer widths

# Training/Optimization Parameters
N_OPT_STEPS = 200       # Number of optimization steps
LEARNING_RATE = 0.0005  # Conservative learning rate
OPT_N_WALKERS = 500     # Number of walkers for optimization
OPT_N_STEPS = 1000      # Steps per optimization iteration
BURN_IN_STEPS = 400     # Burn-in steps
CHECKPOINT_INTERVAL = 50  # Save checkpoint every N steps
OPTIMIZER_TYPE = "adam"  # Default optimizer

# VMC Evaluation Parameters
EVAL_N_WALKERS = 500    # Walkers for final evaluation
EVAL_N_STEPS = 200      # Steps for final evaluation
STEP_SIZE = 0.02        # MCMC step size
THINNING = 2            # Thinning factor

# File naming
MOLECULE_NAME = "H2"
MOLECULE_NAME = "HeH+"
BASIS_SET = "cc-pvtz"
RESULTS_DIR = "training_results"

# Create results directory
os.makedirs(RESULTS_DIR, exist_ok=True)

print(f"Configuration set:")
print(f"  Polynomial: degree={POLY_DEGREE}, rank={POLY_RANK}")
print(f"  Neural: layers={NEURAL_LAYERS}")
print(f"  Training: {N_OPT_STEPS} steps, lr={LEARNING_RATE}")
print(f"  Checkpoints every {CHECKPOINT_INTERVAL} steps")
print(f"  Results saved to: {RESULTS_DIR}/")

# Setup molecular system
mol, mf = setup_molecule(MOLECULE_NAME, basis=BASIS_SET)
fci_energy = calculate_exact_ground_state(mol, mf)

print(f"\nReference energies:")
print(f"  HF energy:  {mf.e_tot:.6f} Hartree")
print(f"  FCI energy: {fci_energy:.6f} Hartree")
print(f"  Correlation energy: {fci_energy - mf.e_tot:.6f} Hartree")

# =============================================================================
# PARAMETER COUNT COMPARISON: NN vs CP vs CP_sparse_LU
# =============================================================================

print("=== Parameter Count Comparison ===")

# Import the new parameter counting functions
from model_analysis import (
    create_full_ansatz, analyze_full_ansatz_parameters
)

# Create ansätze for comparison
print("Creating ansätze...")

# 1. Neural Network ansatz
neural_ansatz, neural_params, _, neural_instances = create_full_ansatz(
    mol, mf, "neural", 
    layer_widths=NEURAL_LAYERS
)

# 2. Multi-layer dense CP ansatz
cp_ansatz, cp_params, _, cp_instances = create_full_ansatz(
    mol, mf, "polynomial", 
    hidden_dims=[16, 8],
    degree=[4, 3, 2], 
    rank=[12, 8, 4], 
    cp_variant='CP',
    use_activation_between_layers=False
)

# 3. Multi-layer sparse CP ansatz with different sparsity per layer and activations
cp_sparse_ansatz, cp_sparse_params, _, sparse_instances = create_full_ansatz(
    mol, mf, "polynomial", 
    hidden_dims=[16, 8],
    degree=[4, 3, 2], 
    rank=[12, 8, 4], 
    cp_variant=['CP_sparse_degree_sawtooth', 'CP_sparse_LU', 'CP'],
    use_activation_between_layers=True
)

# Comprehensive analysis using new system
models = [
    ("Neural Network", neural_params, neural_instances),
    ("Polynomial CP", cp_params, cp_instances),
    ("CP sparse_LU", cp_sparse_params, sparse_instances)
]

print(f"\n=== Non-Zero Parameter Comparison ===")
print(f"{'Model':<15} {'Total':<8} {'Non-Zero':<10} {'Effective':<10} {'Sparsity':<10}")
print(f"{'-'*15} {'-'*8} {'-'*10} {'-'*10} {'-'*10}")

for name, combined_params, instances in models:
    # Complete analysis in one function call
    analysis = analyze_full_ansatz_parameters(combined_params, instances)
    summary = analysis['summary']
    
    total_params = summary['total_parameters']
    effective_params = summary['total_effective']
    sparsity = summary['overall_sparsity']
    
    # Calculate total non-zero (sum across all components)
    total_nonzero = sum(comp.get('nonzero', comp['total']) for comp in analysis['components'].values())
    
    print(f"{name:<15} {total_params:<8} {total_nonzero:<10} {effective_params:<10} {sparsity:<10.3f}")

# Show sparsity benefits
neural_analysis = analyze_full_ansatz_parameters(neural_params, neural_instances)
cp_analysis = analyze_full_ansatz_parameters(cp_params, cp_instances)
sparse_analysis = analyze_full_ansatz_parameters(cp_sparse_params, sparse_instances)

neural_effective = neural_analysis['summary']['total_effective']
cp_effective = cp_analysis['summary']['total_effective']
sparse_effective = sparse_analysis['summary']['total_effective']

print(f"\n=== Efficiency Gains ===")
print(f"CP sparse vs Neural:  {sparse_effective/neural_effective:.2f}x ({(sparse_effective/neural_effective-1)*100:+.0f}%)")
print(f"CP sparse vs CP:      {sparse_effective/cp_effective:.2f}x ({(sparse_effective/cp_effective-1)*100:+.0f}%)")
print(f"Sparsity reduction:   {(1-sparse_analysis['summary']['overall_sparsity'])*100:.1f}% computational savings")

# =============================================================================
# TRAINING USING IMPORTED FUNCTION
# =============================================================================

# Define training configurations
training_configs = [
    ("Neural Network", "neural", {
        "layer_widths": NEURAL_LAYERS
    }),
    #("Polynomial CP sparse", "polynomial", {
        #"degree": 4,
        #"rank": 8,
        #"cp_variant": 'CP_sparse_degree_LU',
        #"activation_scale": 0.1,
        #"use_final_activation": True,
        #"hidden_dims": [4],
        #"use_activation_between_layers": False
    #}),
    ("Polynomial CP ", "polynomial", {
        "degree": [2, 2],
        "rank": [2, 2],
        "cp_variant": ['CP', 'CP'],
        "activation_scale": 0.1,
        "use_final_activation": True,
        "hidden_dims": [2],
        "activation_between_layers": "relu",
        "use_activation_between_layers": True
    }),
    ("Polynomial Individual Configs", "polynomial", {
        "en_config": {
            "degree": 3, "rank": 2, "cp_variant": "CP_sparse_degree_LU",
             "final_activation": "tanh", "use_activation_between_layers": False
        },
        "ee_config": {
            "degree": 3, "rank": 2, "cp_variant": "CP_sparse_degree_LU",
             "final_activation": "tanh", "use_activation_between_layers": False
        },
        "een_config": {
            "degree": [3,3], "rank": [1,1], "cp_variant": "CP", "hidden_dims": [2],
             "final_activation": "tanh", "use_activation_between_layers": False,
             "activation_between_layers": "relu"
        },
        "activation_scale": 0.1,
        "use_final_activation": True
    })
]

# Run training and comparison
print("=== TRAINING AND COMPARISON ===")
print(f"Training configurations: {len(training_configs)}")
print(f"Optimization steps: {N_OPT_STEPS}")
print(f"Learning rate: {LEARNING_RATE}")

# Train all models using the imported function
training_results = train_and_compare(
    mol=mol,
    mf=mf,
    fci_energy=fci_energy,
    molecule_name=MOLECULE_NAME,
    configs=training_configs,
    n_opt_steps=N_OPT_STEPS,
    learning_rate=LEARNING_RATE,
    optimizer_type=OPTIMIZER_TYPE,
    opt_n_walkers=OPT_N_WALKERS,
    opt_n_steps=OPT_N_STEPS,
    burn_in_steps=BURN_IN_STEPS,
    eval_n_walkers=EVAL_N_WALKERS,
    eval_n_steps=EVAL_N_STEPS,
    step_size=STEP_SIZE,
    thinning=THINNING
)

# Display results summary
print("\n" + "="*60)
print("TRAINING RESULTS SUMMARY")
print("="*60)

for name, result in training_results.items():
    if result is not None:
        print(f"\n{name}:")
        print(f"  Parameters: {result['param_count']}")
        print(f"  Final energy: {result['final_energy']:.6f} ± {result['final_error']:.6f} Ha")
        print(f"  HF improvement: {result['hf_improvement']:.6f} Ha")
        if result['fci_diff'] is not None:
            print(f"  FCI difference: {result['fci_diff']:.6f} Ha")
        print(f"  Efficiency: {result['efficiency']:.3f} mHa/param")
        print(f"  Optimization: {'✅' if result['optimized'] else '❌'}")
    else:
        print(f"\n{name}: ❌ Training failed")

training_results['Neural Network']['optimised_params'][0][1]

# After training is complete, add these cells:

# Main comparison plot
plot_training_comparison(training_results, mf, fci_energy, MOLECULE_NAME)

# Error convergence (if FCI available)
if fci_energy:
    plot_error_convergence(training_results, fci_energy)

# Parameter efficiency
plot_parameter_efficiency(training_results)

# Summary table
plot_training_summary_table(training_results, mf, fci_energy)

