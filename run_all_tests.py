#!/usr/bin/env python3
"""
Test runner for all polynomial Jastrow tests.
"""

import sys
import os
import subprocess

def run_test(test_file):
    """Run a single test file."""
    print(f"\n{'='*60}")
    print(f"Running {test_file}")
    print(f"{'='*60}")
    
    try:
        # Change to the correct directory and run the test
        result = subprocess.run([sys.executable, test_file], 
                              cwd='/Users/<USER>/Projects/Quantum/qpolynets/tests',
                              capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print(f"✅ {test_file} PASSED")
            print(result.stdout)
        else:
            print(f"❌ {test_file} FAILED")
            print("STDOUT:", result.stdout)
            print("STDERR:", result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print(f"⏰ {test_file} TIMED OUT")
        return False
    except Exception as e:
        print(f"💥 {test_file} ERROR: {e}")
        return False
    
    return True

def main():
    """Run all tests."""
    print("🧪 Running All Polynomial Jastrow Tests")
    print("=" * 60)
    
    # List of test files to run
    test_files = [
        'test_multi_layer_polynomial.py',
        'test_degree_masks.py', 
        'test_sawtooth_masks.py',
        'test_combined_masks.py',
        'example_all_cp_variants.py'
    ]
    
    passed = 0
    failed = 0
    
    for test_file in test_files:
        if run_test(test_file):
            passed += 1
        else:
            failed += 1
    
    print(f"\n{'='*60}")
    print(f"📊 TEST SUMMARY")
    print(f"{'='*60}")
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failed}")
    print(f"📈 Total:  {passed + failed}")
    
    if failed == 0:
        print("\n🎉 ALL TESTS PASSED! 🎉")
        print("\nMulti-layer polynomial Jastrow implementation is complete and working!")
        return 0
    else:
        print(f"\n💔 {failed} test(s) failed")
        return 1

if __name__ == "__main__":
    exit(main())
