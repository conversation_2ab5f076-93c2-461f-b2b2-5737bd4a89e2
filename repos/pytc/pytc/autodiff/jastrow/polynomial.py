"""
Unified polynomial Jastrow factors using CP decomposition.

This module implements polynomial Jastrow factors that use Canonical Polyadic (CP)
decomposition to represent electron correlations through polynomial recursion
without intermediate activations.
"""
from typing import Dict, Any, Optional, List, Tuple, Union, Sequence
import jax
import jax.numpy as jnp
from functools import partial
import flax.linen as nn  # For nn.softplus

from .jastrow import Jastrow
from jax.random import PRNG<PERSON>ey

# Try to import kfac_jax, but make it optional
try:
    import kfac_jax
    KFAC_AVAILABLE = True
    print("kfac_jax available for polynomial Jastrow. KFAC optimization can be enabled.")
except ImportError:
    KFAC_AVAILABLE = False
    kfac_jax = None
    print("Warning: kfac_jax not available for polynomial Jastrow. KFAC optimization disabled.")


class PolynomialJastrow(Jastrow):
    """Base class for CP-based Jastrow factors with configurable polynomial variants.
    
    Supports multiple CP variants:
    - 'CP': Standard CP decomposition
    - 'CP_sparse_LU': Alternating upper/lower triangular sparsity
    - 'CP_sparse_degree': Degree-dependent sparsity patterns
    """
    
    def __init__(self, mol,
                 degree: Union[int, Sequence[int]] = 3,
                 rank: Union[int, Sequence[int]] = 8,
                 hidden_dims: Optional[Sequence[int]] = None,
                 cp_variant: Union[str, Sequence[str]] = 'CP',
                 use_final_activation: bool = True,
                 activation_scale: float = 0.1,
                 use_kfac: bool = False,
                 use_activation_between_layers: bool = True,
                 activation_between_layers = jnp.tanh,
                 final_activation = jnp.tanh,
                 final_activation_scale: Optional[float] = None,
                 sawtooth_l_offset: int = 0,
                 sawtooth_u_offset: int = 0,
                 name: Optional[str] = None):
        super().__init__(name=name)
        self.mol = mol

        # Infer multi-layer from hidden_dims presence
        self.is_multi_layer = hidden_dims is not None

        if self.is_multi_layer:
            # Multi-layer setup
            self.hidden_dims = list(hidden_dims)
            self.degrees = list(degree) if isinstance(degree, (list, tuple)) else [degree] * (len(hidden_dims) + 1)
            self.ranks = list(rank) if isinstance(rank, (list, tuple)) else [rank] * (len(hidden_dims) + 1)
            self.cp_variants = list(cp_variant) if isinstance(cp_variant, (list, tuple)) else [cp_variant] * (len(hidden_dims) + 1)

            # Validate lengths
            num_layers = len(hidden_dims) + 1
            if len(self.degrees) != num_layers:
                raise ValueError(f"degrees length {len(self.degrees)} must match number of layers {num_layers}")
            if len(self.ranks) != num_layers:
                raise ValueError(f"ranks length {len(self.ranks)} must match number of layers {num_layers}")
            if len(self.cp_variants) != num_layers:
                raise ValueError(f"cp_variants length {len(self.cp_variants)} must match number of layers {num_layers}")

            self.num_layers = num_layers
            self.use_activation_between_layers = use_activation_between_layers
            self.activation_between_layers = activation_between_layers
        else:
            # Single layer setup (backward compatibility)
            self.degree = degree if isinstance(degree, int) else degree[0]
            self.rank = rank if isinstance(rank, int) else rank[0]
            self.cp_variant = cp_variant if isinstance(cp_variant, str) else cp_variant[0]
            self.num_layers = 1

        self.use_final_activation = use_final_activation
        self.activation_scale = activation_scale
        self.final_activation = final_activation
        self.final_activation_scale = final_activation_scale if final_activation_scale is not None else activation_scale
        self.use_kfac = use_kfac
        self.sawtooth_l_offset = sawtooth_l_offset
        self.sawtooth_u_offset = sawtooth_u_offset
        self.input_dim = None  # To be set by subclasses
        
    def _safe_norm(self, x):
        """Compute norm with epsilon to prevent division by zero."""
        return jnp.sqrt(jnp.sum(x * x, axis=-1) + 1e-12)
    
    def _cp_forward(self, x: jnp.ndarray, params: Dict[str, Any]) -> jnp.ndarray:
        """Route to appropriate forward pass based on layer configuration."""
        if self.is_multi_layer:
            return self._multi_layer_forward(x, params)
        else:
            return self._single_layer_forward(x, params)

    def _single_layer_forward(self, x: jnp.ndarray, params: Dict[str, Any]) -> jnp.ndarray:
        """Core CP recursion logic for single layer with optional KFAC registration.

        Implements: out = U1*x; for k=2..D: out = (Uk*x) * out + out
        """
        U = params['U']
        C = params['C']
        bias = params.get('bias', jnp.array(0.0))

        # Apply sparsity masks if needed - use self.masks instead of params['masks']
        if hasattr(self, 'masks') and self.masks is not None:
            # For single layer, use the masks directly
            if not self.is_multi_layer:
                cp_variant = self.cp_variant
            else:
                cp_variant = 'CP'  # Default for multi-layer individual layers
            if cp_variant != 'CP':
                U = self._apply_sparsity_masks(U, self.masks)

        # First linear transformation with optional KFAC registration
        out = x @ U[0]  # (rank,)
        if self.use_kfac and KFAC_AVAILABLE and kfac_jax is not None:
            kfac_jax.register_dense(out, x, U[0], None)

        # Subsequent transformations with optional KFAC registration
        for k in range(1, len(U)):
            ukx = x @ U[k]
            if self.use_kfac and KFAC_AVAILABLE and kfac_jax is not None:
                kfac_jax.register_dense(ukx, x, U[k], None)
            out = ukx * out + out

        # Final linear layer with optional KFAC registration
        raw = jnp.dot(out, C) + bias
        if self.use_kfac and KFAC_AVAILABLE and kfac_jax is not None:
            # Reshape for KFAC registration (expects 2D tensors)
            out_2d = out.reshape(1, -1)
            raw_2d = raw.reshape(1, -1)
            # C is now always 2D, so no need for conditional reshaping
            C_2d = C
            # Bias is now always 1D, so reshape to 2D for KFAC
            bias_2d = bias.reshape(1, -1)
            kfac_jax.register_dense(raw_2d, out_2d, C_2d, bias_2d)

        # Optional final activation for stability (only for final layer)
        if self.use_final_activation and not self.is_multi_layer:
            result = self.activation_scale * jnp.tanh(raw)
        else:
            result = raw

        # Squeeze to scalar if output dimension is 1 (for single-layer networks)
        if not self.is_multi_layer and result.shape[-1] == 1:
            result = result.squeeze(-1)

        return result

    def _multi_layer_forward(self, x: jnp.ndarray, params: Dict[str, Any]) -> jnp.ndarray:
        """Multi-layer polynomial forward pass."""
        current_x = x

        for i, layer_params in enumerate(params['layers']):
            # Apply polynomial layer
            current_x = self._single_layer_forward(current_x, layer_params)

            # Apply activation between layers (except last)
            if (i < len(params['layers']) - 1 and
                self.use_activation_between_layers):
                current_x = self.activation_scale * self.activation_between_layers(current_x)

        # Apply final activation if requested
        if self.use_final_activation:
            current_x = self.final_activation_scale * self.final_activation(current_x)

        # Squeeze to scalar if final output dimension is 1 (for multi-layer networks)
        if current_x.shape[-1] == 1:
            current_x = current_x.squeeze(-1)

        return current_x
        
    def _apply_sparsity_masks(self, U: List[jnp.ndarray], 
                             masks: List[jnp.ndarray]) -> List[jnp.ndarray]:
        """Apply sparsity patterns for sparse CP variants."""
        return [U[i] * masks[i] for i in range(len(U))]
    
    def _generate_lu_masks(self) -> List[jnp.ndarray]:
        """Generate alternating upper/lower triangular masks for CP_sparse_LU."""
        mask1 = jnp.triu(jnp.ones((self.rank, self.input_dim)))
        mask2 = jnp.tril(jnp.ones((self.rank, self.input_dim)))
        masks = []
        for i in range(self.degree):
            if i % 2 == 0:
                masks.append(mask1.T)
            else:
                masks.append(mask2.T)
        return masks
    
    def _generate_degree_masks(self, key: jax.Array) -> List[jnp.ndarray]:
        """Generate degree-dependent sparsity masks.

        This implements the same logic as polyformer's generate_masks function,
        creating structured sparsity patterns based on degree and rank.
        """
        if self.rank < self.degree:
            # Case 1: rank < degree
            # Start with (degree - rank + 1) full masks
            masks = [jnp.ones((self.rank, self.input_dim), dtype=jnp.float32)] * (self.degree - self.rank + 1)

            # Add progressively sparser masks
            for i in range(1, self.rank):
                M = jnp.ones((self.rank, self.input_dim), dtype=jnp.float32)
                r = jnp.arange(self.rank - i, self.rank)
                M = M.at[r].set(0)
                masks = masks + [M]
        else:
            # Case 2: rank >= degree
            # Start with one full mask
            masks = [jnp.ones((self.rank, self.input_dim), dtype=jnp.float32)]
            steps = []

            # Create structured sparsity pattern
            for i in range(0, self.degree - 1):
                M = jnp.ones((self.rank, self.input_dim), dtype=jnp.float32)
                r = jnp.arange(i, self.rank, self.degree)
                steps = steps + [r]
                # Zero out accumulated indices
                all_indices = jnp.concatenate(steps, axis=0)
                M = M.at[all_indices].set(0)
                masks = masks + [M]

        # Transpose to match expected (input_dim, rank) shape
        return [mask.T for mask in masks]

    def _generate_sawtooth_masks(self, l_offset: int = 0, u_offset: int = 0) -> Tuple[jnp.ndarray, jnp.ndarray]:
        """Generate sawtooth L/U pattern masks using polyformer logic.

        Efficiently creates repeating L/U pattern masks along the elongated dimension.
        Converted from polyformer's _create_sawtooth_masks_efficient function.

        Args:
            l_offset: Diagonal offset for lower triangular pattern
            u_offset: Diagonal offset for upper triangular pattern

        Returns:
            Tuple of (mask1, mask2) with alternating U/L patterns
        """
        d = self.input_dim
        k = self.rank

        # Determine which dimension is smaller
        min_dim = min(d, k)

        # Create base L and U patterns for the square part
        L_base = jnp.tril(jnp.ones((min_dim, min_dim), dtype=jnp.float32), k=l_offset)
        U_base = jnp.triu(jnp.ones((min_dim, min_dim), dtype=jnp.float32), k=u_offset)

        if d >= k:  # Input dimension is longer - repeat patterns horizontally
            # Create masks of the right shape
            mask1 = jnp.zeros((k, d), dtype=jnp.float32)
            mask2 = jnp.zeros((k, d), dtype=jnp.float32)

            # Calculate how many complete blocks we need
            num_complete_blocks = d // k

            # Handle complete blocks with efficient tensor operations
            if num_complete_blocks > 0:
                # Create repeated U patterns for mask1
                mask1_blocks = jnp.tile(U_base, (1, num_complete_blocks))
                mask1 = mask1.at[:, :k*num_complete_blocks].set(mask1_blocks)

                # Create repeated L patterns for mask2
                mask2_blocks = jnp.tile(L_base, (1, num_complete_blocks))
                mask2 = mask2.at[:, :k*num_complete_blocks].set(mask2_blocks)

            # Handle remainder efficiently
            remainder = d % k
            if remainder > 0:
                mask1 = mask1.at[:, k*num_complete_blocks:].set(U_base[:, :remainder])
                mask2 = mask2.at[:, k*num_complete_blocks:].set(L_base[:, :remainder])

        else:  # Rank dimension is longer - repeat patterns vertically
            # Create masks of the right shape
            mask1 = jnp.zeros((k, d), dtype=jnp.float32)
            mask2 = jnp.zeros((k, d), dtype=jnp.float32)

            # Calculate how many complete blocks we need
            num_complete_blocks = k // d

            # Handle complete blocks with efficient tensor operations
            if num_complete_blocks > 0:
                # Create repeated U patterns for mask1
                mask1_blocks = jnp.tile(U_base, (num_complete_blocks, 1))
                mask1 = mask1.at[:d*num_complete_blocks, :].set(mask1_blocks)

                # Create repeated L patterns for mask2
                mask2_blocks = jnp.tile(L_base, (num_complete_blocks, 1))
                mask2 = mask2.at[:d*num_complete_blocks, :].set(mask2_blocks)

            # Handle remainder efficiently
            remainder = k % d
            if remainder > 0:
                mask1 = mask1.at[d*num_complete_blocks:, :].set(U_base[:remainder, :])
                mask2 = mask2.at[d*num_complete_blocks:, :].set(L_base[:remainder, :])

        # Transpose to match expected (input_dim, rank) shape
        return mask1.T, mask2.T

    def _generate_sawtooth_variant_masks(self, l_offset: int = 0, u_offset: int = 0) -> List[jnp.ndarray]:
        """Generate alternating sawtooth masks for CP_sparse_sawtooth variant."""
        mask1, mask2 = self._generate_sawtooth_masks(l_offset=l_offset, u_offset=u_offset)
        # Alternate between the two sawtooth patterns
        return [mask1 if i % 2 == 0 else mask2 for i in range(self.degree)]

    def _generate_combined_degree_lu_masks(self) -> List[jnp.ndarray]:
        """Generate combined degree + LU masks for CP_sparse_degree_LU variant."""
        degree_masks = self._generate_degree_masks(jax.random.PRNGKey(0))  # Deterministic
        lu_masks = self._generate_lu_masks()

        # Combine by element-wise multiplication
        combined_masks = []
        for i in range(self.degree):
            degree_mask = degree_masks[i]
            lu_mask = lu_masks[i % len(lu_masks)]  # Cycle through LU masks if needed
            combined_mask = degree_mask * lu_mask
            combined_masks.append(combined_mask)

        return combined_masks

    def _generate_combined_degree_sawtooth_masks(self, l_offset: int = 0, u_offset: int = 0) -> List[jnp.ndarray]:
        """Generate combined degree + sawtooth masks for CP_sparse_degree_sawtooth variant."""
        degree_masks = self._generate_degree_masks(jax.random.PRNGKey(0))  # Deterministic
        sawtooth_masks = self._generate_sawtooth_variant_masks(l_offset=l_offset, u_offset=u_offset)

        # Combine by element-wise multiplication
        combined_masks = []
        for i in range(self.degree):
            degree_mask = degree_masks[i]
            sawtooth_mask = sawtooth_masks[i]
            combined_mask = degree_mask * sawtooth_mask
            combined_masks.append(combined_mask)

        return combined_masks

    # Helper methods for multi-layer mask generation
    def _generate_lu_masks_for_layer(self, input_dim: int, rank: int, degree: int) -> List[jnp.ndarray]:
        """Generate LU masks for a specific layer."""
        mask1 = jnp.triu(jnp.ones((rank, input_dim), dtype=jnp.float32))
        mask2 = jnp.tril(jnp.ones((rank, input_dim), dtype=jnp.float32))

        masks = []
        for i in range(degree):
            if i % 2 == 0:
                masks.append(mask1.T)
            else:
                masks.append(mask2.T)
        return masks

    def _generate_degree_masks_for_layer(self, input_dim: int, rank: int, degree: int, key: jax.Array) -> List[jnp.ndarray]:
        """Generate degree masks for a specific layer."""
        # Use the same logic as _generate_degree_masks but for specific dimensions
        if rank < degree:
            masks = [jnp.ones((rank, input_dim), dtype=jnp.float32)] * (degree - rank + 1)
            for i in range(1, rank):
                M = jnp.ones((rank, input_dim), dtype=jnp.float32)
                r = jnp.arange(rank - i, rank)
                M = M.at[r].set(0)
                masks = masks + [M]
        else:
            masks = [jnp.ones((rank, input_dim), dtype=jnp.float32)]
            steps = []
            for i in range(0, degree - 1):
                M = jnp.ones((rank, input_dim), dtype=jnp.float32)
                r = jnp.arange(i, rank, degree)
                steps = steps + [r]
                all_indices = jnp.concatenate(steps, axis=0)
                M = M.at[all_indices].set(0)
                masks = masks + [M]
        return [mask.T for mask in masks]

    def _generate_sawtooth_variant_masks_for_layer(self, input_dim: int, rank: int, degree: int,
                                                  l_offset: int = 0, u_offset: int = 0) -> List[jnp.ndarray]:
        """Generate sawtooth masks for a specific layer."""
        mask1, mask2 = self._generate_sawtooth_masks_for_layer(input_dim, rank, l_offset, u_offset)
        return [mask1 if i % 2 == 0 else mask2 for i in range(degree)]

    def _generate_sawtooth_masks_for_layer(self, input_dim: int, rank: int,
                                          l_offset: int = 0, u_offset: int = 0) -> Tuple[jnp.ndarray, jnp.ndarray]:
        """Generate sawtooth L/U pattern masks for a specific layer."""
        d = input_dim
        k = rank
        min_dim = min(d, k)

        L_base = jnp.tril(jnp.ones((min_dim, min_dim), dtype=jnp.float32), k=l_offset)
        U_base = jnp.triu(jnp.ones((min_dim, min_dim), dtype=jnp.float32), k=u_offset)

        if d >= k:
            mask1 = jnp.zeros((k, d), dtype=jnp.float32)
            mask2 = jnp.zeros((k, d), dtype=jnp.float32)

            num_complete_blocks = d // k
            if num_complete_blocks > 0:
                mask1_blocks = jnp.tile(U_base, (1, num_complete_blocks))
                mask1 = mask1.at[:, :k*num_complete_blocks].set(mask1_blocks)
                mask2_blocks = jnp.tile(L_base, (1, num_complete_blocks))
                mask2 = mask2.at[:, :k*num_complete_blocks].set(mask2_blocks)

            remainder = d % k
            if remainder > 0:
                mask1 = mask1.at[:, k*num_complete_blocks:].set(U_base[:, :remainder])
                mask2 = mask2.at[:, k*num_complete_blocks:].set(L_base[:, :remainder])
        else:
            mask1 = jnp.zeros((k, d), dtype=jnp.float32)
            mask2 = jnp.zeros((k, d), dtype=jnp.float32)

            num_complete_blocks = k // d
            if num_complete_blocks > 0:
                mask1_blocks = jnp.tile(U_base, (num_complete_blocks, 1))
                mask1 = mask1.at[:d*num_complete_blocks, :].set(mask1_blocks)
                mask2_blocks = jnp.tile(L_base, (num_complete_blocks, 1))
                mask2 = mask2.at[:d*num_complete_blocks, :].set(mask2_blocks)

            remainder = k % d
            if remainder > 0:
                mask1 = mask1.at[d*num_complete_blocks:, :].set(U_base[:remainder, :])
                mask2 = mask2.at[d*num_complete_blocks:, :].set(L_base[:remainder, :])

        return mask1.T, mask2.T

    def _generate_combined_degree_lu_masks_for_layer(self, input_dim: int, rank: int, degree: int, key: jax.Array) -> List[jnp.ndarray]:
        """Generate combined degree + LU masks for a specific layer."""
        degree_masks = self._generate_degree_masks_for_layer(input_dim, rank, degree, key)
        lu_masks = self._generate_lu_masks_for_layer(input_dim, rank, degree)

        combined_masks = []
        for i in range(degree):
            degree_mask = degree_masks[i]
            lu_mask = lu_masks[i % len(lu_masks)]
            combined_mask = degree_mask * lu_mask
            combined_masks.append(combined_mask)
        return combined_masks

    def _generate_combined_degree_sawtooth_masks_for_layer(self, input_dim: int, rank: int, degree: int, key: jax.Array,
                                                          l_offset: int = 0, u_offset: int = 0) -> List[jnp.ndarray]:
        """Generate combined degree + sawtooth masks for a specific layer."""
        degree_masks = self._generate_degree_masks_for_layer(input_dim, rank, degree, key)
        sawtooth_masks = self._generate_sawtooth_variant_masks_for_layer(input_dim, rank, degree, l_offset, u_offset)

        combined_masks = []
        for i in range(degree):
            degree_mask = degree_masks[i]
            sawtooth_mask = sawtooth_masks[i]
            combined_mask = degree_mask * sawtooth_mask
            combined_masks.append(combined_mask)
        return combined_masks

    def init_params(self, key: Optional[jax.Array] = None, **kwargs) -> Dict[str, Any]:
        """Initialize parameters compatible with PyTC's functional approach."""
        if key is None:
            key = jax.random.PRNGKey(0)

        if self.input_dim is None:
            raise ValueError("input_dim must be set by subclass before calling init_params")

        if self.is_multi_layer:
            return self._init_multi_layer_params(key)
        else:
            return self._init_single_layer_params(key)

    def _init_single_layer_params(self, key: jax.Array) -> Dict[str, Any]:
        """Initialize parameters for single layer (current behavior)."""
        keys = jax.random.split(key, self.degree + 5)  # +2 more keys for decay params

        # Better scaling based on input dimensions (like LeCun normal)
        scale = 1.0 / jnp.sqrt(self.input_dim)

        # Generate sparsity masks FIRST (before parameter initialization)
        if self.cp_variant == 'CP_sparse_LU':
            self.masks = self._generate_lu_masks()
        elif self.cp_variant == 'CP_sparse_degree':
            self.masks = self._generate_degree_masks(keys[-1])  # Keep key for consistency
        elif self.cp_variant == 'CP_sparse_sawtooth':
            self.masks = self._generate_sawtooth_variant_masks(
                l_offset=self.sawtooth_l_offset, u_offset=self.sawtooth_u_offset)
        elif self.cp_variant == 'CP_sparse_degree_LU':
            self.masks = self._generate_combined_degree_lu_masks()
        elif self.cp_variant == 'CP_sparse_degree_sawtooth':
            self.masks = self._generate_combined_degree_sawtooth_masks(
                l_offset=self.sawtooth_l_offset, u_offset=self.sawtooth_u_offset)
        else:
            self.masks = None

        # Core CP parameters with improved initialization
        # Apply sparsity during initialization for sparse variants
        if self.masks is not None:
            # Sparse initialization: apply masks during initialization
            U = []
            for i in range(self.degree):
                u_full = jax.random.normal(keys[i], (self.input_dim, self.rank)) * scale
                u_sparse = u_full * self.masks[i]  # Apply sparsity immediately
                U.append(u_sparse)
        else:
            # Standard dense initialization for non-sparse variants
            U = [jax.random.normal(keys[i], (self.input_dim, self.rank)) * scale
                 for i in range(self.degree)]

        # Scale C based on rank (always 2D for consistent matrix operations)
        c_scale = 1.0 / jnp.sqrt(self.rank)
        C = jax.random.normal(keys[-4], (self.rank, 1)) * c_scale
        bias = jax.random.normal(keys[-3], (1,)) * 0.1

        # Add decay parameters (like neural implementation)
        initial_rc_en_raw = 0.5  # softplus(0.5) ≈ 0.97
        initial_rc_ee_raw = 0.5

        params = {
            'degree': self.degree,
            'U': U,
            'C': C,
            'bias': bias,
            # NEW: Add decay parameters
            'rc_en_raw': initial_rc_en_raw,
            'rc_ee_raw': initial_rc_ee_raw
        }

        # Note: masks are now stored in self.masks (set above), not in params
        return params

    def _init_multi_layer_params(self, key: jax.Array) -> Dict[str, Any]:
        """Initialize parameters for multi-layer configuration."""
        # Set up layer dimensions: input_dim -> hidden_dims -> 1
        layer_dims = [self.input_dim] + self.hidden_dims + [1]

        # Split keys for all layers and extra parameters
        total_keys_needed = sum(self.degrees) + self.num_layers * 2 + 5  # U matrices + C/bias per layer + extras
        keys = jax.random.split(key, total_keys_needed)
        key_idx = 0

        layers = []

        for layer_idx in range(self.num_layers):
            input_dim = layer_dims[layer_idx]
            output_dim = layer_dims[layer_idx + 1]
            degree = self.degrees[layer_idx]
            rank = self.ranks[layer_idx]
            cp_variant = self.cp_variants[layer_idx]

            # Better scaling based on input dimensions
            scale = 1.0 / jnp.sqrt(input_dim)

            # Generate sparsity masks for this layer
            layer_masks = None
            if cp_variant == 'CP_sparse_LU':
                layer_masks = self._generate_lu_masks_for_layer(input_dim, rank, degree)
            elif cp_variant == 'CP_sparse_degree':
                layer_masks = self._generate_degree_masks_for_layer(input_dim, rank, degree, keys[key_idx])
                key_idx += 1
            elif cp_variant == 'CP_sparse_sawtooth':
                layer_masks = self._generate_sawtooth_variant_masks_for_layer(
                    input_dim, rank, degree, self.sawtooth_l_offset, self.sawtooth_u_offset)
            elif cp_variant == 'CP_sparse_degree_LU':
                layer_masks = self._generate_combined_degree_lu_masks_for_layer(input_dim, rank, degree, keys[key_idx])
                key_idx += 1
            elif cp_variant == 'CP_sparse_degree_sawtooth':
                layer_masks = self._generate_combined_degree_sawtooth_masks_for_layer(
                    input_dim, rank, degree, keys[key_idx], self.sawtooth_l_offset, self.sawtooth_u_offset)
                key_idx += 1

            # Initialize U matrices for this layer
            U = []
            for i in range(degree):
                u_full = jax.random.normal(keys[key_idx], (input_dim, rank)) * scale
                key_idx += 1

                if layer_masks is not None:
                    u_sparse = u_full * layer_masks[i]
                    U.append(u_sparse)
                else:
                    U.append(u_full)

            # Initialize C matrix for this layer (always 2D for consistent matrix operations)
            c_scale = 1.0 / jnp.sqrt(rank)
            # Always use 2D matrix format to avoid scalar/dimension issues
            C = jax.random.normal(keys[key_idx], (rank, output_dim)) * c_scale
            bias = jax.random.normal(keys[key_idx + 1], (output_dim,)) * 0.1
            key_idx += 2

            layer_params = {
                'degree': degree,
                'U': U,
                'C': C,
                'bias': bias
            }
            layers.append(layer_params)

        # Add decay parameters (like neural implementation)
        initial_rc_en_raw = 0.5  # softplus(0.5) ≈ 0.97
        initial_rc_ee_raw = 0.5

        params = {
            'layers': layers,
            'rc_en_raw': initial_rc_en_raw,
            'rc_ee_raw': initial_rc_ee_raw
        }

        return params
    
    def _features(self, r1: jnp.ndarray, r2: jnp.ndarray, 
                 params: Dict[str, Any]) -> jnp.ndarray:
        """Extract features for polynomial evaluation. To be implemented by subclasses."""
        raise NotImplementedError("Subclasses must implement _features method")
    
    @partial(jax.jit, static_argnums=(0,))
    def _compute(self, r1, r2, params):
        """Compute polynomial Jastrow exponent."""
        x = self._features(r1, r2, params)
        return self._cp_forward(x, params)


class CPJastrowEN(PolynomialJastrow):
    """CP polynomial for electron-nuclear correlations.

    Features: distances from electron to all nuclei
    Mathematical form: u_EN(r1) = CP(|r1 - R_A|) for all nuclei A
    """

    def __init__(self, mol, use_kfac: bool = False, sawtooth_l_offset: int = 0,
                 sawtooth_u_offset: int = 0, **kwargs):
        super().__init__(mol, use_kfac=use_kfac, sawtooth_l_offset=sawtooth_l_offset,
                        sawtooth_u_offset=sawtooth_u_offset, **kwargs)
        self.nuclear_pos = jnp.array(mol.atom_coords())
        self.input_dim = len(self.nuclear_pos)
        
    def _features(self, r1: jnp.ndarray, r2: jnp.ndarray,
                 params: Dict[str, Any]) -> jnp.ndarray:
        """EN features: distances from electron to nuclei."""
        # Extract decay parameter (to match neural implementation)
        rc_en = nn.softplus(params['rc_en_raw'])

        r1n_dist = self._safe_norm(r1[None, :] - self.nuclear_pos)
        # Apply decay parameter (currently not used, matching neural implementation)
        r1n_feat = r1n_dist
        return r1n_feat
        
    @partial(jax.jit, static_argnums=(0,))
    def _compute(self, r1, r2, params):
        x = self._features(r1, r2, params)
        result = self._cp_forward(x, params)
        return result / (self.mol.nelectron - 1)  # Normalization
    
    def get_log_grads_r2(self, r1, r2, params):
        """EN correlation is symmetric in electron exchange."""
        return self.get_log_grads_r1(r2, r1, params)


class CPJastrowEE(PolynomialJastrow):
    """CP polynomial for electron-electron correlations.

    Features: inter-electron distance
    Mathematical form: u_EE(r1, r2) = CP(|r1 - r2|)
    """

    def __init__(self, mol, use_kfac: bool = False, sawtooth_l_offset: int = 0,
                 sawtooth_u_offset: int = 0, **kwargs):
        super().__init__(mol, use_kfac=use_kfac, sawtooth_l_offset=sawtooth_l_offset,
                        sawtooth_u_offset=sawtooth_u_offset, **kwargs)
        self.input_dim = 1
        
    def _features(self, r1: jnp.ndarray, r2: jnp.ndarray,
                 params: Dict[str, Any]) -> jnp.ndarray:
        """EE features: inter-electron distance."""
        # Extract decay parameter (to match neural implementation)
        rc_ee = nn.softplus(params['rc_ee_raw'])

        r12_dist = self._safe_norm(r1 - r2)
        # Apply decay parameter (currently not used, matching neural implementation)
        r12_feat = r12_dist
        return jnp.array([r12_feat])

    @partial(jax.jit, static_argnums=(0,))
    def _compute(self, r1, r2, params):
        x = self._features(r1, r2, params)
        return self._cp_forward(x, params)


class CPJastrowEEN(PolynomialJastrow):
    """CP polynomial for electron-electron-nuclear correlations.

    Features: r12 + symmetric EN distances (min/max)
    Mathematical form: u_EEN(r1, r2) = CP(|r1-r2|, min(|r1-R_A|, |r2-R_A|), max(|r1-R_A|, |r2-R_A|))
    """

    def __init__(self, mol, use_kfac: bool = False, sawtooth_l_offset: int = 0,
                 sawtooth_u_offset: int = 0, **kwargs):
        super().__init__(mol, use_kfac=use_kfac, sawtooth_l_offset=sawtooth_l_offset,
                        sawtooth_u_offset=sawtooth_u_offset, **kwargs)
        self.nuclear_pos = jnp.array(mol.atom_coords())
        self.input_dim = 1 + 2 * len(self.nuclear_pos)
        
    def _features(self, r1: jnp.ndarray, r2: jnp.ndarray,
                 params: Dict[str, Any]) -> jnp.ndarray:
        """EEN features: r12 + symmetric EN distances."""
        # Extract decay parameters (to match neural implementation)
        rc_ee = nn.softplus(params['rc_ee_raw'])
        rc_en = nn.softplus(params['rc_en_raw'])

        # Calculate distances (matching neural implementation exactly)
        r12_dist = self._safe_norm(r1 - r2)[None]  # Add singleton dimension
        r1n_dist = self._safe_norm(r1[None, :] - self.nuclear_pos)  # Shape: (N,)
        r2n_dist = self._safe_norm(r2[None, :] - self.nuclear_pos)  # Shape: (N,)

        # Create symmetric features using min/max (matching neural implementation)
        min_dist = jnp.minimum(r1n_dist, r2n_dist)
        max_dist = jnp.maximum(r1n_dist, r2n_dist)

        # Concatenate features with consistent dimensions (matching neural implementation)
        return jnp.concatenate([
            r12_dist,  # Shape: (1,)
            min_dist,  # Shape: (N,)
            max_dist,  # Shape: (N,)
        ], axis=0)

    @partial(jax.jit, static_argnums=(0,))
    def _compute(self, r1, r2, params):
        x = self._features(r1, r2, params)
        return self._cp_forward(x, params)
