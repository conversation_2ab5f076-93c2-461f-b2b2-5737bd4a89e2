import jax
import jax.numpy as jnp
import numpy as np
import netket as nk

from qpolynets.layers import PolyDenseEquivariantFFT


def test_poly_dense_equivariant_fft_shapes():
    g = nk.graph.Hypercube(length=4, n_dim=1, pbc=True)
    pt = g.space_group().product_table
    shape = tuple(g.extent)

    layer = PolyDenseEquivariantFFT(
        product_table=pt,
        shape=shape,
        features=8,
        degree=2,
    )

    # Create fake x in GCNN inner shape [batch, features, n_symm]
    x = jnp.ones((2, 4, len(np.asarray(g.space_group()))))
    params = layer.init(jax.random.PRNGKey(0), x)
    y = layer.apply(params, x)
    assert y.shape == (2, 8, x.shape[-1])

