#!/usr/bin/env python3
"""
Comprehensive example demonstrating all CP variants with KFAC support.
"""

import sys
import os
import jax
import jax.numpy as jnp
from jax import random

# Enable float64 precision
jax.config.update("jax_enable_x64", True)

# Add PyTC to path
pytc_path = os.path.abspath('../../repos/pytc')
if pytc_path not in sys.path:
    sys.path.insert(0, pytc_path)

# PySCF for quantum chemistry
from pyscf import gto, scf

# Import polynomial Jastrow components
from pytc.autodiff.jastrow.polynomial import CPJastrowEN, CPJastrowEE, CPJastrowEEN


def demonstrate_all_variants():
    """Demonstrate all 8 CP variants."""
    print("=== Demonstrating All CP Variants ===")
    
    mol = gto.M(atom='H 0 0 0; H 0 0 1.4', basis='sto-3g', unit='bohr', verbose=0)
    
    variants = [
        ('CP', 'Standard dense CP decomposition'),
        ('CP_sparse_LU', 'Alternating upper/lower triangular sparsity'),
        ('CP_sparse_degree', 'Degree-dependent sparsity patterns'),
        ('CP_sparse_sawtooth', 'Sawtooth L/U patterns (generalizes LU)'),
        ('CP_sparse_degree_LU', 'Combined degree + LU sparsity'),
        ('CP_sparse_degree_sawtooth', 'Combined degree + sawtooth (most flexible)'),
    ]
    
    for variant, description in variants:
        print(f"\n{variant}:")
        print(f"  Description: {description}")
        
        # Create component
        cp = CPJastrowEEN(mol, degree=4, rank=8, cp_variant=variant)
        
        # Show configuration
        print(f"  Configuration: degree={cp.degree}, rank={cp.rank}")
        print(f"  Input dimension: {cp.input_dim}")
        print(f"  KFAC enabled: {cp.use_kfac}")
        
        # Initialize and show sparsity
        key = random.PRNGKey(42)
        params = cp.init_params(key=key)
        
        if cp.masks is not None:
            sparsity = [jnp.sum(mask) for mask in cp.masks]
            total_params = cp.input_dim * cp.rank * cp.degree
            sparse_params = sum(sparsity)
            sparsity_ratio = sparse_params / total_params
            print(f"  Sparsity: {sparsity} ({sparsity_ratio:.1%} of dense)")
        else:
            print(f"  Sparsity: Dense (100%)")


def demonstrate_kfac_integration():
    """Demonstrate KFAC integration with all variants."""
    print("\n=== Demonstrating KFAC Integration ===")
    
    mol = gto.M(atom='H 0 0 0; H 0 0 1.4', basis='sto-3g', unit='bohr', verbose=0)
    
    # Test a few representative variants with KFAC
    test_variants = ['CP', 'CP_sparse_degree', 'CP_sparse_degree_sawtooth']
    
    for variant in test_variants:
        print(f"\nTesting {variant} with KFAC:")
        
        # Without KFAC
        cp_no_kfac = CPJastrowEEN(mol, degree=3, rank=6, cp_variant=variant, use_kfac=False)
        
        # With KFAC
        cp_kfac = CPJastrowEEN(mol, degree=3, rank=6, cp_variant=variant, use_kfac=True)
        
        # Test forward pass
        key = random.PRNGKey(123)
        r1, r2 = jnp.array([0.0, 0.0, 0.5]), jnp.array([0.0, 0.0, -0.5])
        
        params_no_kfac = cp_no_kfac.init_params(key=key)
        params_kfac = cp_kfac.init_params(key=key)
        
        val_no_kfac = cp_no_kfac._compute(r1, r2, params_no_kfac)
        val_kfac = cp_kfac._compute(r1, r2, params_kfac)
        
        print(f"  No KFAC: {val_no_kfac:.6f}")
        print(f"  With KFAC: {val_kfac:.6f}")
        print(f"  Values match: {jnp.allclose(val_no_kfac, val_kfac)}")


def demonstrate_sawtooth_offsets():
    """Demonstrate sawtooth offset configurations."""
    print("\n=== Demonstrating Sawtooth Offset Configurations ===")
    
    mol = gto.M(atom='H 0 0 0; H 0 0 1.4', basis='sto-3g', unit='bohr', verbose=0)
    
    offset_configs = [
        (0, 0, "Standard triangular patterns"),
        (-1, 1, "Shifted patterns (more sparse)"),
        (1, -1, "Opposite shift (less sparse)"),
        (0, 1, "Upper triangular only"),
        (-1, 0, "Lower triangular only"),
    ]
    
    for l_offset, u_offset, description in offset_configs:
        print(f"\nOffsets l={l_offset}, u={u_offset}: {description}")
        
        cp = CPJastrowEEN(mol, degree=3, rank=4, cp_variant='CP_sparse_sawtooth',
                         sawtooth_l_offset=l_offset, sawtooth_u_offset=u_offset)
        
        key = random.PRNGKey(456)
        params = cp.init_params(key=key)
        
        sparsity = [jnp.sum(mask) for mask in cp.masks]
        total_possible = cp.input_dim * cp.rank * cp.degree
        actual_sparse = sum(sparsity)
        
        print(f"  Sparsity: {sparsity}")
        print(f"  Total sparsity: {actual_sparse}/{total_possible} ({actual_sparse/total_possible:.1%})")


def demonstrate_full_ansatz_creation():
    """Demonstrate creating full ansatz with new variants."""
    print("\n=== Demonstrating Full Ansatz Creation ===")
    
    # Add qpolynets notebooks to path
    notebooks_path = os.path.abspath('qpolynets/notebooks')
    if notebooks_path not in sys.path:
        sys.path.insert(0, notebooks_path)
    
    try:
        from model_analysis import create_full_ansatz
        
        mol = gto.M(atom='H 0 0 0; H 0 0 1.4', basis='sto-3g', unit='bohr', verbose=0)
        mf = scf.RHF(mol)
        mf.kernel()
        
        # Test different configurations
        configs = [
            {
                'cp_variant': 'CP_sparse_degree_sawtooth',
                'use_kfac': True,
                'sawtooth_l_offset': -1,
                'sawtooth_u_offset': 1,
                'degree': 4,
                'rank': 8
            },
            {
                'cp_variant': 'CP_sparse_sawtooth',
                'use_kfac': False,
                'sawtooth_l_offset': 0,
                'sawtooth_u_offset': 0,
                'degree': 3,
                'rank': 6
            }
        ]
        
        for i, config in enumerate(configs, 1):
            print(f"\nConfiguration {i}:")
            for key, value in config.items():
                print(f"  {key}: {value}")
            
            ansatz, params, _, instances = create_full_ansatz(
                mol, mf, "polynomial", **config
            )
            
            print(f"  EN component: {instances['en'].__class__.__name__}")
            print(f"  EE component: {instances['ee'].__class__.__name__}")
            print(f"  EEN component: {instances['een'].__class__.__name__}")
            print(f"  KFAC enabled: {instances['een'].use_kfac}")
            print(f"  CP variant: {instances['een'].cp_variant}")
            
            # Test evaluation
            r1, r2 = jnp.array([0.0, 0.0, 0.5]), jnp.array([0.0, 0.0, -0.5])
            # Note: ansatz call signature may vary - just show successful creation
            print(f"  ✓ Ansatz created successfully")
            
    except ImportError:
        print("  model_analysis not available - showing manual creation:")
        
        # Manual creation
        mol = gto.M(atom='H 0 0 0; H 0 0 1.4', basis='sto-3g', unit='bohr', verbose=0)
        
        cp_en = CPJastrowEN(mol, degree=4, rank=8, cp_variant='CP_sparse_degree_sawtooth',
                           use_kfac=True, sawtooth_l_offset=-1, sawtooth_u_offset=1)
        cp_ee = CPJastrowEE(mol, degree=4, rank=8, cp_variant='CP_sparse_degree_sawtooth',
                           use_kfac=True, sawtooth_l_offset=-1, sawtooth_u_offset=1)
        cp_een = CPJastrowEEN(mol, degree=4, rank=8, cp_variant='CP_sparse_degree_sawtooth',
                             use_kfac=True, sawtooth_l_offset=-1, sawtooth_u_offset=1)
        
        print(f"  Created components with:")
        print(f"    Variant: {cp_een.cp_variant}")
        print(f"    KFAC: {cp_een.use_kfac}")
        print(f"    Offsets: l={cp_een.sawtooth_l_offset}, u={cp_een.sawtooth_u_offset}")


def demonstrate_performance_comparison():
    """Demonstrate performance characteristics of different variants."""
    print("\n=== Performance Characteristics ===")
    
    mol = gto.M(atom='H 0 0 0; H 0 0 1.4', basis='sto-3g', unit='bohr', verbose=0)
    
    # Compare parameter counts
    variants = ['CP', 'CP_sparse_LU', 'CP_sparse_degree', 'CP_sparse_degree_sawtooth']
    
    print("Parameter count comparison:")
    print("Variant".ljust(30) + "Parameters".ljust(15) + "Sparsity")
    print("-" * 60)
    
    for variant in variants:
        cp = CPJastrowEEN(mol, degree=4, rank=8, cp_variant=variant)
        key = random.PRNGKey(789)
        params = cp.init_params(key=key)
        
        if cp.masks is not None:
            sparse_params = sum(jnp.sum(mask) for mask in cp.masks)
            total_possible = cp.input_dim * cp.rank * cp.degree
            sparsity_ratio = sparse_params / total_possible
        else:
            sparse_params = cp.input_dim * cp.rank * cp.degree
            sparsity_ratio = 1.0
        
        print(f"{variant}".ljust(30) + f"{int(sparse_params)}".ljust(15) + f"{sparsity_ratio:.1%}")


def main():
    """Run all demonstrations."""
    print("Comprehensive CP Variant Demonstration")
    print("=" * 50)
    
    demonstrate_all_variants()
    demonstrate_kfac_integration()
    demonstrate_sawtooth_offsets()
    demonstrate_full_ansatz_creation()
    demonstrate_performance_comparison()
    
    print("\n🎉 All demonstrations completed!")
    print("\nKey Features Demonstrated:")
    print("  • 6 different CP sparsity variants")
    print("  • KFAC optimization support for all variants")
    print("  • Configurable sawtooth offset parameters")
    print("  • Full ansatz creation with new parameters")
    print("  • Performance and sparsity comparisons")
    print("  • Sawtooth patterns generalize LU patterns")
    print("\nReady for quantum chemistry applications!")


if __name__ == "__main__":
    main()
